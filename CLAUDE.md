# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## System Overview

This is a multi-strategy automated cryptocurrency trading system built around Binance futures/spot trading with real-time data processing and strategy execution capabilities.

## Architecture

### Strategy Modules
Each strategy follows a consistent microservice-like pattern:
- `/alpha/`, `/beta/`, `/grid/`, `/flash/`, `/oneper/`, `/trph/`, `/jason/`, `/wasee/` - Trading strategies
- Each strategy contains: `strategy_name.py` (main logic), `account_*.py` (account management), `minute_job.py`/`hourly_job.py` (periodic tasks), `util.py` (utilities), `stop_market.py` (risk management)

### Core Infrastructure
- `/public/` - Shared APIs, utilities, and common functions
- `/download/` - Market data ingestion from Binance
- `/backtest/`, `/grid_bt/`, `/grid_claude/` - Backtesting engines
- `/user_data/` - FreqTrade integration and user data storage
- `/notebook/` - Jupyter notebooks for analysis

### Key Components
- **FuturesApi.py**, **SpotApi.py**, **PortfolioApi.py** - Binance API wrappers
- **WebsocketApi.py** - Real-time data streams
- **constant.py** - Symbol lists and trading pair definitions
- **util.py** - Common utilities (notifications, time conversion, data validation)

## Technology Stack

### Core Dependencies
- **pandas**, **numpy** - Data processing
- **ccxt**, **python-binance** - Exchange APIs
- **requests** - HTTP client
- **matplotlib**, **seaborn**, **plotly** - Visualization

### Infrastructure
- **Redis** - Position tracking, market data caching, account balances
- **Kafka** - Inter-strategy communication, trade signals
- **WebSocket** - Real-time market data feeds
- **WeChat/DingTalk APIs** - Notification system

## Common Development Tasks

### Running Strategies
```bash
# Individual strategy execution
python alpha/alpha.py
python grid/trade_grid.py
python oneper/oneper.py

# Grid backtesting
cd grid_bt && python main.py
cd grid_claude && python main.py
```

### Data Download
```bash
# Historical data download
cd download && python download-kline.py
cd download && python download-aggTrade.py

# Fetch trading pairs
cd download && bash fetch-all-trading-pairs.sh
```

### Poker Utilities
```bash
# Texas Hold'em equity calculator
python poker.py
python interactive_poker.py
```

### Backtesting
```bash
# Simple grid backtesting
cd grid_bt && python main.py

# Comprehensive grid testing with optimization
cd grid_claude && python main.py --analysis
```

## Key Patterns

### Strategy Implementation
All strategies follow this execution pattern:
1. **Signal Generation** → 2. **Kafka Message** → 3. **Trade Execution** → 4. **Position Tracking**

### Configuration
- Strategy-specific configuration in each module's `util.py`
- Global symbol lists in `/public/constant.py`
- JSON configuration files for backtesting (e.g., `grid_claude/config.json`)

### Risk Management
- Position size limits and notional checks
- Stop-loss automation in `stop_market.py` files
- Real-time balance monitoring
- Emergency shutdown capabilities

### Notification System
- Trade confirmations via WeChat/DingTalk webhooks
- Balance alerts and P&L reporting
- Error notifications with tokens defined in `util.py` files

## Data Flow

```
Market Data (Binance WebSocket/REST) → Redis Cache → Strategy Signal Generation → Kafka → Trade Execution → Position Management → Monitoring & Notifications
```

## Testing

### Backtesting
- Grid strategies have comprehensive backtesting in `/grid_bt/` and `/grid_claude/`
- Historical data simulation with commission and slippage modeling
- Parameter optimization and sensitivity analysis

### Poker Simulation
- Monte Carlo simulation for Texas Hold'em equity calculation
- Performance testing with 7000+ simulations per second
- Interactive and batch calculation modes

## Important Notes

### Symbol Management
- Trading symbols are defined in `/public/constant.py`
- Separate lists for spot/futures trading
- Invalid/delisted symbols are tracked

### API Keys & Security
- API credentials stored in strategy-specific `util.py` files
- Webhook tokens for notifications
- Redis passwords and connection details

### Multi-Account Support
- `wasee/` strategy supports spot, margin, and futures accounts
- Account-specific API wrappers and balance management
- Cross-account position tracking


## git commit rule
### never mention claude, AI or assistant in commit message