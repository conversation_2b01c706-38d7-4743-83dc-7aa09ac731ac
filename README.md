# 德州扑克胜率计算器

一个高性能的德州扑克胜率计算器，使用蒙特卡洛模拟方法计算两手牌的胜率。

## 功能特性

- ✅ 计算两组手牌的胜率（无翻牌情况）
- ✅ 计算两组手牌在给定翻牌情况下的胜率
- ✅ 支持所有德州扑克手牌类型评估
- ✅ 高性能优化，每秒可模拟7000+次
- ✅ 简单易用的API接口

## 快速开始

### 基本用法

```python
from poker import PokerCalculator

# 创建计算器
calculator = PokerCalculator()

# 计算无翻牌胜率
result = calculator.calculate_equity("As Kh", "Qd Jc", simulations=10000)
print(f"手牌1胜率: {result['hand1']['percentage']}")
print(f"手牌2胜率: {result['hand2']['percentage']}")

# 计算有翻牌胜率
result = calculator.calculate_equity("As Kh", "Qd Jc", "Ah Kd 5s", simulations=10000)
print(f"手牌1胜率: {result['hand1']['percentage']}")
print(f"手牌2胜率: {result['hand2']['percentage']}")
```

### 牌的表示格式

- **花色**：S(♠), H(♥), D(♦), C(♣)
- **点数**：2-9, T(10), J, Q, K, A
- **示例**：
  - `As` = A♠ (黑桃A)
  - `Kh` = K♥ (红桃K)
  - `Tc` = T♣ (梅花10)
  - `2d` = 2♦ (方块2)

### 输入格式

```python
# 手牌：用空格分隔两张牌
hand1 = "As Kh"  # A♠ K♥
hand2 = "Qd Jc"  # Q♦ J♣

# 翻牌：用空格分隔，最多5张
board = "Ah Kd 5s"  # A♥ K♦ 5♠
board = "Ah Kd 5s 7h 2c"  # 完整的5张公共牌
```

## 使用示例

### 示例1：经典对决 - AK vs QJ

```python
calculator = PokerCalculator()
result = calculator.calculate_equity("As Kh", "Qd Jc", simulations=50000)

# 输出结果
print(f"AK胜率: {result['hand1']['percentage']}")  # 约64%
print(f"QJ胜率: {result['hand2']['percentage']}")  # 约36%
```

### 示例2：口袋对子 vs 高牌

```python
result = calculator.calculate_equity("As Ad", "Kh Qc", simulations=10000)
print(f"AA胜率: {result['hand1']['percentage']}")  # 约87%
print(f"KQ胜率: {result['hand2']['percentage']}")  # 约13%
```

### 示例3：翻牌后的胜率

```python
# 翻牌前 AK vs QJ
result_preflop = calculator.calculate_equity("As Kh", "Qd Jc")

# 翻牌后 A♥ K♦ 5♠
result_flop = calculator.calculate_equity("As Kh", "Qd Jc", "Ah Kd 5s")

print(f"翻牌前 AK胜率: {result_preflop['hand1']['percentage']}")  # 约64%
print(f"翻牌后 AK胜率: {result_flop['hand1']['percentage']}")     # 约84%
```

## API 参考

### PokerCalculator.calculate_equity()

```python
def calculate_equity(self, hand1: str, hand2: str, board: str = "", simulations: int = 10000) -> dict:
```

**参数：**
- `hand1` (str): 第一手牌，如 "As Kh"
- `hand2` (str): 第二手牌，如 "Qd Jc"  
- `board` (str, 可选): 公共牌，如 "Ah Kd 5s"
- `simulations` (int, 可选): 模拟次数，默认10000

**返回值：**
```python
{
    'hand1': {
        'cards': 'As Kh',
        'wins': 6416,
        'equity': 0.6416,
        'percentage': '64.16%'
    },
    'hand2': {
        'cards': 'Qd Jc', 
        'wins': 3584,
        'equity': 0.3584,
        'percentage': '35.84%'
    },
    'ties': 38,
    'tie_percentage': '0.38%',
    'simulations': 10000,
    'board': '无翻牌'
}
```

## 性能

- **速度**：每秒约7000次模拟
- **精度**：10000次模拟通常足够准确
- **建议**：
  - 快速测试：1000-5000次模拟
  - 准确计算：10000-50000次模拟
  - 高精度：100000+次模拟

## 运行示例

```bash
python poker.py
```

这将运行内置的示例，展示各种使用场景和性能测试。

## 技术实现

- 使用蒙特卡洛模拟方法
- 优化的手牌评估算法
- 预计算查找表提升性能
- 高效的牌堆管理

## 支持的手牌类型

1. 同花顺 (Straight Flush)
2. 四条 (Four of a Kind)
3. 葫芦 (Full House)
4. 同花 (Flush)
5. 顺子 (Straight)
6. 三条 (Three of a Kind)
7. 两对 (Two Pair)
8. 一对 (One Pair)
9. 高牌 (High Card)

## 注意事项

- 输入的牌不能重复
- 每手牌必须是2张
- 公共牌最多5张
- 牌的表示区分大小写（建议使用大写） 