# -*- coding: utf-8 -*
import time, json
import redis
import pandas as pd
import numpy as np
from util import *
import os
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import matplotlib.pyplot as plt
import logging
from decimal import Decimal

import sys
sys.path.append("/root/workspace/trade/public")
from FuturesApi import FuturesApi
from constant import *

logger = logging.getLogger("SmallGoal-StopMarket")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.stopMarket')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
fapi = FuturesApi(api_key, api_secret)
notional = 0
global info

def get_ask_bid(symbol):
    depth = json.loads(r.get('futures_' + symbol + '_depthUpdate'))
    gap = time.time() - depth['E']/1000
    if gap < 30:
        bid = depth['b'][0][0]
        ask = depth['a'][0][0]         
    else:
        print(f'{symbol} gap more than 30s')
        depth = fapi.get_depth(symbol, 5)
        bid = float(depth['bids'][0][0])
        ask = float(depth['asks'][0][0])
    return bid

def adjust_price(symbol, price):
    info = json.loads(r.get('futures_' + symbol + '_info'))
    filters = info['filters']
    for flt in filters:
        if flt['filterType'] == 'PRICE_FILTER':
            tickSize = float(flt['tickSize'])
            count = int(price / tickSize)
            return float(Decimal(str(count)) * Decimal(str(tickSize)))

def gererate_orderId(strategy, symbol):
    t = int(time.time()*1000) / 1000
    local_time = time.localtime(time.time())
    tstr = time.strftime("%m%d-%H:%M:%S", local_time) + str(t)[-4:]
    return f'{strategy}_{symbol[:-4]}_{tstr}'

def run():
    global info
    info = fapi.get_account()
    try:
        balance = float(info['totalWalletBalance'])
        r.set(group + '_info', json.dumps(info))
    except Exception as e:
        info = json.loads(r.get(group + '_info'))
    positions = info['positions']
    gap = 0.3
    #futures_trade_symbols = ['ADAUSDT','DYDXUSDT']

    for p in positions:
        symbol = p['symbol']
        notional = float(p['notional'])
        if symbol in futures_trade_symbols and notional != 0:
            fapi.delete_allOpenOrders(symbol)      
            price = get_ask_bid(symbol)
            if notional > 0:
                stopPrice = price * (1-gap)
                stopPrice = adjust_price(symbol, stopPrice)
                orderId = gererate_orderId('SELL', symbol)
                msg = fapi.stop_market(symbol, 'SELL', stopPrice, newClientOrderId=orderId)
            if notional < 0:
                stopPrice = price * (1+gap)
                stopPrice = adjust_price(symbol, stopPrice)
                orderId = gererate_orderId('BUY', symbol)
                msg = fapi.stop_market(symbol, 'BUY', stopPrice, newClientOrderId=orderId)
            if 'code' in msg:
                logger.info(symbol + json.dumps(msg) + str(stopPrice))
                weixin_info(symbol + json.dumps(msg), warning_token)
            else:
                logger.info(json.dumps(msg))


def main():
    local_time = time.localtime(time.time())
    hr = time.strftime("%H", local_time)
    minute = int(time.strftime("%M", local_time))
    if hr == '00' and minute <= 3:
        time.sleep(240)
    
    run()

if __name__ == '__main__':
    main()
