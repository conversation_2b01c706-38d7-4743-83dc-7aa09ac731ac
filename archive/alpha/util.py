# -*- coding: utf-8 -*

import requests, json
import time
import base64
from hashlib import md5

group = 'alpha'

api_key = 'pB6Tde0JOt02NUff7P1frUxk1KyI2wZ3NQSDR94JXBnaRJ7Xaz2hKM8EUjS8yB0w'
api_secret = 'axagBrjwBjgTxFnShroZJpD6eHeW2KmVLeojbQWbTjGmzR0Ev9cymBbKS36JPSSM'
redis_pw = 'peng1234'
recv_window = 3000

weixin_token = ''
account_token = 'bb0f3569-b783-4048-9997-a26560b34f30' # 账户通知
order_token = '7def2c6e-94f9-4584-8a86-81e31eefe7b7'  # 订单详情
warning_token='a25c36d0-00ce-4159-a69d-ce37056914e3'  #报警通知
order_merge_token = 'ff9f0b33-2a92-437c-a964-f498b140c3e7'  # 订单汇总
lever_token = ''
profit_token = '690f3bbd-e632-4122-8608-8c1ff1fc15b8'  # 盈亏通知
balance_token = '15e8bc2c-4a57-416e-8ba0-f34a3b3580e9' # 余额播报
zhangdie_token = 'edca7081-21f7-4a1b-9476-756a4c3284f6'  # 异常涨跌
wave_token = ''


def _msg(text):
    json_text = {
        "msgtype": "text",
        "at": {
            "atMobiles": ["11111"],
            "isAtAll": False
        },
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_msg(text):
    json_text = {
        "msgtype": "text",
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_img(base64_data, md5_data):
    json_text = {
        "msgtype": "image",
        "image": {
            "base64": str(base64_data,'utf-8'),
            "md5": md5_data
        }
    }
    return json_text

def dingding_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = "https://oapi.dingtalk.com/robot/send?access_token=%s" % token
    json_text = _msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_img(path, token):
    hash = md5()
    img = open(path, 'rb')
    data = img.read()
    hash.update(data)
    base64_data = base64.b64encode(data)
    md5_data = hash.hexdigest()
    img.close()
    
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_img(base64_data, md5_data)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def ts2date(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%Y-%m-%d", local_time)
    return z

def ts2idx(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%H%M", local_time)
    return int(z)

def check_klines(klines):
        for i in range(len(klines)-1):
            if klines[i]['t'] - klines[i+1]['t'] != 60 * 1000:
                print(klines[i], klines[i+1])
                return False
        return True


