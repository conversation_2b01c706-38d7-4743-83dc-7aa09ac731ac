# -*- coding: utf-8 -*
import time, json
import redis
import pandas as pd
import numpy as np
import math
from util import *

import sys
sys.path.append("/root/workspace/trade/public")
from constant import *
from FuturesApi import FuturesApi

r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
fapi = FuturesApi(api_key, api_secret)
notional = 0

def get_volatility():
    ret = 0
    weights = json.loads(r.get('trph'))
    weights = {i['symbol']:i['weight'] for i in weights}
    for s in futures_trade_symbols:
        klines = r.lrange(f'futures_{s}_1m', 0, 60*24)
        klines = [json.loads(k) for k in klines]
        rates = [(k['c']-k['o'])/k['o'] for k in klines]
        ret += np.std(rates) * weights.get(s, 0)
    return round(ret * math.sqrt(1440), 4)
    
def account_info():
    info = fapi.get_account()
    try:
        balance = float(info['totalWalletBalance'])
    except Exception as e:
        print(e)
        time.sleep(30)
        info = fapi.get_account()
    balance = float(info['totalWalletBalance'])
    profit = round(float(info['totalUnrealizedProfit']), 2)
    balance = int(balance + profit)
    local_time = time.localtime(time.time())
    z = time.strftime("%H", local_time)
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    global notional
    notional = 1e-8
    ntl_ = 0
    num = 0
    positions = info['positions']
    group_positions = {}
    syml_info = []
    for p in positions:
        symbol = p['symbol'][0:-4]
        ntl = float(p['notional'])
        group_positions[p['symbol']] = ntl
        notional += abs(ntl)
        ntl_ += ntl
        if ntl != 0:
            num += 1
            profit = float(p['unrealizedProfit'])
            entryPrice = float(p['entryPrice'])
            positionAmt = float(p['positionAmt'])
            rate = round(profit / (entryPrice * abs(positionAmt)) * 100, 2)
            # symbol, 目前持仓，收益，收益率
            syml_info.append(f'{symbol}: {round(ntl,2)}, {round(profit,2)}, {rate}%')
    # 目前持仓 {'symbol':ntl, ...}
    r.set(group + '_positions', json.dumps(group_positions))
    syml_info.sort()
    syml_info.append('通知时间：' + tstr)
    text = '交易对 持仓通知：\nsymbol,持仓,收益,收益率\n' + '\n'.join(syml_info)
    # 下发持仓和收益通知
    weixin_info(text, order_merge_token)

    ntl_ = round(ntl_, 2)
    ntl_p = round(ntl_ / notional * 100, 2)
    lever = round(notional / balance, 2)
    rates = []
    for s in futures_trade_symbols:
        key = f'futures_{s}_1m'
        first = json.loads(r.lindex(key, 60))
        last = json.loads(r.lindex(key, 0))
        rates.append(last['c'] / first['c'] - 1)
    rate_ = round(np.mean(rates) * 100, 2)
    
    if z == '00':
        balance_day = r.get(group + '_balance_day')
        if balance_day is None:
            pre = 1
        else:
            pre = round(float(balance_day), 2)
        r.set(group + '_balance_day', balance)
        r.set(group + '_balance_hour', balance)
        diff = round(balance - pre, 2)
        ratio = round(diff / pre * 100, 2)
        text = '账号通知：\n当前余额：{cur}\n昨天余额：{pre}\n变化金额：{diff}\n变化幅度：{ratio}%\n通知时间：{tstr}'.format(
            cur=balance, pre=pre, diff=diff, ratio=ratio, tstr=tstr
        )
        weixin_info(text, account_token)
    balance_hour = r.get(group + '_balance_hour')
    if balance_hour is None:
        pre = 1
    else:
        pre = int(float(balance_hour))
    r.set(group + '_balance_hour', balance)
    #volatility = get_volatility()
    diff = balance - pre
    ratio = round(diff / pre * 100, 2)
    base = int(r.get(f'{group}_base'))
    profit = balance - base
    profit_ratio = profit / base * 100
    text = ['账号通知：']
    text.append(f'投资总额：{base}')
    text.append(f'当前余额：{balance}')
    text.append(f'前时余额：{pre}')
    text.append(f'变化金额：{diff}')
    text.append(f'变化幅度：{ratio}%')
    text.append(f'总收益额：{profit}')
    text.append(f'总收益率：{round(profit_ratio, 2)}%')
    text.append(f'持仓数量：{num}')
    text.append(f'持仓总额：{int(notional)}')
    #text.append(f'敞口总额：{int(ntl_)}')
    #text.append(f'敞口占比：{ntl_p}%')
    #text.append(f'杠杆比例：{lever}')
    #text.append(f'大盘指数：{rate_}%')
    #text.append(f'波动率：{volatility}')
    text.append(f'通知时间：{tstr}')
    text = '\n'.join(text)
    weixin_info(text, account_token)


def orders_stat():
    if (int(time.time()) + 3600 * 8) % (3600 * 24) < 60 * 5:
        time.sleep(60 * 5)
    if not r.exists(group + '_order_trade_list'):
        text = '交易异常通知：本小时没有成交'
        weixin_info(text, warning_token)
        return
    orders = r.lrange(group + '_order_trade_list',0, -1)
    orders = [json.loads(i) for i in orders]
    amount = 0
    fee = 0
    for i in orders:
        t = i.pop('o', None)
        i = {**i, **t}
        id = i['i']
        fee += float(i['n'])
        amount += float(i['l']) * float(i['ap'])
    ratio = round(fee/amount * 100, 3)
    local_time = time.localtime(time.time())
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    turnover = round(amount/notional*100, 2)
    text = f'小时订单通知：\n成交总额：{round(amount,2)}\n手续费用：{round(fee,4)}\n手续费率：{ratio}%\n换手率：{turnover}%\n通知时间：{tstr}'
    weixin_info(text, profit_token)
    r.delete(group + '_order_trade_list')

def orders_stat_day():
    local_time = time.localtime(time.time())
    z = time.strftime("%H", local_time)
    if z != '00':
        return
    if not r.exists(group + '_order_trade_day_list'):
        return

    orders = r.lrange(group + '_order_trade_day_list',0, -1)
    orders = [json.loads(i) for i in orders]
    amount = 0
    fee = 0
    for i in orders:
        t = i.pop('o', None)
        i = {**i, **t}
        id = i['i']
        fee += float(i['n'])
        amount += float(i['l']) * float(i['ap'])
    ratio = round(fee/amount * 100, 3)
    local_time = time.localtime(time.time())
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    turnover = round(amount/notional*100, 2)
    text = f'每天订单通知：\n成交总额：{round(amount,2)}\n手续费用：{round(fee,4)}\n手续费率：{ratio}%\n换手率：{turnover}%\n通知时间：{tstr}'
    weixin_info(text, profit_token)
    r.delete(group + '_order_trade_day_list')

def set_lever():
    local_time = time.localtime(time.time())
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    balance = float(r.get('balance_hour'))
    LNtl = json.loads(r.get('levers_notional'))
    levers = json.loads(r.get(f'{group}_levers'))
    weights = json.loads(r.get('trph'))
    base = 10000
    per = 0.8
    le = float(r.get(f'{group}_lever'))
    trph_positions = json.loads(r.get(f'{group}_positions'))
    for i in weights:
        symbol = i['symbol']
        if symbol in ['BTCUSDT', 'ETHUSDT']:
            continue
        no10 = LNtl[symbol]
        lever = levers[symbol]
        position = abs(balance * i['weight']) * le
        #position = abs(trph_positions[symbol])
        new_lever = 10
        if no10 == base * 10:
            if base * 2.5 * per > position:
                new_lever = 20
        if no10 == base * 25:
            if base * 5 * per > position:
                new_lever = 50
            elif base * 15 * per > position:
                new_lever = 20
        if no10 == base * 100:
            if base * 5 * per > position:
                new_lever = 50
            elif base * 25 * 0.8 > position:
                new_lever = 25
        if symbol in ['DYDXUSDT','FTMUSDT']:
            new_lever = min(new_lever, 25)
        if symbol == 'TRXUSDT':
            new_lever = min(new_lever, 20)
        if new_lever != lever:
            x = fapi.set_leverage(symbol, new_lever)
            if 'maxNotionalValue' not in x:
                text = '报警通知：' + symbol + str(new_lever) + json.dumps(x)
                weixin_info(text, lever_token)
                continue
            text = ['杠杆变更通知：']
            text.append('交易对：' + x['symbol'])
            text.append('杠杆：' + str(x['leverage']))
            text.append('额度：' + str(int(x['maxNotionalValue'])/base) + 'w' )
            text.append('通知时间：' + tstr)
            weixin_info('\n'.join(text), lever_token)
            levers[symbol] = new_lever
    r.set(f'{group}_levers', json.dumps(levers))



def main():
    account_info()
    #set_lever()
    time.sleep(180)
    orders_stat()
    orders_stat_day()


if __name__ == '__main__':
    main()