# -*- coding: utf-8 -*
import time, json
import redis
import pandas as pd
import numpy as np
from util import *
import os
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
#from util4hyt import df_to_hytstyle2
import matplotlib.pyplot as plt

import sys
sys.path.append("/root/workspace/trade/public")
from FuturesApi import FuturesApi
from constant import *

r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
fapi = FuturesApi(api_key, api_secret)
notional = 0
global info

'''
"positions": [  // 头寸，将返回所有市场symbol。
    //根据用户持仓模式展示持仓方向，即单向模式下只返回BOTH持仓情况，双向模式下只返回 LONG 和 SHORT 持仓情况
    {
        "symbol": "BTCUSDT",  // 交易对
        "initialMargin": "0",   // 当前所需起始保证金(基于最新标记价格)
        "maintMargin": "0", //维持保证金
        "unrealizedProfit": "0.00000000",  // 持仓未实现盈亏
        "positionInitialMargin": "0",  // 持仓所需起始保证金(基于最新标记价格)
        "openOrderInitialMargin": "0",  // 当前挂单所需起始保证金(基于最新标记价格)
        "leverage": "100",  // 杠杆倍率
        "isolated": true,  // 是否是逐仓模式
        "entryPrice": "0.00000",  // 持仓成本价
        "maxNotional": "250000",  // 当前杠杆下用户可用的最大名义价值
        "bidNotional": "0",  // 买单净值，忽略
        "askNotional": "0",  // 卖单净值，忽略
        "positionSide": "BOTH",  // 持仓方向
        "positionAmt": "0",      // 持仓数量
        "updateTime": 0         // 更新时间 
    }
]
'''
def get_ask_bid(symbol):
    depth = json.loads(r.get('futures_' + symbol + '_depthUpdate'))
    gap = time.time() - depth['E']/1000
    if gap < 6:
        bid = depth['b'][0][0]
        ask = depth['a'][0][0]         
    else:
        depth = fapi.get_depth(symbol, 5)
        bid = float(depth['bids'][0][0])
        ask = float(depth['asks'][0][0])
    return bid

def df_to_hytstyle2(df, day_date):
    names = {'s':'symbol', 'o':'open', 'h':'high','l':'low','c':'close', 'v':'volume'}
    df.rename(columns=names, inplace = True)
    df['date'] = df['T'].apply(lambda x: ts2date(x))
    df['idx'] = df['T'].apply(lambda x: ts2idx(x))
    df['symbol'] = df['symbol'].apply(lambda x: x[0:-4])
    df['date'] = pd.to_datetime(df['date'])
    df = df[df['date'] == day_date]
    df = df[['date','idx','symbol','open','high','low','close','volume','q','n','V','Q']]
    df = df.set_index(['date','idx','symbol'])
    df.sort_index(inplace=True)
    return df

def monitor_account():
    global info
    info = fapi.get_account()
    try:
        balance = float(info['totalWalletBalance'])
        r.set(group + '_info', json.dumps(info))
    except Exception as e:
        info = json.loads(r.get(group + '_info'))
    balance = float(info['totalWalletBalance'])
    profit = round(float(info['totalUnrealizedProfit']), 2)
    balance = int(balance + profit)
    local_time = time.localtime(int(time.time()/60)*60)
    minute = time.strftime("%M", local_time)
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)

    positions_5min = r.lindex(group + '_positions_5min', 0)
    if positions_5min is None:
       pre_positions = {}
    else: 
        pre_positions = json.loads(positions_5min)
    pre_json = {x['symbol']:x for x in pre_positions}
    positions = info['positions']

    profit_notice = []
    price_notice = []
    total_profit = 0
    group_positions = []
    for p in positions:
        symbol = p['symbol']
        if symbol in futures_trade_symbols:
            pre = pre_json.get(symbol, None)
            pt = {}
            pt['symbol'] = symbol
            pt['ntl'] = float(p['notional'])
            pt['profit'] = float(p['unrealizedProfit'])
            pt['entryPrice'] = float(p['entryPrice'])
            pt['amt'] = float(p['positionAmt'])
            pt['price'] = get_ask_bid(symbol)
            if pre is None:
                group_positions.append(pt)
                continue
            pt['ntl_diff'] = int(pt['ntl'] - pre['ntl'])
            pt['profit_diff'] = int(pt['profit'] - pre['profit'])
            pt['profit_rate'] = round(pt['profit_diff']/abs(pt['ntl']+1e-8), 4)
            pt['price_diff'] = round(pt['price'] / pre['price'] - 1, 4)
            pt['time'] = tstr
            total_profit += pt['profit_diff']
            if abs(pt['profit_diff']) > 400:
                profit_notice.append('{}, {}, {}, {}%'.format(symbol[0:-4], int(pt['ntl']), pt['profit_diff'], round(pt['profit_rate']*100, 2)))

            if abs(pt['price_diff']) > 0.02:
                price_notice.append('{}, {}, {}, {}%'.format(symbol[0:-4],pre['price'],pt['price'],round(pt['price_diff']*100, 2)))
            group_positions.append(pt)
    r.lpush(group + '_positions_5min', json.dumps(group_positions))
    
    text = []
    balance_5min = r.lindex(group + '_balance_5min', 0)
    if balance_5min is None:
        pre_balance = 0
    else:
        pre_balance = json.loads(balance_5min)['balance']
    diff = balance - pre_balance
    text.append('现在余额：{}'.format(balance))
    text.append('之前余额：{}'.format(pre_balance))
    text.append('盈亏波动：{}'.format(diff))
    text.append('通知时间：{}'.format(tstr))
    text_5min = text

    text = ['5分钟-波动告警:']
    if abs(diff) > 800:
        text.append('----------------------')
        text.append('现在余额：{}'.format(balance))
        text.append('之前余额：{}'.format(pre_balance))
        text.append('盈亏波动：{}'.format(diff))
    if len(profit_notice) > 0:
        text.append('----------------------')
        text.append('持仓波动：')
        text.append('交易对, 持仓, 振幅, 波动率')
        text += profit_notice
    if len(price_notice) > 0:
        text.append('----------------------')
        text.append('价格波动：')
        text.append('交易对,前价格,现价格,波动率')
        text += price_notice
    if len(text) > 1:
        text.append('----------------------')
        text.append('通知时间：{}'.format(tstr))
        weixin_info('\n'.join(text), zhangdie_token)

    r.lpush(group + '_balance_5min', json.dumps({'balance':balance, 'diff':diff, 'time':tstr}))

    history = r.lrange(group + '_balance_5min', 0, -1)[::-1][-288:]
    history = [json.loads(i) for i in history]
    df = pd.json_normalize(history)
    plt.figure(figsize=(15, 6), dpi=60)
    plt.plot(df.index, df['balance']/1000, label='balance')
    plt.title(tstr)
    plt.legend()
    plt.grid()
    path = f'/root/data/figs/{group}.jpg'
    plt.savefig(path, bbox_inches='tight')
    weixin_img(path, balance_token)
    weixin_info('\n'.join(text_5min), balance_token)


def account_info():
    global info
    #info = fapi.get_account()
    balance = float(info['totalWalletBalance'])
    profit = round(float(info['totalUnrealizedProfit']), 2)
    balance = int(balance + profit)
    local_time = time.localtime(int(time.time()/60)*60)
    minute = time.strftime("%M", local_time)
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    positions_half_hour = r.lindex(group + '_positions_half_hour', 0)
    if positions_half_hour is None:
        pre_positions = {}
    else:
        pre_positions = json.loads(positions_half_hour)
    pre_json = {x['symbol']:x for x in pre_positions}
    positions = info['positions']
    group_positions = []
    for p in positions:
        symbol = p['symbol']
        if symbol in futures_trade_symbols:
            pre = pre_json.get(symbol, {})
            pt = {}
            pt['symbol'] = symbol
            pt['ntl'] = float(p['notional'])
            pt['profit'] = float(p['unrealizedProfit'])
            pt['entryPrice'] = float(p['entryPrice'])
            pt['amt'] = float(p['positionAmt'])
            pt['price'] = get_ask_bid(symbol)
            pt['ntl_diff'] = pt['ntl'] - pre.get('ntl', 0)
            pt['profit_diff'] = pt['profit'] - pre.get('profit', 0)
            pt['price_diff'] = round(pt['price'] / pre.get('price',1) - 1, 4)
            pt['time'] = tstr
            group_positions.append(pt)
    r.lpush(f'{group}_positions_half_hour', json.dumps(group_positions))
    if minute == '00':
        r.lpush(group + '_positions_hour', json.dumps(group_positions))
        r.lpush(group + '_info_list', json.dumps(info))
        r.ltrim(group + '_info_list', 0, 24 * 10)
        r.ltrim(group + '_positions_5min', 0, 12 * 24 * 7)
        r.ltrim(group + '_positions_half_hour', 0, 2 * 24 * 30)
        r.ltrim(group + '_positions_hour', 0, 24 * 7)
    
    group_positions_sort = sorted(group_positions, key=lambda x:x['profit_diff'])
    win_n = 0
    win_q = 0
    loss_q = 0
    for p in group_positions_sort:
        win_q += max(0, p['profit_diff'])
        loss_q += min(0, p['profit_diff'])
        if p['profit_diff'] > 0:
            win_n += 1

    pre_balance = json.loads(r.lindex(group + '_balance_5min', 6))['balance']
    text = ['半小时-盈亏通知：']
    text.append('当前余额：{}'.format(balance))
    text.append('之前余额：{}'.format(pre_balance))
    text.append('总体盈亏：{}'.format(balance - pre_balance))
    text.append('亏损总额：{}'.format(int(abs(loss_q))))
    text.append('盈利总额：{}'.format(int(win_q)))
    text.append('盈利数量：{}'.format(win_n))
    text.append('盈利数率：{}%'.format(round(win_n/120*100, 2)))
    text.append('------------------------')
    
    text.append('交易对,持仓,盈亏,波动率')
    for p in group_positions_sort[0:10]:
        text.append('{}, {}, {}, {}%'.format(p['symbol'][0:-4], int(p['ntl']), int(p['profit_diff']), round(p['profit_diff']/abs(p['ntl']+1e-8) * 100, 2)))
    text.append('------------------------')

    for p in group_positions_sort[::-1][0:10]:
        text.append('{}, {}, {}, {}%'.format(p['symbol'][0:-4], int(p['ntl']), int(p['profit_diff']), round(p['profit_diff']/abs(p['ntl']+1e-8) * 100, 2)))
    text.append('------------------------')
    text.append('时间：' + tstr)
    weixin_info('\n'.join(text), profit_token)
    if abs(balance - pre_balance) > 5000:
        weixin_info('\n'.join(text), warning_token)
    

def group_history():
    local_time = time.localtime(time.time())
    z = time.strftime("%H", local_time)
    tstr = time.strftime("%Y-%m-%d %H:%M", local_time)
    global info
    #info = fapi.get_account()
    balance = float(info['totalWalletBalance'])
    profit = round(float(info['totalUnrealizedProfit']), 2)
    balance = round(balance + profit, 2)
    record = {}
    record_history = r.lindex(group + '_record_history', 0)
    if record_history is None:
        pre_record = {'balance':1, 'btc':1}
    else:
        pre_record = json.loads(record_history)
    record['balance'] = balance
    record['balance_diff'] = round(balance - pre_record['balance'], 2)
    record['balance_rate'] = round(balance / pre_record['balance'] - 1, 5)

    btc = json.loads(r.lindex(f'futures_BTCUSDT_1m', 0))
    record['btc'] = btc['c']
    record['btc_rate'] = round(btc['c'] / pre_record['btc'] - 1, 5)

    rates = []
    for s in futures_trade_symbols:
        key = f'futures_{s}_1m'
        first = json.loads(r.lindex(key, 60))
        last = json.loads(r.lindex(key, 0))
        rates.append(last['c'] / first['c'] - 1)
    record['market_rate'] = round(np.mean(rates), 5)

    record['tstr'] = tstr
    r.lpush(group + '_record_history', json.dumps(record))


def monitor_ban_symbols():
    ban_symbols = json.loads(r.get('ban_symbols'))
    if len(set(ban_symbols)) >= 20:
        r.set(f'{group}_lever', 0)
        producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        producer.send('trade', key='trph', value=r.get('trph'))
        weixin_info('通知：开始平仓！', warning_token)


def main():
    local_time = time.localtime(time.time())
    hr = time.strftime("%H", local_time)
    minute = int(time.strftime("%M", local_time))
    monitor_ban_symbols()
    if minute % 5 == 0:
        monitor_account()

    if minute in [0, 30]:
        account_info()
    time.sleep(10)
    if minute in [0, 30]:
        group_history()

if __name__ == '__main__':
    main()
