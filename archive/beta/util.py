# -*- coding: utf-8 -*

import requests, json
import time
import base64
from hashlib import md5

group = 'beta'

api_key = 'B8GlvAT5uOwovmaaL2lwWNLKRlzLznIwB8OsFBlSr0JRuGv3VldGjO0j6JOKdLm3'
api_secret = 'GAMqz5tJEWQ3ZGUBaniAU8u3o2SLaW11ZoXF7qM8wNFAYsIybzlgwE4zI5eGlJro'
redis_pw = 'peng1234'
recv_window = 3000

weixin_token = ''
account_token = '109b0138-ef1c-49ce-b710-41e275563b34' # 账户通知
order_token = 'd941498f-9506-4b18-9de9-40dda12b0230'  # 订单详情
warning_token='cfdacca0-8677-42fd-951b-5edd85c74667'  #报警通知
order_merge_token = 'd9cc9dd2-3567-41b4-88c7-e9cec205eee9'  # 订单汇总
lever_token = ''
profit_token = '2ebda110-0bc9-45ee-904b-a20ce852cd80'  # 盈亏通知
balance_token = 'a7a5e1f8-25d6-4838-a408-29120cd05304' # 余额播报
zhangdie_token = 'fe49ef75-9509-4e54-b16c-a63d76e8f13d'  # 异常涨跌
wave_token = ''


def _msg(text):
    json_text = {
        "msgtype": "text",
        "at": {
            "atMobiles": ["11111"],
            "isAtAll": False
        },
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_msg(text):
    json_text = {
        "msgtype": "text",
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_img(base64_data, md5_data):
    json_text = {
        "msgtype": "image",
        "image": {
            "base64": str(base64_data,'utf-8'),
            "md5": md5_data
        }
    }
    return json_text

def dingding_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = "https://oapi.dingtalk.com/robot/send?access_token=%s" % token
    json_text = _msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_img(path, token):
    hash = md5()
    img = open(path, 'rb')
    data = img.read()
    hash.update(data)
    base64_data = base64.b64encode(data)
    md5_data = hash.hexdigest()
    img.close()
    
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_img(base64_data, md5_data)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def ts2date(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%Y-%m-%d", local_time)
    return z

def ts2idx(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%H%M", local_time)
    return int(z)

def check_klines(klines):
        for i in range(len(klines)-1):
            if klines[i]['t'] - klines[i+1]['t'] != 60 * 1000:
                print(klines[i], klines[i+1])
                return False
        return True


