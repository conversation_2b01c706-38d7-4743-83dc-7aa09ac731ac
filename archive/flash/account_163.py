# -*- coding: utf-8 -*-
import websocket
import time, json, os, sys
from websocket import create_connection
import pandas as pd
from kafka import KafkaProducer, KafkaConsumer
from multiprocessing import Process,Queue
import logging, schedule, redis

from util import *

import sys
sys.path.append("../public")
from constant import *
from FuturesApi import FuturesApi

logger = logging.getLogger("SmallGoal-Sub_account")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.account-futures')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)



class sub_account_info():
    def __init__(self):
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self._x = {'NEW':'新订单', 'CANCELED':'已撤单', 'TRADE':'交易', 'EXPIRED':'订单失效'}
        self._X = {'NEW':'新订单', 'CANCELED':'已撤单', 'FILLED':'成交', 'PARTIALLY_FILLED':'部分成交', 'EXPIRED':'订单失效'}
        self._o = {'MARKET':'市价单', 'LIMIT':'限价单', 'STOP_MARKET':'市价止损单', 'TAKE_PROFIT_MARKET':'市价止盈单', 'STOP':'止损单', 'TAKE_PROFIT':'止盈单', 'LIQUIDATION':'强平单'}
        self.fid = ['pa','ep','cr','up','iw']
        self.info_list = []
        self.last_time = time.time()

    def get_listen_key(self):
        req = self.fapi.get_listen_key()
        listen_key = req['listenKey']
        # logger.info('listen_key:' + listen_key )
        return listen_key
    
    def live_subscribe(self, listen_key):
        params = [listen_key]
        self.streams = { "method": "SUBSCRIBE", "params": params, "id": ********}
        self.ws = create_connection("wss://fstream.binance.com/ws")
        self.ws.send(json.dumps(self.streams))
        self.ws.recv()
    
    def account_update(self):
        a = self.msg['a']
        info = []
        info.append('合约账户通知：')
        info.append('钱包余额:' + a['B'][0]['wb'])
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        positions = {}
        for i in a['P']:
            if i['ps'] != 'BOTH':
                continue
            info.append('--------------------')
            info.append('交易对：' + i['s'])
            info.append('入仓价格：' + i['ep'])
            info.append('仓位数量：' + i['pa'])
            info.append('累积损益：' + i['cr'])
            for id in self.fid:
                i[id] = float(i[id])
            self.redis.set(group + '_futures_' + i['s'] + '_p', json.dumps(i))
            positions[i['s']] = i
        info.append('事件时间：' + tstr)
        #self.redis.set(group + '_futures_positions', json.dumps(positions))
        return '\n'.join(info)
    
    def order_trade_update(self):
        o = self.msg['o']
        if o['x'] == 'TRADE':
            self.redis.lpush(group + '_futures_order_trade_list', json.dumps(self.msg))
            #self.redis.lpush(group + '_futures_order_trade_day_list', json.dumps(self.msg))
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        info = []
        info.append('订单通知：' + self._x[o['x']] + '-' + o['s'])
        info.append('订单状态：' + self._X[o['X']])
        # info.append('交易对：' + o['s'])
        info.append('订单方向：' + o['S'])
        #info.append('持仓方向：'  + o['ps'])
        info.append('订单类型：' + self._o[o['o']])
        info.append('原始价格：' + o['p'])
        if o['X'] != 'NEW':
            info.append('平均价格：' + o['ap'])
        info.append('原始数量：' + o['q'])
        if o['X'] != 'NEW':
            info.append('累计成交：' + o['z'])
        if o['X'] != 'NEW':
            info.append('交易盈亏：' + o['rp'])
        if o['o'] == 'STOP_MARKET':
            info.append('触发价格：' + o['sp'])
        if 'n' in o:
            info.append('手续费用：' + o['n'])
        info.append('订单编号：' + str(o['i']))
        info.append('事件时间：' + tstr)
        return '\n'.join(info)
    
    '''
    {
	"e": "GRID_UPDATE", // 事件类型
	"T": 1669262908216, // 撮合时间
	"E": 1669262908218, // 事件时间
	"gu": {
			"si": 176057039, // 策略 ID
			"st": "GRID", // 策略类型
			"ss": "WORKING", // 策略状态
			"s": "BTCUSDT", // 交易对
			"r": "-0.00300716", // 已实现 PNL
			"up": "16720", // 未配对均价
			"uq": "-0.001", // 未配对数量
			"uf": "-0.00300716", // 未配对手续费
			"mp": "0.0", // 已配对 PNL
			"ut": 1669262908197 // 更新时间
		}
    }
    '''
    
    def grid_update(self):
        info = []
        info.append('网格更新：')
        info.append('策略ID：' + self.msg['gu']['si'])
        # info.append('策略类型：' + self.msg['gu']['st'])
        # info.append('策略状态：' + self.msg['gu']['ss'])
        info.append('交易对：' + self.msg['gu']['s'])
        info.append('已实现 PNL：' + self.msg['gu']['r'])
        info.append('未配对均价：' + self.msg['gu']['up'])
        info.append('未配对数量：' + self.msg['gu']['uq'])
        info.append('未配对手续费：' + self.msg['gu']['uf'])
        info.append('已配对 PNL：' + self.msg['gu']['mp'])
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        info.append('更新时间：' + tstr)
        return '\n'.join(info)
    
    def loop_run(self):
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        listen_key = self.get_listen_key()
        self.live_subscribe(listen_key) 
        
        while True:
            recv = self.ws.recv()
            logger.info(recv)
            self.msg = json.loads(recv)
            self.producer.send('futures_all', key='account_163', value=recv)          
            if 'listenKeyExpired' in recv:
                listen_key = self.get_listen_key()
                self.live_subscribe(listen_key)
                continue
            if self.msg['e'] == 'ACCOUNT_UPDATE':
                info = self.account_update()
                # weixin_info(info, balance_token)
                feishu_info(info, fs_token)
                
            if self.msg['e'] == 'ORDER_TRADE_UPDATE':
                info = self.order_trade_update()
                # weixin_info(info, order_token)
                feishu_info(info, fs_token)
                # if 'FILLED' in recv:
                    # weixin_info(info, warning_token)
                    # weixin_info(info, filled_token)
            
            if self.msg['e'] == 'GRID_UPDATE':
                info = self.grid_update()
                feishu_info(info, fs_token)


def main():
    while True:
        try:
            instance = sub_account_info()
            instance.loop_run()
        except Exception as e:
            logger.error(e)


if __name__ == "__main__":
    main()
