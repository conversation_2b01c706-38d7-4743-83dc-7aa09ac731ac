import json, time, logging
import numpy as np
import requests


logger = logging.getLogger("SmallGoal")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/log.bls')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)


class bls():
    def __init__(self):
        self.cpi_sid = 'CUUR0000SA0'
        self.ppi_sid = 'PCU22112222112241'
            
    def get_bls(self, sid):
        url = 'https://api.bls.gov/publicAPI/v1/timeseries/data/' + sid
        return requests.get(url).json()
        
    def run(self):
        print('starts:')
        i = 0
        while True:
            try:
                i = i + 1
                cpi = self.get_bls(self.ppi_sid)
                logger.info(str(i) + ' - ' + json.dumps(cpi))
            except Exception as e:
                print(i, e)
                     
                     
if __name__ == "__main__":
    t = bls()
    t.run()
