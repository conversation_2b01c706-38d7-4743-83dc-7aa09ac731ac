import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import numpy as np
from datetime import datetime

import sys
sys.path.append("/root/workspace/trade/public")
from FuturesApi import FuturesApi
from util import *

class trend():
    def __init__(self):
        api_key = 'kUAgsplhuNrs3jrD0KCFSUokbUFkq49UXKMRSwPPlgiYoFtAxeb4x6yRch7Ilg5c'
        api_secret = 'T0mAgpa1XgwfDwGY6KByxFXVpmHHyLqcwve9fvcZDKtcnz3BeH0NcGBiYILlfYH9'
        api_key = 'TIhE2w9Ik5DqOUBL2aQjh4mXkb9c6RevfIVHBXk6C9wk4hznwiioxfhf2eM3JCmk'
        api_secret = 'EHn9fl9Wc4jwmSKLRFeeupSmqLsCFD6Zl62wIuQiYaUBwwXoMVnd7akYZJ1FOYTe'
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.position = None
        self.quantity = 1
            
    def order(self, side, symbol):
        qty = self.quantity
        msg = None
        if side == self.position:
            return
        if self.position != None:
            qty = self.quantity * 4

        if side == 'BUY':
            msg = self.fapi.buy_market(symbol, qty)
        else:
            msg = self.fapi.sell_market(symbol, qty)
        
        if msg:
            print(msg)
        if 'code' in msg:
            self.order(side, symbol)

        self.position = side
    
        
    def run(self):
        print('starts:')
        group_id = 'flash'
        symbol = 'ETHUSDT'
        price_threshold = 0.0013
        vol_threshold = 7
        consumer = KafkaConsumer('spot_kline',
            group_id = group_id,
            auto_offset_reset='latest', #earliest
            key_deserializer= bytes.decode,
            value_deserializer= bytes.decode,
            auto_commit_interval_ms=1000)
        for msg in consumer:
            if msg.key != f'{symbol}_1s':
                continue
            kline = json.loads(msg.value)
            dt = datetime(2023, 2, 28, 20, 30, 0)
            ts = dt.timestamp() * 1000
            self.redis.lpush(f'spot_{symbol}_1s', msg.value)
            if kline['T'] >= ts -2000 and kline['T'] <= ts:
                o = kline['c']
                past = self.redis.lrange(f'spot_{symbol}_1s', 0, 300)
                past = [json.loads(k) for k in past]
                vol = np.mean([k['v'] for k in past])
                local_time = time.localtime(time.time())
                tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
                print(tstr, o, vol)
            if kline['T'] > ts:
                local_time = time.localtime(time.time())
                tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time) + '.' + str(int(time.time() * 1000))[-3:]
                rate = (kline['c'] - o) / o
                vol_ratio = kline['v'] / vol
                print(tstr, kline['c'], str(round(rate*100, 2)) + '%', round(vol_ratio, 2))
                if rate > price_threshold and vol_ratio > vol_threshold:
                    print(tstr, '-----------buy--------')
                    self.order('BUY', symbol)
                if rate < -price_threshold and vol_ratio > vol_threshold:
                    print(tstr, '------------sell-----------')
                    self.order('SELL', symbol)
                    

            
    
if __name__ == "__main__":
    t = trend()
    t.run()
