import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import numpy as np
from datetime import datetime
from decimal import Decimal

import sys
sys.path.append("/root/workspace/trade/public")
from FuturesApi import FuturesApi
from util import *

logger = logging.getLogger("SmallGoal-shot")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/flash/log/log.flash2')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)


class trend():
    def __init__(self):
        api_key = 'kUAgsplhuNrs3jrD0KCFSUokbUFkq49UXKMRSwPPlgiYoFtAxeb4x6yRch7Ilg5c'
        api_secret = 'T0mAgpa1XgwfDwGY6KByxFXVpmHHyLqcwve9fvcZDKtcnz3BeH0NcGBiYILlfYH9'
        api_key = 'TIhE2w9Ik5DqOUBL2aQjh4mXkb9c6RevfIVHBXk6C9wk4hznwiioxfhf2eM3JCmk'
        api_secret = 'EHn9fl9Wc4jwmSKLRFeeupSmqLsCFD6Zl62wIuQiYaUBwwXoMVnd7akYZJ1FOYTe'
        # trph
        api_key = 'ynR0rmsqOzWYPIwGm5F0tK575YYlGfQTYyff5FUay3q8W6qjjOToee4Sb3gE3be7'
        api_secret = '7Og5pRcumpB1Fqbs95LbsA8YOPHzQsKTmxTesMW2iQ3EbbKnaKG8gbLAmcZn0udg'
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.position = None
        self.quantity = 1
            
    def adjust_price(self, symbol, price):
        info = json.loads(self.redis.get('futures_' + symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'PRICE_FILTER':
                tickSize = float(flt['tickSize'])
                count = int(price / tickSize)
                return float(Decimal(str(count)) * Decimal(str(tickSize)))
    
        
    def run(self):
        print('starts:')
        group_id = 'flash2'
        symbol = 'BTCUSDT'
        price_threshold = 0.0013
        vol_threshold = 7
        consumer = KafkaConsumer('spot_kline',
            group_id = group_id,
            auto_offset_reset='latest', #earliest
            key_deserializer= bytes.decode,
            value_deserializer= bytes.decode,
            auto_commit_interval_ms=1000)
        for msg in consumer:
            if msg.key != f'{symbol}_1s':
                continue
            kline = json.loads(msg.value)
            dt = datetime(2023, 5, 10, 20, 30, 0)
            ts = int(dt.timestamp() * 1000)
            #self.redis.lpush(f'spot_{symbol}_1s', msg.value)
            if kline['T'] == ts -2000:
                msg = self.fapi.buy_market(symbol, 0.01)
                logger.info(json.dumps(msg))
                avgPrice = float(msg['avgPrice'])
                price = avgPrice * 0.996
                price = self.adjust_price(symbol, price)
                msg = self.fapi.stop_market(symbol, 'SELL', price, 0.01)
                logger.info(json.dumps(msg))                            
            
    
if __name__ == "__main__":
    t = trend()
    t.run()
