import json, time, logging
import numpy as np
import requests
from fredapi import Fred


logger = logging.getLogger("SmallGoal")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/flash/log/log.ppi')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)


class bls():
    def __init__(self):
        self.cpi_sid = 'CPIAUCNS'
        self.ppi_sid = 'PPIACO'
        self.fred = Fred(api_key='16717b38415102a04cf2c3e504ba0430')
        
    def run(self):
        print('starts:')
        while True:
            try:
                ppi = self.fred.get_series(self.ppi_sid,
                    observation_start='2023-01-01', 
                    observation_end='2023-09-01')
                ppi.index = ppi.index.strftime('%y-%m-%d')
                logger.info(json.dumps(ppi.to_dict()))
            except Exception as e:
                print(e)
                     
    
if __name__ == "__main__":
    t = bls()
    t.run()
