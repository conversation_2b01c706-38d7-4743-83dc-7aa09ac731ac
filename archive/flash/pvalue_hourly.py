# -*- coding: utf-8 -*
import time, json
import redis
import pandas as pd
from util import *
import numpy as np
from statsmodels.tsa.stattools import coint
from statsmodels.tsa.stattools import adfuller

r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)

symbols = ['1INCHUSDT', 'AAVEUSDT', 'ADAUSDT', 'ALGOUSDT', 'ALICEUSDT', 'ALPHAUSDT', 'ANTUSDT', 'APEUSDT', 'API3USDT', 'ARUSDT', 'ATOMUSDT', 'AUDIOUSDT', 'AVAXUSDT', 'AXSUSDT', 'BAKEUSDT', 'BCHUSDT', 'BELUSDT', 'BNBUSDT', 'BNXUSDT', 'BTCUSDT', 'CELOUSDT', 'CELRUSDT', 'CHRUSDT', 'CHZUSDT', 'COTIUSDT', 'CRVUSDT', 'DARUSDT', 'DOGEUSDT', 'DOTUSDT', 'DYDXUSDT', 'EGLDUSDT', 'ENJUSDT', 'ENSUSDT', 'EOSUSDT', 'ETCUSDT', 'ETHUSDT', 'FILUSDT', 'FTMUSDT', 'GALUSDT', 'GALAUSDT', 'GMTUSDT', 'GRTUSDT', 'IMXUSDT', 'IOSTUSDT', 'JASMYUSDT', 'KAVAUSDT', 'KNCUSDT', 'LINAUSDT', 'LINKUSDT', 'LRCUSDT', 'LTCUSDT', 'MANAUSDT', 'MATICUSDT', 'MTLUSDT', 'NEARUSDT', 'OGNUSDT', 'ONEUSDT', 'PEOPLEUSDT', 'ROSEUSDT', 'RSRUSDT', 'RUNEUSDT', 'SANDUSDT', 'SKLUSDT', 'SNXUSDT', 'SOLUSDT', 'SUSHIUSDT', 'SXPUSDT', 'THETAUSDT', 'TRXUSDT', 'UNIUSDT', 'VETUSDT', 'WAVESUSDT', 'XLMUSDT', 'XMRUSDT', 'XRPUSDT', 'XTZUSDT', 'YFIUSDT', 'ZECUSDT', 'ZILUSDT', 'ZRXUSDT'] + ['1000SHIBUSDT'] + ['BANDUSDT','BLZUSDT','CTSIUSDT','FLMUSDT','GTCUSDT','LITUSDT','TRBUSDT','UNFIUSDT']
symbols.sort()

def calculate_pvalues():
    local_time = time.localtime(time.time())
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    data = {}
    for s in symbols:
        key =f'futures_{s}_1m'
        k = r.lrange(key, 0, 1440*3-1)
        close = [json.loads(i)['c'] for i in k]
        data[s] = close
    for s in symbols:
        exp = data[s]
        mean_ = np.mean(exp)
        std = np.std(exp)
        max_ = np.max(exp)
        min_ = np.min(exp)
        top = np.max(exp)
        bottom = np.min(exp)
        pvalue = round(adfuller(exp)[1], 4)
        stat = {'pvalue':pvalue,'mean':mean_,'max':max_, 'min': min_, 'top':top, 'bottom':bottom, 'std':std,'time':tstr}
        key =f'futures_{s}_stat'
        r.lpush(key, json.dumps(stat))

def main():
    time.sleep(10)
    calculate_pvalues()


if __name__ == '__main__':
    main()
