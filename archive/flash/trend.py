import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from FuturesApi import FuturesApi
from constant import all_symbols, trade_symbols
from util import *
from multiprocessing import Process,Queue
import pandas as pd
from FuturesApi import FuturesApi

logger = logging.getLogger("SmallGoal-trend")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler('/root/workspace/trade/trend/log/log.trend')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class trend():
    def __init__(self):
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.trade_symbols = trade_symbols
        self.indexes = ['o', 'h', 'l', 'c', 'n', 'v', 'q', 'V', 'Q', 't', 'T', 'E', 'co', 'ho', 'hl','z']
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        self.trade_symbols = ['1INCHUSDT', 'AAVEUSDT', 'ADAUSDT', 'ALGOUSDT', 'ALICEUSDT', 'ALPHAUSDT', 'ANTUSDT', 'APEUSDT', 'API3USDT', 'ARUSDT', 'ATOMUSDT', 'AUDIOUSDT', 'AVAXUSDT', 'AXSUSDT', 'BAKEUSDT', 'BCHUSDT', 'BELUSDT', 'BNBUSDT', 'BNXUSDT', 'BTCUSDT', 'CELOUSDT', 'CELRUSDT', 'CHRUSDT', 'CHZUSDT', 'COTIUSDT', 'CRVUSDT', 'DARUSDT', 'DOGEUSDT', 'DOTUSDT', 'DYDXUSDT', 'EGLDUSDT', 'ENJUSDT', 'ENSUSDT', 'EOSUSDT', 'ETCUSDT', 'ETHUSDT', 'FILUSDT', 'FTMUSDT', 'GALUSDT', 'GALAUSDT', 'GMTUSDT', 'GRTUSDT', 'IMXUSDT', 'IOSTUSDT', 'JASMYUSDT', 'KAVAUSDT', 'KNCUSDT', 'LINAUSDT', 'LINKUSDT', 'LRCUSDT', 'LTCUSDT', 'MANAUSDT', 'MATICUSDT', 'MTLUSDT', 'NEARUSDT', 'OGNUSDT', 'ONEUSDT', 'PEOPLEUSDT', 'ROSEUSDT', 'RSRUSDT', 'RUNEUSDT', 'SANDUSDT', 'SKLUSDT', 'SNXUSDT', 'SOLUSDT', 'SUSHIUSDT', 'SXPUSDT', 'THETAUSDT', 'TRXUSDT', 'UNIUSDT', 'VETUSDT', 'WAVESUSDT', 'XLMUSDT', 'XMRUSDT', 'XRPUSDT', 'XTZUSDT', 'YFIUSDT', 'ZECUSDT', 'ZILUSDT', 'ZRXUSDT'] + ['1000SHIBUSDT'] + ['BANDUSDT','BLZUSDT','CTSIUSDT','FLMUSDT','GTCUSDT','LITUSDT','TRBUSDT','UNFIUSDT']
        self.trade_symbols.sort()
        self.profit = float(self.redis.get('trend_profit'))
        self.his = {}
        self.num = 4
        self.qty = 100
        self.p_open = 0.2
        self.std_open = 3.8
        self.std_close = 1
    
    def adjust_price(self, symbol, price):
        info = json.loads(self.redis.get(symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'PRICE_FILTER':
                tickSize = float(flt['tickSize'])
                return int(price / tickSize) * tickSize
    

    def get_price(self, symbol):
        depth = json.loads(self.redis.get(symbol + '_depthUpdate'))
        gap = time.time() - depth['E']/1000
        if gap < 3:
            bid = depth['b'][0][0] 
        else:
            depth = self.fapi.get_depth(symbol, 5)
            bid = float(depth['bids'][0][0])
            logger.info(f'{symbol} get depthUpdate delay: {gap} seconds')
        return bid
    
    def record_his(self,asset, price, pvalue, diff, rtn, tstr, contion, action):
        logger.info(json.dumps(action))
        self.profit += rtn
        self.his = {}
        self.his['symbol'] = asset['s']
        self.his['open_date'] = asset['date']
        self.his['close_date'] = tstr
        self.his['open_ts'] = int(asset['ts']*1000) / 1000
        self.his['close_ts'] = int(time.time()*1000) / 1000
        self.his['target'] = asset['target']
        self.his['top'] = asset['top']
        self.his['bottom'] = asset['bottom']
        self.his['open_price'] = asset['p']
        self.his['close_price'] = price
        self.his['max'] = asset['max']
        self.his['min'] = asset['min']
        self.his['mean'] = self.adjust_price(asset['s'], asset['mean'])
        self.his['open-mean_rate'] = round(asset['p']/asset['mean']-1, 4)
        self.his['std_ratio'] = round(asset['std']/asset['mean'], 4)
        self.his['mnt_rate'] = round(asset['mnt_rate'], 5)
        self.his['open_pvalue'] = asset['pvalue']
        self.his['close_pvalue'] = pvalue
        self.his['std'] =  self.adjust_price(asset['s'], asset['std'])
        self.his['open_diff_ratio'] = round(asset['diff'], 2)
        self.his['close_diff_ratio'] = round(diff/asset['std'], 2)
        self.his['rtn'] = round(rtn, 2)
        self.his['profit'] = round(self.profit, 2)
        self.his['rate'] = round(self.profit/(self.num*self.qty), 4)
        self.his['t_win'] = asset['t_win']
        self.his['t_lose'] = asset['t_lose']
        self.his['num'] = len(self.assets)
        self.his['contion'] = contion

        self.redis.lpush('trend_close_position_history', json.dumps(self.his))

        logger.info(json.dumps(self.his))


    def run(self):
        producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        local_time = time.localtime(time.time())
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        tmp = []
        actions = []
        self.assets = json.loads(self.redis.get('trend_assets'))
        for asset in self.assets:
            s = asset['s']
            p = asset['p']
            v = asset['v']
            price = self.get_price(s)
            key = f'futures_{s}_1m'
            kline = json.loads(self.redis.lindex(key,0))
            
            asset['max'] = max(price, max(kline['h'], asset['max']))
            asset['min'] = min(price, min(kline['l'], asset['min']))

            std = asset['std']
            stat = json.loads(self.redis.lindex(f'futures_{s}_stat',0))
            pvalue = stat['pvalue']
            mean_ = asset['mean']
            top = asset['top']
            bottom = asset['bottom']

            diff = price - mean_
            rtn = asset['v'] * (price - asset['p'])
            action = {'symbol':s, 'weight': 0}
            if v < 0:
                if abs(diff) / std < self.std_close:
                    self.record_his(asset, price, pvalue, diff, rtn, tstr, 'inner_std', action)
                    actions.append(action)
                elif asset['min'] - p < -2 * std:
                    self.record_his(asset, price, pvalue, diff, rtn, tstr, 'out_std', action)
                    actions.append(action)
                else:
                    tmp.append(asset)
            else:
                if abs(diff) / std < self.std_close:
                    self.record_his(asset, price, pvalue, diff, rtn, tstr, 'inner_std', action)
                    actions.append(action)
                elif asset['max'] - p > 2 * std:
                    self.record_his(asset, price, pvalue, diff, rtn, tstr, 'out_std', action)
                    actions.append(action)
                else:
                    tmp.append(asset)
        self.assets = tmp

        for s in self.trade_symbols:
            if len(self.assets) >= 3 or s in str(self.assets):
                continue
            stat = json.loads(self.redis.lindex(f'futures_{s}_stat',0))
            pvalue = stat['pvalue']
            key = f'futures_{s}_1m'
            kline = json.loads(self.redis.lindex(key,0))
            mnt_rate = kline['c'] / kline['o'] - 1
            if pvalue > self.p_open or abs(mnt_rate) > 0.02 :
                continue

            std = stat['std']
            mean_ = stat['mean']
            top = stat['top']
            bottom = stat['bottom']

            price = self.get_price(s)
            diff = price - mean_
            t={}
            t['s'] = s
            if diff > std * self.std_open or price > top:
                t['target'] = 'Long'
                weight = 1
                target_price1 = price + 2 * std
                target_price2 = mean_ + std
            elif diff < -std * self.std_open or price < bottom:
                weight = -1
                t['target'] = 'Short'
                target_price1 = price - 2 * std
                target_price2 = mean_ - std
            else:
                continue

            action = {'symbol':s, 'weight':-weight}
            logger.info(json.dumps(action))
            actions.append(action)

            t['date'] = tstr
            t['p'] = price
            t['pvalue'] = pvalue
            t['mean'] = self.adjust_price(s, mean_)
            t['std'] = std
            t['diff'] = round(diff/std, 2)
            t['max'] = price
            t['min'] = price
            t['top'] = top
            t['bottom'] = bottom
            t['v'] = self.qty / price * weight
            t['ts'] = int(time.time()*1000)/1000
            t['mnt_rate'] = round(mnt_rate, 4)
            t['t_win'] = self.adjust_price(s, target_price1)
            t['t_lose'] = self.adjust_price(s, target_price2)
            self.assets.append(t)
            self.redis.lpush('trend_open_position_history', json.dumps(t))
            logger.info('open_position:' + json.dumps(t))
        if len(actions) > 0:
            producer.send('trade', key='trend', value=json.dumps(actions))
        self.redis.set('trend_assets', json.dumps(self.assets))
        self.redis.set('trend_profit', self.profit)
  

if __name__ == "__main__":
    t = trend()
    time.sleep(10)
    t.run()