# -*- coding: utf-8 -*-
import websocket
import time, json, os, sys
from websocket import create_connection
import pandas as pd
from kafka import KafkaProducer, KafkaConsumer
from multiprocessing import Process,Queue
import logging, schedule, redis
from util import *

import sys
sys.path.append("../public")
from FuturesApi import FuturesApi

logger = logging.getLogger("SmallGoal-Sub_account")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.account-sub')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

api = FuturesApi(api_key, api_secret)
r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)


class sub_account_info():
    def __init__(self):
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self._x = {'NEW':'新订单', 'CANCELED':'已撤单', 'TRADE':'交易', 'EXPIRED':'订单失效'}
        self._X = {'NEW':'新订单', 'CANCELED':'已撤单', 'FILLED':'成交', 'PARTIALLY_FILLED':'部分成交', 'EXPIRED':'订单失效'}
        self._o = {'MARKET':'市价单', 'LIMIT':'限价单', 'STOP_MARKET':'市价止损单', 'TAKE_PROFIT_MARKET':'市价止盈单', 'STOP':'止损单', 'TAKE_PROFIT':'止盈单', 'LIQUIDATION':'强平单'}
        self.fid = ['pa','ep','cr','up','iw']
        self.info_list = []
        self.last_time = time.time()

    def get_listen_key(self):
        req = self.fapi.get_listen_key()
        listen_key = req['listenKey']
        # logger.info('listen_key:' + listen_key )
        return listen_key
    
    def live_subscribe(self, listen_key):
        params = [listen_key]
        self.streams = { "method": "SUBSCRIBE", "params": params, "id": ********}
        self.ws = create_connection("wss://fstream.binance.com/ws")
        self.ws.send(json.dumps(self.streams))
        self.ws.recv()
    
    def account_update(self):
        a = self.msg['a']
        info = []
        info.append('账户通知：')
        info.append('钱包余额:' + a['B'][0]['wb'])
        self.redis.set('balance_USDT', a['B'][0]['wb'])
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        for i in a['P']:
            info.append('--------------------')
            info.append('交易对：' + i['s'])
            info.append('入仓价格：' + i['ep'])
            info.append('仓位数量：' + i['pa'])
            info.append('累积损益：' + i['cr'])
            for id in self.fid:
                i[id] = float(i[id])
            self.redis.set(group + '_' + i['s'] + '_p', json.dumps(i))
        info.append('事件时间：' + tstr) 
        return '\n'.join(info)
    
    def order_trade_update(self):
        o = self.msg['o']
        if o['x'] == 'TRADE':
            self.redis.lpush(group + '_order_trade_list', json.dumps(self.msg))
            self.redis.lpush(group + '_order_trade_day_list', json.dumps(self.msg))
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        info = []
        info.append('订单通知：' + self._x[o['x']] + '-' + o['s'])
        info.append('订单状态：' + self._X[o['X']])
        # info.append('交易对：' + o['s'])
        info.append('订单方向：' + o['S'])
        #info.append('持仓方向：'  + o['ps'])
        info.append('订单类型：' + self._o[o['o']])
        info.append('原始价格：' + o['p'])
        if o['X'] != 'NEW':
            info.append('平均价格：' + o['ap'])
        info.append('原始数量：' + o['q'])
        if o['X'] != 'NEW':
            info.append('累计成交：' + o['z'])
        if o['X'] != 'NEW':
            info.append('交易盈亏：' + o['rp'])
        if o['o'] == 'STOP_MARKET':
            info.append('触发价格：' + o['sp'])
        if 'n' in o:
            info.append('手续费用：' + o['n'])
        info.append('订单编号：' + str(o['i']))
        info.append('事件时间：' + tstr)
        return '\n'.join(info)

    def loop_run(self):
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        listen_key = self.get_listen_key()
        self.live_subscribe(listen_key) 
        
        while True:
            recv = self.ws.recv()
            logger.info(recv)
            self.msg = json.loads(recv)
            self.producer.send('futures_all', key='account', value=recv)          
            if 'listenKeyExpired' in recv:
                listen_key = self.get_listen_key()
                self.live_subscribe(listen_key)
                continue
            if self.msg['e'] == 'ACCOUNT_UPDATE':
                info = self.account_update()
                
            if self.msg['e'] == 'ORDER_TRADE_UPDATE':
                info = self.order_trade_update()
                self.info_list.append(info)
                t = time.time()
                if len(self.info_list) >= 15 or t - self.last_time > 3 or 'ios' in recv:# or 'STOP_MARKET' in recv:
                    weixin_info('\n--------------------\n'.join(self.info_list), order_token)
                    self.info_list = []
                if 'EXPIRED' in recv:
                    weixin_info(info, warning_token)
                    symbol = self.msg['o']['s']
                    ban_symbols = json.loads(r.get('ban_symbols'))
                    ban_symbols.append(symbol)
                    r.set('ban_symbols', json.dumps(ban_symbols))
                self.last_time = t
            

            

def main():
    while True:
        try:
            instance = sub_account_info()
            instance.loop_run()
        except Exception as e:
            logger.error(e)


if __name__ == "__main__":
    main()
