# -*- coding: utf-8 -*
import json, logging
import redis
import time
import pandas as pd
import numpy as np
from kafka import KafkaProducer, KafkaConsumer
from util import *

import sys
sys.path.append("/root/workspace/trade/public")
from FuturesApi import FuturesApi

logger = logging.getLogger("SmallGoal-Trade")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.trade')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class Trade():
    def __init__(self):
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.position = None
        self.symbol = None
        self.trade_price = None
        self.trade_volume = None
    
    def set_exchange_info(self):
        exchange_info = self.fapi.get_exchangeInfo()
        symbols = exchange_info['symbols']
        for info in symbols:
            symbol = info['symbol']
            self.redis.set(symbol + '_info', json.dumps(info))
    
    def get_current_pa(self, symbol):
        p = self.redis.get(group + '_' + symbol + '_p')
        if p:
            return json.loads(p)['pa']
        else:
            return 0.0
    
    def get_market_maxQty(self, symbol):
        info = json.loads(self.redis.get(symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'MARKET_LOT_SIZE':
                return float(flt['maxQty'])
        

    def get_ask_bid(self, symbol):
        depth = json.loads(self.redis.get('futures_' + symbol + '_depthUpdate'))
        gap = time.time() - depth['E']/1000
        if gap < 3:
            bid = depth['b'][0][0]
            ask = depth['a'][0][0]         
        else:
            depth = self.fapi.get_depth(symbol, 5)
            bid = float(depth['bids'][0][0])
            ask = float(depth['asks'][0][0])
            logger.info(f'{symbol} get depthUpdate delay: {gap} seconds')
        return bid, ask
    
    def get_24h_avgprice(self, symbol):
        key = f'futures_{symbol}_1m'
        k = self.redis.lrange(key, 0, 60 * 24)
        closes = [json.loads(i)['c'] for i in k]
        return np.mean(closes)

    def adjust_q(self,symbol, q, price):
        info = json.loads(self.redis.get(symbol + '_info'))
        filters = info['filters']
        notional = 5
        for flt in filters:
            if flt['filterType'] == 'MARKET_LOT_SIZE':
                step_size = float(flt['stepSize'])
                min_qty = float(flt['minQty'])
            if flt['filterType'] == 'MIN_NOTIONAL':
                notional = float(flt['notional'])
        q = int(q / step_size) * step_size
        if abs(q) < min_qty or price * abs(q) < notional * 1.2:
            return 0
        return q

    def gererate_orderId(self, strategy, symbol):
        t = int(time.time()*1000) / 1000
        local_time = time.localtime(time.time())
        tstr = time.strftime("%y%m%d-%H:%M:%S", local_time) + str(t)[-4:]
        return f'{strategy}_{symbol[:-4]}_{tstr}'

    def market_trade(self, symbol, dp):
        maxQty = self.get_market_maxQty(symbol)
        if abs(dp) / maxQty > 3:
            info = f'{symbol} market trade too much: {dp}'
            weixin_info(info, warning_token)
            return
        
        dp2 = abs(dp)
        ratio = int(dp2 / maxQty)
        reminder = dp2 % maxQty
        qtys = [maxQty] * ratio + [reminder]
        
        for qty in qtys:
            newClientOrderId = self.gererate_orderId(group, symbol)
            if dp > 0:
                msg = self.fapi.buy_market(symbol, qty, newClientOrderId)
            else:
                msg = self.fapi.sell_market(symbol, qty, newClientOrderId)
            if 'code' in msg:
                msg['symbol'] = symbol
                weixin_info(json.dumps(msg), warning_token)
            logger.info(json.dumps(msg))
        

    def make_deal(self, orders):
        # {'symbol': , 'side', 'weight',}
        balance = float(self.redis.get(f'{group}_base'))
        #balance = float(self.redis.get('balance_hour'))
        all_fp = []
        lever = float(self.redis.get(f'{group}_lever'))
        lever = min(max(0, lever), 20)
        hold_symbol = []
        trade_info = ['订单通知：']
        trade_info.append('symbol, quantity')
        i = 0
        target_p = {}
        while i < len(orders):
            v = orders[i]
            i += 1

            symbol = v['symbol']
            msg = self.fapi.delete_allOpenOrders(symbol)
            logger.info('delete OpenOrders: ' + symbol + json.dumps(msg))
            time.sleep(0.1)
            weight = v['weight']
            pa = self.get_current_pa(symbol)
            bid, ask = self.get_ask_bid(symbol)
            avg_price_24h = self.get_24h_avgprice(symbol)    
            fp = lever * balance * weight / avg_price_24h
            fp = self.adjust_q(symbol, fp, bid)
            target_p[symbol] = bid * fp
            dp = fp - pa
            dp = self.adjust_q(symbol, dp, bid)
            msg = None
            newClientOrderId = self.gererate_orderId(group, symbol)
            if dp > 0:
                trade_info.append(f'{symbol[0:-4]}, {round(dp*bid, 2)}')
                msg = self.fapi.buy_limit(symbol, dp, bid, newClientOrderId)
                price = bid
            elif dp < 0:
                trade_info.append(f'{symbol[0:-4]}, {round(dp*ask, 2)}')
                msg = self.fapi.sell_limit(symbol, abs(dp), ask, newClientOrderId)
                price = ask
            else:
                hold_symbol.append(symbol[0:-4])
            if msg:
                if 'code' in msg:
                    logger.info(symbol + json.dumps(msg))
                    if msg['code'] in [-1001, -1021]:
                        i = i - 1
                    else:
                        weixin_info(symbol + json.dumps(msg), warning_token)
                else:
                    all_fp.append({'symbol':symbol, 'fp': fp, 'price': price, 'orderId': msg['orderId']})
                    logger.info(json.dumps(msg))
        local_time = time.localtime(time.time())
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        trade_info.append('通知时间：' + tstr)
        trade_info = '\n'.join(trade_info) + '\nhold_symbols:' + ','.join(hold_symbol)
        logger.info('target_position:' + json.dumps(target_p))
        weixin_info(trade_info, order_merge_token)
        
        time.sleep(60)

        for i in all_fp:
            symbol = i['symbol']
            pa = self.get_current_pa(symbol)
            fp = i['fp']
            dp = fp - pa
            dp = self.adjust_q(symbol, dp, i['price'])
            if dp != 0:
                msg = self.fapi.delete_order(symbol, i['orderId'])
                logger.info(json.dumps(msg))
                self.market_trade(symbol, dp)
            
     
    def run(self):
        group_id = group
        logger.info('group_id:' + group_id)
        consumer = KafkaConsumer(bootstrap_servers= ['localhost:9092'],
                                group_id= group_id,
                                auto_offset_reset='latest',
                                key_deserializer= bytes.decode, 
                                value_deserializer= bytes.decode)
        consumer.subscribe(topics= ['trade'])
        # key: trph
        for msg in consumer:
            topic = msg.topic
            key = msg.key
            tgap = round(time.time() - msg.timestamp/1000, 1)
            if tgap > 60 * 10 and key == 'trph':
                info = 'Trade {key} delay {diff} seconds: {msg}'.format(key=key, diff=tgap, msg=msg)
                logger.info(info)
                weixin_info(info, weixin_token)
                continue
            value = json.loads(msg.value)
            ban_symbols = json.loads(self.redis.get('ban_symbols'))
            
            #self.follow_trend(topic, key, value)
            if key == 'trph' or key == 'jason':
                for x in value:
                    if x['symbol'] in ban_symbols:
                        x['weight'] = 0
                logger.info(json.dumps(value))
                '''
                half_order = []
                balance = float(self.redis.get('balance_hour'))
                lever = float(self.redis.get('ph_trph_lever'))
                lever = min(max(0, lever), 20)
                for v in value:
                    symbol = v['symbol']
                    weight = v['weight']
                    pa = self.get_current_pa(symbol)
                    avg_price_24h = self.get_24h_avgprice(symbol)    
                    fp = lever * balance * weight / avg_price_24h
                    dp = fp - pa
                    fp = pa + dp / 2
                    new_weight = fp * avg_price_24h / (lever * balance)
                    tmp = {'symbol': symbol, 'weight': new_weight}
                    half_order.append(tmp)
                self.trph(half_order)
                '''

                self.make_deal(value)
            consumer.commit_async()


def main():
    msg = '### 开始交易 ###\nBelieve in AI, Believe in Money!'
    while True:
        try:
            weixin_info(msg, weixin_token)
            trade = Trade()
            trade.set_exchange_info()
            trade.run()
        except Exception as e:
            text = str(e) + ' restart! please check it!'
            weixin_info(text, weixin_token)
    
if __name__ == "__main__":
    main()


