# -*- coding: utf-8 -*

import requests, json
import time
import base64
from hashlib import md5

group = 'jason'

api_key = 'RYuyB5hbLvSwNo3GwKqMWQxX6waX1G7nIqDx5afCoqcZhs4TvlQsRSZw0LPGGChi'
api_secret = 'OiDaWe3HxOl8DIZvoXHjNHdQSkBsa1oaKlZWeGE4BB4Lytooc93tkcxksRdqT1Np'
redis_pw = 'peng1234'
recv_window = 3000


weixin_token = '910da4a9-ca7b-47a1-bf47-0f65506a2a4f'

account_token = '109b0138-ef1c-49ce-b710-41e275563b34'
order_token = 'd941498f-9506-4b18-9de9-40dda12b0230'
warning_token='cfdacca0-8677-42fd-951b-5edd85c74667'
order_merge_token = 'd9cc9dd2-3567-41b4-88c7-e9cec205eee9'
#lever_token = '29411a35-116f-4082-a75b-c99c96420c2b'
profit_token = '2ebda110-0bc9-45ee-904b-a20ce852cd80'
balance_token = 'a7a5e1f8-25d6-4838-a408-29120cd05304'
zhangdie_token = 'fe49ef75-9509-4e54-b16c-a63d76e8f13d'
# wave_token = '9df48d8d-1d63-435e-bb86-788ceb6db6e9'


def _msg(text):
    json_text = {
        "msgtype": "text",
        "at": {
            "atMobiles": ["11111"],
            "isAtAll": False
        },
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_msg(text):
    json_text = {
        "msgtype": "text",
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_img(base64_data, md5_data):
    json_text = {
        "msgtype": "image",
        "image": {
            "base64": str(base64_data,'utf-8'),
            "md5": md5_data
        }
    }
    return json_text

def dingding_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = "https://oapi.dingtalk.com/robot/send?access_token=%s" % token
    json_text = _msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_img(path, token):
    hash = md5()
    img = open(path, 'rb')
    data = img.read()
    hash.update(data)
    base64_data = base64.b64encode(data)
    md5_data = hash.hexdigest()
    img.close()
    
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_img(base64_data, md5_data)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def ts2date(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%Y-%m-%d", local_time)
    return z

def ts2idx(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%H%M", local_time)
    return int(z)

def check_klines(klines):
        for i in range(len(klines)-1):
            if klines[i]['t'] - klines[i+1]['t'] != 60 * 1000:
                print(klines[i], klines[i+1])
                return False
        return True


