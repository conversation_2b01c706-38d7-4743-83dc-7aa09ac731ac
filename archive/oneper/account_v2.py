# -*- coding: utf-8 -*-
import websocket
import time, json, os, sys
from websocket import create_connection
import pandas as pd
from kafka import KafkaProducer, KafkaConsumer
from multiprocessing import Process,Queue
import logging, schedule, redis
from decimal import Decimal

from util import *

import sys
sys.path.append("../public")
from constant import *
from FuturesApi import FuturesApi

logger = logging.getLogger("Account")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.account_v2')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)


class sub_account_info():
    def __init__(self):
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self._x = {'NEW':'新订单', 'CANCELED':'已撤单', 'TRADE':'交易', 'EXPIRED':'订单失效'}
        self._X = {'NEW':'新订单', 'CANCELED':'已撤单', 'FILLED':'成交', 'PARTIALLY_FILLED':'部分成交', 'EXPIRED':'订单失效'}
        self._o = {'MARKET':'市价单', 'LIMIT':'限价单', 'STOP_MARKET':'市价止损单', 'TAKE_PROFIT_MARKET':'市价止盈单', 'STOP':'止损单', 'TAKE_PROFIT':'止盈单', 'LIQUIDATION':'强平单', 'TRAILING_STOP_MARKET':'追踪止损单'}
        self.fid = ['pa','ep','cr','up','iw']
        self.info_list = []
        self.last_time = time.time()

    def get_listen_key(self):
        req = self.fapi.get_listen_key()
        listen_key = req['listenKey']
        # logger.info('listen_key:' + listen_key )
        return listen_key
    
    def live_subscribe(self, listen_key):
        params = [listen_key]
        self.streams = { "method": "SUBSCRIBE", "params": params, "id": ********}
        self.ws = create_connection("wss://fstream.binance.com/ws")
        self.ws.send(json.dumps(self.streams))
        self.ws.recv()
    
    def account_update(self):
        a = self.msg['a']
        info = []
        info.append('合约账户通知：')
        info.append('钱包余额:' + a['B'][0]['wb'])
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        positions = {}
        for i in a['P']:
            if i['ps'] != 'BOTH':
                continue
            info.append('--------------------')
            info.append('交易对：' + i['s'])
            info.append('入仓价格：' + i['ep'])
            info.append('仓位数量：' + i['pa'])
            info.append('累积损益：' + i['cr'])
            for id in self.fid:
                i[id] = float(i[id])
            self.redis.set(group + '_futures_' + i['s'] + '_p', json.dumps(i))
            positions[i['s']] = i
        info.append('事件时间：' + tstr)
        #self.redis.set(group + '_futures_positions', json.dumps(positions))
        return '\n'.join(info)
    
    def order_trade_update(self):
        o = self.msg['o']
        if o['x'] == 'TRADE':
            self.redis.lpush(group + '_futures_order_trade_list', json.dumps(self.msg))
            #self.redis.lpush(group + '_futures_order_trade_day_list', json.dumps(self.msg))
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        info = []
        info.append('订单通知：' + self._x[o['x']] + '-' + o['s'])
        info.append('订单状态：' + self._X[o['X']])
        # info.append('交易对：' + o['s'])
        info.append('订单方向：' + o['S'])
        #info.append('持仓方向：'  + o['ps'])
        info.append('订单类型：' + self._o[o['o']])
        info.append('原始价格：' + o['p'])
        if o['X'] != 'NEW':
            info.append('平均价格：' + o['ap'])
        info.append('原始数量：' + o['q'])
        if o['X'] != 'NEW':
            info.append('累计成交：' + o['z'])
        if o['X'] != 'NEW':
            info.append('交易盈亏：' + o['rp'])
        if o['o'] in ['STOP_MARKET','TAKE_PROFIT_MARKET','STOP','TAKE_PROFIT','TRAILING_STOP_MARKET']:
            info.append('触发价格：' + o['sp'])
        if 'n' in o:
            info.append('手续费用：' + o['n'])
        info.append('订单编号：' + str(o['c']))
        info.append('事件时间：' + tstr)
        return '\n'.join(info)
    
    def adjust_price(self, symbol, price):
        info = json.loads(self.redis.get('futures_' + symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'PRICE_FILTER':
                tickSize = float(flt['tickSize'])
                count = int(price / tickSize)
                return float(Decimal(str(count)) * Decimal(str(tickSize)))
    
    def get_order(self, type, symbol, side='', price=0, volume=0):
        order = {}
        order['type'] = type
        order['symbol'] = symbol
        order['side'] = side
        order['price'] = price
        order['volume'] = volume
        order['from'] = 'account'
        order['ts'] = time.time()
        return json.dumps(order)

    def loop_run(self):
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        listen_key = self.get_listen_key()
        self.live_subscribe(listen_key) 
        
        while True:
            recv = self.ws.recv()
            logger.info(recv)
            self.msg = json.loads(recv)
            self.producer.send('futures_all', key='account', value=recv)          
            if 'listenKeyExpired' in recv:
                listen_key = self.get_listen_key()
                self.live_subscribe(listen_key)
                continue
            if self.msg['e'] == 'ACCOUNT_UPDATE':
                info = self.account_update()
                self.producer.send('info', key=balance_token, value=info)
                
            if self.msg['e'] == 'ORDER_TRADE_UPDATE':
                o = self.msg['o']
                symbol = o['s']

                if o['x'] == 'TRADE' and o['X'] == 'FILLED' and 'open' in o['c']:
                    open_price = float(o['ap'])
                    if 'BUY' == o['S']:
                        side = 'SELL'
                        close_price = open_price * 1.01
                        stop_price = open_price * 0.99
                        self.redis.set(f'oneper_{symbol}_status', 'long')
                    else:
                        side = 'BUY'
                        close_price = open_price * 0.99
                        stop_price = open_price * 1.01
                        self.redis.set(f'oneper_{symbol}_status', 'short')

                    k = json.loads(self.redis.get(f'oneper_{symbol}_kline'))
                    
                    k['open_filled_price'] = open_price
                    self.redis.lpush(f'oneper_{symbol}_klines', json.dumps(k))
                    vol = float(o['q'])
                    
                    close_price = self.adjust_price(symbol, close_price )
                    close_order = self.get_order('close', symbol, side, close_price, vol)
                    self.producer.send('trade', key='oneper', value=close_order)

                    stop_price = self.adjust_price(symbol, stop_price)
                    stop_order = self.get_order('stop', symbol, side, stop_price, vol)
                    self.producer.send('trade', key='oneper', value=stop_order)

                if o['x'] == 'TRADE' and o['X'] == 'FILLED' and 'close' in o['c']:
                    self.redis.set(f'oneper_{symbol}_status', 'waiting')
                    delete_order = self.get_order('cancel', symbol)
                    self.producer.send('trade', key='oneper', value=delete_order)

                if o['x'] == 'EXPIRED' and 'stop' in o['c']:
                    self.redis.set(f'oneper_{symbol}_status', 'waiting')
                    delete_order = self.get_order('cancel', symbol)
                    self.producer.send('trade', key='oneper', value=delete_order)
                
                info = self.order_trade_update()
                self.producer.send('info', key=order_token, value=info)
                self.producer.send('info', key='feishu:'+fs_order_token, value=info)

                if '：成交' in info:
                    self.producer.send('info', key=filled_token, value=info)
                    self.producer.send('info', key='feishu:'+fs_filled_token, value=info)
            

def main():
    while True:
        try:
            instance = sub_account_info()
            instance.loop_run()
        except Exception as e:
            logger.error(e)


if __name__ == "__main__":
    main()
