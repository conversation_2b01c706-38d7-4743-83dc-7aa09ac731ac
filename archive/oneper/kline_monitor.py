import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import numpy as np
from datetime import datetime

from util import *

import sys
sys.path.append("../public")
from constant import *


logger = logging.getLogger("Monitor")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.kline_monitor')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class kline_monitor():
    def __init__(self):
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.threshold = 1
        self.symbol_set = set()
        
    def run(self):
        print('starts:')
        group_id = 'kline_monitor'
        consumer = KafkaConsumer('futures_kline',
            group_id = group_id,
            auto_offset_reset='latest', #earliest
            key_deserializer= bytes.decode,
            value_deserializer= bytes.decode,
            auto_commit_interval_ms=3000)

        producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
            key_serializer= str.encode,
            value_serializer= str.encode,
            compression_type='gzip')

        for msg in consumer:
            kline = json.loads(msg.value)
            if kline['i'] != '1m':
                continue

            co = round((kline['c'] / kline['o'] - 1) * 100, 2)
            hl = round((kline['h'] / kline['l'] - 1) * 100, 2)
            ho = round((kline['h'] / kline['o'] - 1) * 100, 2)
            lo = round((kline['l'] / kline['o'] - 1) * 100, 2)
            kline['co'] = co
            kline['ho'] = ho
            kline['lo'] = lo
            kline['hl'] = hl
            kline['z'] = kline['z'][:-4].replace('_',' ')
            if abs(co) > self.threshold and kline['s'] not in self.symbol_set:
                self.redis.lpush('futures_1per_kline', json.dumps(kline))
                self.symbol_set.add(kline['s'])
                logger.info(json.dumps(kline))
                text = ['涨跌通知：']
                text.append(f'交易对：{kline["s"][0:-4]}_1m')
                text.append(f'CO：{co}%')
                text.append(f'HO：{ho}%')
                text.append(f'LO：{lo}%')
                text.append(f'HL：{hl}%')
                text.append(f'开盘价：{kline["o"]}')
                text.append(f'最高价：{kline["h"]}')
                text.append(f'最低价：{kline["l"]}')
                text.append(f'收盘价：{kline["c"]}')
                text.append(f'交易额：{int(kline["q"]/1000)}k')
                text.append(f'时间：{kline["z"]}')
                weixin_info('\n'.join(text), baifenzhiyi_token)
                feishu_info('\n'.join(text), fs_baifenzhiyi_token)
            if kline['x']:
                if kline['s'] in self.symbol_set:
                    self.redis.lpush('futures_unusual_kline', json.dumps(kline))
                    self.symbol_set.discard(kline['s'])

            
    
if __name__ == "__main__":
    t = kline_monitor()
    t.run()
