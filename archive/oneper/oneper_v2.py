import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import numpy as np
from datetime import datetime
from decimal import Decimal

from util import *

import sys
sys.path.append("../public")
from constant import *

logger = logging.getLogger("Oneper")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.oneper_v2')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class oneper():
    def __init__(self):
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.down = 0.9
        self.up = 1.2
        self.callbackRate = 0.5
        self.take_trail_gap = 0.0015
        self.open_diff = 0.009

    def adjust_price(self, symbol, price):
        info = json.loads(self.redis.get('futures_' + symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'PRICE_FILTER':
                tickSize = float(flt['tickSize'])
                count = int(price / tickSize)
                return float(Decimal(str(count)) * Decimal(str(tickSize)))
    
    def adjust_vol(self, symbol, q):
        info = json.loads(self.redis.get(symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'MARKET_LOT_SIZE':
                step_size = float(flt['stepSize'])
        q = float(Decimal(str(int(q / step_size))) * Decimal(str(step_size)))
        return q
    
    def get_order(self, type, symbol, side, price, volume, o, co, callbackRate=None):
        order = {}
        order['type'] = type
        order['symbol'] = symbol
        order['side'] = side
        order['price'] = price
        order['volume'] = volume
        if callbackRate:
            order['callbackRate'] = callbackRate
        order['o'] = o
        order['co'] = co
        order['from'] = 'oneper'
        order['ts'] = time.time()
        return json.dumps(order)
        
    def run(self):
        print('starts:')
        group_id = 'oneper'
        consumer = KafkaConsumer('futures_kline',
            group_id = group_id,
            auto_offset_reset='latest', #earliest
            key_deserializer= bytes.decode,
            value_deserializer= bytes.decode,
            auto_commit_interval_ms=3000)

        producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
            key_serializer= str.encode,
            value_serializer= str.encode,
            compression_type='gzip')

        for msg in consumer:
            kline = json.loads(msg.value)
            if kline['i'] != '1m' or time.time() - kline['E']/1000 > 2:
                continue

            symbol = kline['s']
            o = kline['o']
            co = round((kline['c'] / o - 1) * 100, 2)
            kline['co'] = co
            status = self.redis.get(f'oneper_{symbol}_status')
            if status == None:
                status = 'free'
            diff = (kline['E'] - kline['t']) / 1000
            x = kline['x']
            if abs(co) >= self.down and abs(co) <= self.up and status in ['free','waiting'] and not x and diff < 58:
                #logger.info(json.dumps(kline))
                cnt = 0
                for s in futures_symbols:
                    if self.redis.get(f'oneper_{symbol}_status') != 'free':
                        cnt += 1
                if cnt > 30:
                    continue
                qty = 30
                if co > 0:
                    open_price = o * (1 + self.open_diff)
                    open_side = 'buy'
                    close_side = 'sell'
                else:
                    open_price = o * (1 - self.open_diff)
                    open_side = 'sell'
                    close_side = 'buy'
                open_price = self.adjust_price(symbol, open_price)
                volume = self.adjust_vol(symbol, qty/open_price)
                kline['side'] = open_side
                kline['open_price'] = open_price
                kline['volume'] = volume
                logger.info(json.dumps(kline))

                open_order = self.get_order('open', symbol, open_side, open_price, volume, o, co)

                producer.send('trade', key='oneper', value=open_order)

                self.redis.set(f'oneper_{symbol}_status', 'pending')
                self.redis.set(f'oneper_{symbol}_kline', json.dumps(kline))
            
            if x and status in ['pending']:
                self.redis.set(f'oneper_{symbol}_status', 'free')
                order = {}
                order['type'] = 'cancel'
                order['symbol'] = symbol
                order['o'] = o
                order['co'] = co
                order['from'] = 'oneper'
                order['ts'] = time.time()
                producer.send('trade', key='oneper', value=json.dumps(order))
            if x and status in ['waiting']:
                self.redis.set(f'oneper_{symbol}_status', 'free')

                


                
         
    
if __name__ == "__main__":
    t = oneper()
    t.run()
