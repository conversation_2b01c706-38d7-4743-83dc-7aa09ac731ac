import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Process,Queue
import pandas as pd

from binance.um_futures import UMFutures
from binance.lib.utils import config_logging
from binance.error import ClientError

from util import *

import sys
sys.path.append("../public")
from constant import *
from SpotApi import SpotApi
from FuturesApi import FuturesApi

logger = logging.getLogger("Order")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.order')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class order():
    def __init__(self, i):
        self.fapi = FuturesApi(api_key, api_secret)
        self.sapi = SpotApi(api_key, api_secret)
        self.client = UMFutures(key=api_key, secret=api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.indexes = ['o', 'h', 'l', 'c', 'n', 'v', 'q', 'V', 'Q', 't', 'T', 'E', 'co', 'ho', 'hl','z']
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        self.trade_symbols = wasee_symbols
        self.wasee_spot = wasee_spot
        self.wasee_margin = wasee_margin
        self.i = i
    
    
    def gererate_orderId(self, strategy, symbol):
        t = int(time.time()*1000)
        local_time = time.localtime(time.time())
        tstr = time.strftime("%y%m%d-%H_%M_%S_", local_time) + str(t)[-3:]
        return f'{strategy}_{symbol[:-4]}_{tstr}' 

    def batch_orders(self, orders):
        params = []
        for order in orders:
            _type = order['type']
            symbol = order['symbol']
            side = order['side'].upper()
            price = order['price']
            quantity = order['volume']
            orderId = self.gererate_orderId(_type, symbol)
            if _type == 'open':
                param = self.fapi.stop_market(symbol, side, price, quantity, orderId, get_params=True)
            if _type == 'take':
                param = self.fapi.take_profit_market(symbol, side, price, quantity, orderId, get_params=True)
            if _type == 'trail':
                param = self.fapi.trail_stop_market(side, symbol, price, quantity, order['callbackRate'], orderId, get_params=True)
            for key in param:
                param[key] = str(param[key])
            params.append(param)

        msg = self.client.new_batch_order(params)
        logger.info('group' + str(self.i) + ' - ' + json.dumps(msg))
        open_success = False if 'code' in msg[0] else True
        if not open_success and ('code' not in msg[1] or 'code' not in msg[2]):
            msg = self.fapi.delete_allOpenOrders(symbol)
            msg['symbol'] = symbol
            self.redis.set(f'oneper_{symbol}_status', 'free')
            logger.info('group' + str(self.i) + ' - ' + json.dumps(msg))


    def run(self):
        group_id = group + str(self.i)
        logger.info('group_id:' + group_id)
        consumer = KafkaConsumer(bootstrap_servers= ['localhost:9092'],
                                group_id= group_id,
                                auto_offset_reset='latest',
                                key_deserializer= bytes.decode, 
                                value_deserializer= bytes.decode)
        consumer.subscribe(topics= ['trade'])
        for msg in consumer:
            topic = msg.topic
            key = msg.key
            if key == 'oneper' and msg.offset % 30 == self.i:
                logger.info('group' + str(self.i) + ' - ' + msg.value)
                if time.time() - msg.timestamp/1000 > 4:
                    continue
                order = json.loads(msg.value)
                if type(order) == list:
                    self.batch_orders(order)
                    continue

                _type = order['type']
                symbol = order['symbol']
                side = order.get('side', '').upper()
                price = order.get('price', '')
                quantity = order.get('volume', '')

                orderId = self.gererate_orderId(_type, symbol)

                if _type == 'open':
                    msg = self.fapi.stop_market(symbol, side, price, quantity, orderId)

                if _type == 'take':
                    msg = self.fapi.take_profit_market(symbol, side, price, quantity, orderId)

                if _type == 'trail':
                    callbackRate = order['callbackRate']
                    msg = self.fapi.trail_stop_market(side, symbol, price, quantity, callbackRate, orderId)
                
                if _type == 'close':
                    msg = self.fapi.stop_market(symbol, side, price, quantity, orderId)
                
                if _type == 'cancel':
                    msg = self.fapi.delete_allOpenOrders(symbol)
                    msg['symbol'] = symbol

                logger.info('group' + str(self.i) + ' - ' + json.dumps(msg))
                #if 'code' in msg:
                #    self.redis.set(f'oneper_{symbol}_status', 'free')
                #    if msg['code'] == -1001:
                #        self.redis.set(f'oneper_{symbol}_status', 'free')
           

def main(q, i):
    while True:
        try:
            t = order(i)
            t.run()
        except Exception as e:
            weixin_info(str(e), warning_token)

if __name__ == "__main__":
    #t = shot()
    #t.init_order()
    #t.run()
    q = Queue()
    process_list = []
    num = 30
    for i in range(num):
        p = Process(target=main, args=(q, i,))
        p.start()
        process_list.append(p)
    for p in process_list:
        p.join()
    logger.info('over')
    weixin_info('over', warning_token)