# -*- coding: utf-8 -*-
import websocket
import time, json, os, sys
from websocket import create_connection
import pandas as pd
from kafka import KafkaProducer, KafkaConsumer
from multiprocessing import Process,Queue
import logging, schedule, redis

from util import *

import sys
sys.path.append("../public")
from SpotApi import SpotApi

logger = logging.getLogger("SmallGoal-Sub_account")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.account-margin')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)


class sub_account_info():
    def __init__(self):
        self.sapi = SpotApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self._x = {'NEW':'新订单', 'CANCELED':'已撤单', 'TRADE':'交易', 'EXPIRED':'订单失效', 'REJECTED':'新订单被拒绝'}
        self._X = {'NEW':'新订单', 'CANCELED':'已撤单', 'FILLED':'成交', 'PARTIALLY_FILLED':'部分成交', 'EXPIRED':'订单失效'}
        self._o = {'MARKET':'市价单', 'LIMIT':'限价单', 'STOP_MARKET':'市价止损单', 'TAKE_PROFIT_MARKET':'市价止盈单', 'STOP':'止损单', 'TAKE_PROFIT':'止盈单', 'LIQUIDATION':'强平单'}
        self.fid = ['pa','ep','cr','up','iw']
        self.info_list = []
        self.last_time = time.time()

    def get_listen_key(self):
        req = self.sapi.get_margin_listen_key()
        listen_key = req['listenKey']
        # logger.info('listen_key:' + listen_key )
        return listen_key
    
    def live_subscribe(self, listen_key):
        logger.info('margin listen_key:' + listen_key)
        self.redis.set(group + '_margin_listen_key', listen_key)
        params = [listen_key]
        self.streams = { "method": "SUBSCRIBE", "params": params, "id": ********}
        self.ws = create_connection("wss://stream.binance.com/ws")
        self.ws.send(json.dumps(self.streams))
        self.ws.recv()
    
    def account_update(self):
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        info = ['余额变更：']
        info.append('资产：' + self.msg['a'])
        info.append('变更量：' + self.msg['d'])
        info.append('事件时间：' + tstr) 
        return '\n'.join(info)
    
    def account_position(self):
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        info = ['杠杆账号通知：']
        #positions = {}
        for i in self.msg['B']:
            info.append('资产名称：' + i['a'])
            info.append('可用余额：' + i['f'])
            info.append('冻结余额：' + i['l'])
            info.append('--------------------')
            self.redis.set(group + '_margin_' + i['a'] + '_p', float(i['f']))
            self.redis.set(group + '_margin_' + i['a'] + '_l', float(i['l']))
            self.redis.set(group + '_margin_' + i['a'] + 'USDT_p', float(i['f']))
            self.redis.set(group + '_margin_' + i['a'] + 'USDT_l', float(i['l']))
            #positions[i['a']] = {'p':float(i['f']), 'l':float(i['l'])}
        #self.redis.set(group + '_spot_positions', json.dumps(positions))
        info.append('事件时间：' + tstr)
        return '\n'.join(info)

    def order_trade_update(self):
        #o = self.msg['o']
        o = self.msg
        if o['x'] == 'TRADE' and o['S'] == 'SELL' and group in o['c']:
            value = {'symbol':o['s'], 'q':float(o['l']), 'side':'buy'}
            self.producer.send('trade', key='wasee', value=json.dumps(value))

        if o['x'] == 'TRADE':
            self.redis.lpush(group + '_margin_order_trade_list', json.dumps(self.msg))
            #self.redis.lpush(group + '_spot_order_trade_day_list', json.dumps(self.msg))
        local_time = time.localtime(self.msg['E'] / 1000)
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        info = []
        info.append('订单通知：' + self._x[o['x']] + '-' + o['s'])
        info.append('订单状态：' + self._X[o['X']])
        # info.append('交易对：' + o['s'])
        info.append('订单方向：' + o['S'])
        #info.append('持仓方向：'  + o['ps'])
        info.append('订单类型：' + self._o[o['o']])
        info.append('原始价格：' + o['p'])

        info.append('原始数量：' + o['q'])
        if o['X'] != 'NEW':
            info.append('累计成交：' + o['Z'])
        #if o['o'] == 'STOP_MARKET':
        #    info.append('触发价格：' + o['sp'])
        if 'n' in o:
            info.append('手续费用：' + o['n'])
        if 'L' in o and float(o['L']) > 0:
            info.append('成交价格：' + o['L'])
        if 'N' in o and o['N'] is not None:
            info.append('手续费资产：' + o['N'])
        info.append('订单编号：' + str(o['i']))
        info.append('事件时间：' + tstr)
        return '\n'.join(info)

    def loop_run(self):
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        listen_key = self.get_listen_key()
        self.live_subscribe(listen_key) 
        
        while True:
            recv = self.ws.recv()
            logger.info(recv)
            self.msg = json.loads(recv)
            self.producer.send('spot_all', key='account', value=recv)          
            if 'listenKeyExpired' in recv:
                listen_key = self.get_listen_key()
                self.live_subscribe(listen_key)
                continue
            if self.msg['e'] == 'balanceUpdate':
                info = self.account_update()
                #weixin_info(info, balance_token)
                self.producer.send('info', key=balance_token, value=info)
            
            if self.msg['e'] == 'outboundAccountPosition':
                info = self.account_position()
                #weixin_info(info, balance_token)
                self.producer.send('info', key=balance_token, value=info)
                
            if self.msg['e'] == 'executionReport':
                info = self.order_trade_update()
                self.producer.send('info', key=order_token, value=info)
                #weixin_info(info, order_token)
                if 'TRADE' in recv:
                    #weixin_info(info, filled_token)
                    #weixin_info(info, warning_token)
                    self.producer.send('info', key=filled_token, value=info)
                    self.producer.send('info', key=warning_token, value=info)

            

def main():
    while True:
        try:
            instance = sub_account_info()
            instance.loop_run()
        except Exception as e:
            logger.error(e)


if __name__ == "__main__":
    main()
