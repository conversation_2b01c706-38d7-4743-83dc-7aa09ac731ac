import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import numpy as np
from datetime import datetime

from util import *

import sys
sys.path.append("../public")
from constant import *


logger = logging.getLogger("SmallGoal-monitor")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.monitor')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class one_monitor():
    def __init__(self):
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.threshold = 1
        self.ho_th = 5
        self.lo_th = -5
        self.band = 0.005
        
    def run(self):
        print('starts:')
        group_id = 'ones'
        consumer = KafkaConsumer('spot_kline',
            group_id = group_id,
            auto_offset_reset='latest', #earliest
            key_deserializer= bytes.decode,
            value_deserializer= bytes.decode,
            auto_commit_interval_ms=3000)

        producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
            key_serializer= str.encode,
            value_serializer= str.encode,
            compression_type='gzip')

        for msg in consumer:
            kline = json.loads(msg.value)
            if kline['i'] != '1s':
                continue
            if kline['s'] in wasee_symbols:
                symbol = kline['s']
                self.redis.lpush(f'spot_{symbol}_1s', msg.value)

                base_price = float(self.redis.get(f'spot_{symbol}_base_price'))
                price = kline['c']
                if abs((price - base_price) / base_price) > self.band:
                    value = {}
                    value['symbol'] = symbol
                    value['side'] = 'sell'
                    value['base_price'] = base_price
                    value['price'] = price
                    value['time'] = time.time()
                    producer.send('trade', key='wasee', value=json.dumps(value))
                    logger.info(json.dumps(value))

            co = round((kline['c'] / kline['o'] - 1) * 100, 2)
            hl = round((kline['h'] / kline['l'] - 1) * 100, 2)
            ho = round((kline['h'] / kline['o'] - 1) * 100, 2)
            lo = round((kline['l'] / kline['o'] - 1) * 100, 2)
            if abs(co) > self.threshold or ho > self.ho_th or lo < self.lo_th:
                kline['co'] = co
                kline['ho'] = ho
                kline['lo'] = lo
                kline['hl'] = hl
                logger.info(json.dumps(kline))
                text = ['异常涨跌通知：']
                text.append(f'交易对：{kline["s"][0:-4]}_1s')
                text.append(f'CO：{co}%')
                text.append(f'HO：{ho}%')
                text.append(f'LO：{lo}%')
                text.append(f'HL：{hl}%')
                text.append(f'开盘价：{kline["o"]}')
                text.append(f'最高价：{kline["h"]}')
                text.append(f'最低价：{kline["l"]}')
                text.append(f'收盘价：{kline["c"]}')
                text.append(f'交易额：{int(kline["q"]/1000)}k')
                text.append(f'开始时间：{kline["z"]}')
                weixin_info('\n'.join(text), wave_token)
                if ho > self.ho_th or lo < self.lo_th:
                    weixin_info('\n'.join(text), warning_token)

            
    
if __name__ == "__main__":
    t = one_monitor()
    t.run()
