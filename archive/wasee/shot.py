import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Process,Queue
import pandas as pd

from util import *

import sys
sys.path.append("/root/workspace/trade/public")
from constant import *
from FuturesApi import FuturesApi
from SpotApi import SpotApi

logger = logging.getLogger("SmallGoal-shot")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.shot')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class shot():
    def __init__(self):
        self.fapi = FuturesApi(api_key, api_secret)
        self.sapi = SpotApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.indexes = ['o', 'h', 'l', 'c', 'n', 'v', 'q', 'V', 'Q', 't', 'T', 'E', 'co', 'ho', 'hl','z']
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        self.trade_symbols = wasee_symbols
        self.wasee_spot = wasee_spot
        self.wasee_margin = wasee_margin
        self.up = 1.055
        self.band = 0.008
    
    def adjust_price(self, symbol, price):
        info = json.loads(self.redis.get('spot_' + symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'PRICE_FILTER':
                tickSize = float(flt['tickSize'])
                return int(price / tickSize) * tickSize
    

    def get_bid(self, symbol):
        depth = json.loads(self.redis.get('futures_' + symbol + '_depthUpdate'))
        gap = time.time() - depth['E']/1000
        if gap < 5:
            bid = depth['b'][0][0] 
        else:
            depth = self.fapi.get_depth(symbol, 5)
            bid = float(depth['bids'][0][0])
            logger.info(f'{symbol} get depthUpdate delay: {gap} seconds')
        return bid

    def gererate_orderId(self, strategy, symbol):
        t = int(time.time()*1000)
        local_time = time.localtime(time.time())
        tstr = time.strftime("%y%m%d-%H_%M_%S-", local_time) + str(t)[-3:]
        return f'{strategy}_{symbol[:-4]}_{tstr}'

    def init_order(self):
        base_prices = {}
        for symbol in self.trade_symbols:
            positions = json.loads(self.redis.get(group + '_futures_' +symbol + '_p'))
            pa = abs(positions['pa'])
            price = self.get_bid(symbol)
            base_prices[symbol] = price
            self.redis.set(f'spot_{symbol}_base_price', price)
            price = self.adjust_price(symbol, price * self.up)
            newClientOrderId = self.gererate_orderId(group, symbol)
            if symbol in self.wasee_spot:
                self.sapi.delete_openOrders_spot(symbol)
                msg = self.sapi.sell_limit_spot(symbol, pa, price, newClientOrderId)
            else:
                self.sapi.delete_openOrders(symbol)
                msg = self.sapi.sell_limit(symbol, pa, price, newClientOrderId)
            if 'code' in msg:
                msg['symbol'] = symbol
                weixin_info(json.dumps(msg), warning_token)
            logger.info(json.dumps(msg))
        self.redis.set(group + '_base_prices', json.dumps(base_prices))

    def re_order(self, symbol):
        positions = json.loads(self.redis.get(group + '_futures_' +symbol + '_p'))
        base_prices = json.loads(self.redis.get(group + '_base_prices'))
        pa = abs(positions['pa'])
        price = self.get_bid(symbol)
        base_prices[symbol] = price
        self.redis.set(f'spot_{symbol}_base_price', price)
        price = self.adjust_price(symbol, price * self.up)
        newClientOrderId = self.gererate_orderId(group, symbol)
        if symbol in self.wasee_spot:
            self.sapi.delete_openOrders_spot(symbol)
            msg = self.sapi.sell_limit_spot(symbol, pa, price, newClientOrderId)
        else:
            self.sapi.delete_openOrders(symbol)
            msg = self.sapi.sell_limit(symbol, pa, price, newClientOrderId)
        self.redis.set(group + '_base_prices', json.dumps(base_prices))
        if 'code' in msg:
            msg['symbol'] = symbol
            weixin_info(json.dumps(msg), warning_token)
        logger.info(json.dumps(msg))
    
    def put_listen_key(self):
        listen_key = self.redis.get(group + '_spot_listen_key')
        msg = self.sapi.put_spot_listen_key(listen_key)
        logger.info(json.dumps(msg))

        listen_key = self.redis.get(group + '_margin_listen_key')
        msg = self.sapi.put_margin_listen_key(listen_key)
        logger.info(json.dumps(msg))

    def run(self):
        producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        local_time = time.localtime(time.time())
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        minute = int(time.strftime("%M", local_time))
        sec = int(time.strftime("%S", local_time))
        if minute in [0, 20, 40] and sec < 10:
            self.put_listen_key()
        
        return
        for symbol in self.trade_symbols:
            base_price = float(self.redis.get(f'spot_{symbol}_base_price'))
            price = self.get_bid(symbol)
            if abs((price - base_price) / base_price) > self.band:
                value = {'symbol': symbol, 'side':'sell', 'base_price':base_price, 'price':price}
                producer.send('trade', key='wasee', value=json.dumps(value))
                logger.info(json.dumps(value))
                #self.re_order(symbol)


if __name__ == "__main__":
    t = shot()
    #t.init_order()
    t.run()