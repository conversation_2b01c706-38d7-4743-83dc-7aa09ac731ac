import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Process,Queue
import pandas as pd

from util import *

import sys
sys.path.append("../public")
from constant import *
from SpotApi import SpotApi
from FuturesApi import FuturesApi

logger = logging.getLogger("SmallGoal-shot")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.wasee')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class wasee():
    def __init__(self, i):
        self.fapi = FuturesApi(api_key, api_secret)
        self.sapi = SpotApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.indexes = ['o', 'h', 'l', 'c', 'n', 'v', 'q', 'V', 'Q', 't', 'T', 'E', 'co', 'ho', 'hl','z']
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        self.trade_symbols = wasee_symbols
        self.wasee_spot = wasee_spot
        self.wasee_margin = wasee_margin
        self.i = i
        self.up = 1.055
    
    def adjust_price(self, symbol, price):
        info = json.loads(self.redis.get('spot_' + symbol + '_info'))
        filters = info['filters']
        for flt in filters:
            if flt['filterType'] == 'PRICE_FILTER':
                tickSize = float(flt['tickSize'])
                return int(price / tickSize) * tickSize
    
    def gererate_orderId(self, strategy, symbol):
        t = int(time.time()*1000)
        local_time = time.localtime(time.time())
        tstr = time.strftime("%y%m%d-%H_%M_%S-", local_time) + str(t)[-3:]
        return f'{strategy}_{symbol[:-4]}_{tstr}'
    
    def set_order(self, symbol):
        positions = json.loads(self.redis.get(group + '_futures_' +symbol + '_p'))
        pa = abs(positions['pa'])
        #price = self.get_bid(symbol)
        kline = json.loads(self.redis.lindex(f'spot_{symbol}_1s', 0))
        price = kline['c']
        self.redis.set(f'spot_{symbol}_base_price', price)
        price = self.adjust_price(symbol, price * self.up)
        newClientOrderId = self.gererate_orderId(group, symbol)
        if symbol in self.wasee_spot:
            self.sapi.delete_openOrders_spot(symbol)
            msg = self.sapi.sell_limit_spot(symbol, pa, price, newClientOrderId)
        else:
            self.sapi.delete_openOrders(symbol)
            msg = self.sapi.sell_limit(symbol, pa, price, newClientOrderId)
        msg['base_price'] = kline['c']
        if 'code' in msg:
            msg['symbol'] = symbol
            #weixin_info(json.dumps(msg), warning_token)
            self.producer.send('info', key=warning_token, value=json.dumps(msg))
        logger.info(json.dumps(msg))

    def buy_market(self, symbol, quantity):
        if symbol in self.wasee_spot:
            msg = self.sapi.buy_market_spot(symbol, quantity)
        else:
            msg = self.sapi.buy_market(symbol, quantity)
        if 'code' in msg:
            msg['symbol'] = symbol
            #weixin_info(json.dumps(msg), warning_token)
            self.producer.send('info', key=warning_token, value=json.dumps(msg))
        else:
            self.set_order(symbol)
        logger.info(json.dumps(msg))

            

    def run(self):
        group_id = group + str(self.i)
        logger.info('group_id:' + group_id)
        consumer = KafkaConsumer(bootstrap_servers= ['localhost:9092'],
                                group_id= group_id,
                                auto_offset_reset='latest',
                                key_deserializer= bytes.decode, 
                                value_deserializer= bytes.decode)
        consumer.subscribe(topics= ['trade'])
        for msg in consumer:
            topic = msg.topic
            key = msg.key
            if key == 'wasee' and msg.offset % 15 == self.i:
                logger.info(msg.value)
                if time.time() - msg.timestamp/1000 > 120:
                    continue
                value = json.loads(msg.value)
                if value['side'] == 'buy':
                    if time.time() - msg.timestamp/1000 < 0.5:
                        time.sleep(0.1)
                    symbol = value['symbol']
                    quantity = value['q']
                    self.buy_market(symbol, quantity)
                if value['side'] == 'sell':
                    symbol = value['symbol']
                    self.set_order(symbol)
                

def main(q, i):
    while True:
        try:
            t = wasee(i)
            t.run()
        except Exception as e:
            weixin_info(str(e), warning_token)

if __name__ == "__main__":
    #t = shot()
    #t.init_order()
    #t.run()
    q = Queue()
    process_list = []
    num = 15
    for i in range(num):
        p = Process(target=main, args=(q, i,))
        p.start()
        process_list.append(p)
    for p in process_list:
        p.join()
    logger.info('over')
    weixin_info('over', warning_token)