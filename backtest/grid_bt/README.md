# DOGEUSDT 网格策略回测系统

这是一个基于币安合约数据的DOGEUSDT网格交易策略回测系统。

## 功能特点

- 自动下载币安DOGEUSDT合约历史数据（最近半年）
- 支持等比网格和等差网格模式
- 完整的回测引擎，包含手续费和滑点计算
- 详细的交易记录和收益分析
- 可视化图表展示回测结果

## 网格策略参数

基于提供的币安网格配置：

- 价格区间：0.10 - 0.30 USDT
- 网格数量：100
- 投资金额：200 USDT
- 网格模式：等比
- 杠杆：40x
- 手续费率：0.04%

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行主程序：
```bash
python main.py
```

2. 系统将自动：
   - 下载最新的DOGEUSDT合约数据
   - 运行网格策略回测
   - 生成详细的回测报告
   - 保存交易记录和图表

## 文件结构

- `main.py` - 主程序入口
- `config.py` - 配置文件
- `data_downloader.py` - 数据下载模块
- `grid_strategy.py` - 网格策略核心逻辑
- `backtest_engine.py` - 回测引擎
- `data/` - 数据存储目录
- `results/` - 回测结果存储目录

## 回测结果

回测完成后将生成：

1. 详细的文字报告（控制台输出）
2. 交易记录CSV文件
3. 组合价值变化CSV文件
4. 可视化图表PNG文件

## 参数调整

可在 `config.py` 文件中调整以下参数：

- 网格价格区间
- 网格数量
- 投资金额
- 网格模式（等比/等差）
- 手续费率
- 数据时间范围

## 注意事项

- 请确保网格价格区间覆盖历史价格波动范围
- 网格数量越多，交易频率越高，但单笔利润越小
- 等比网格适合价格波动较大的市场
- 回测结果仅供参考，实际交易需考虑更多因素 