"""
网格策略回测引擎
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List
import os
from datetime import datetime

from grid_strategy import BinanceGridStrategy
from config import GRID_CONFIG, PATHS


class BacktestEngine:
    def __init__(self, strategy: BinanceGridStrategy = None, backtest_config: dict = None):
        """
        初始化回测引擎
        
        Args:
            strategy: 币安风格网格策略实例，如果为None则使用默认配置创建
            backtest_config: 回测配置
        """
        if strategy is None:
            self.strategy = BinanceGridStrategy(
                price_upper=GRID_CONFIG["price_upper"],
                price_lower=GRID_CONFIG["price_lower"],
                grid_num=GRID_CONFIG["grid_num"],
                investment_amount=GRID_CONFIG["investment_amount"],
                grid_mode=GRID_CONFIG["grid_mode"]
            )
        else:
            self.strategy = strategy
        
        self.backtest_config = backtest_config or {}
        
        # 创建结果目录
        os.makedirs(PATHS["results_dir"], exist_ok=True)
    
    def run_backtest(self, data: pd.DataFrame = None):
        """运行回测"""
        if data is not None:
            self.data = data
        
        # 根据配置过滤时间范围
        self.data = self._filter_data_by_time_range(self.data)
        
        print("开始回测...")
        print(f"回测数据时间范围: {self.data.index[0]} 到 {self.data.index[-1]}")
        print(f"回测数据条数: {len(self.data)}")
        
        all_trades = []
        portfolio_values = []
        
        for i, (timestamp, row) in enumerate(self.data.iterrows()):
            current_price = row['close']
            
            # 处理价格变化
            trades = self.strategy.process_price(current_price, timestamp)
            all_trades.extend(trades)
            
            # 记录组合价值
            stats = self.strategy.get_strategy_stats(current_price)
            portfolio_values.append({
                'timestamp': timestamp,
                'price': current_price,
                'total_value': stats['total_value'],
                'cash': stats['current_cash'],
                'position_value': stats['total_value'] - stats['current_cash'],  # 计算持仓价值
                'unrealized_pnl': stats['unrealized_pnl']
            })
            
            # 打印进度
            if i % 5000 == 0:
                print(f"回测进度: {i}/{len(self.data)} ({i/len(self.data)*100:.1f}%)")
        
        # 计算最终统计数据
        final_price = self.data['close'].iloc[-1]
        final_stats = self.strategy.get_strategy_stats(final_price)
        
        # 转换为DataFrame格式
        portfolio_df = pd.DataFrame(portfolio_values)
        trades_df = pd.DataFrame(all_trades) if all_trades else None
        
        print("回测完成！")
        return portfolio_df, trades_df
    
    def generate_report_v2(self, portfolio_df: pd.DataFrame, trades_df: pd.DataFrame):
        """生成回测报告 (新版本，接受DataFrame输入)"""
        print("\n" + "="*60)
        print("网格策略回测报告")
        print("="*60)
        
        # 基本信息
        print(f"交易对: DOGEUSDT")
        print(f"网格数量: {self.strategy.grid_num}")
        print(f"价格区间: {self.strategy.price_lower:.6f} - {self.strategy.price_upper:.6f} USDT")
        print(f"投资金额: {self.strategy.investment_amount:.2f} USDT")
        print(f"网格模式: {self.strategy.grid_mode}")
        print(f"交易模式: 币安风格配对交易")
        
        # 计算关键指标
        initial_value = portfolio_df['total_value'].iloc[0]
        final_value = portfolio_df['total_value'].iloc[-1]
        total_return = (final_value - initial_value) / initial_value * 100
        
        # 交易统计
        print(f"\n交易统计:")
        total_trades = len(trades_df) if trades_df is not None else 0
        print(f"总交易次数: {total_trades}")
        
        if trades_df is not None and len(trades_df) > 0:
            buy_trades = trades_df[trades_df['type'] == 'BUY']
            sell_trades = trades_df[trades_df['type'] == 'SELL']
            
            print(f"买入次数: {len(buy_trades)}")
            print(f"卖出次数: {len(sell_trades)}")
            
            if len(sell_trades) > 0:
                avg_profit_per_trade = sell_trades['profit'].mean()
                print(f"平均每笔交易利润: {avg_profit_per_trade:.4f} USDT")
                total_realized_profit = sell_trades['profit'].sum()
                print(f"已实现利润: {total_realized_profit:.2f} USDT")
        
        # 收益统计
        print(f"\n收益统计:")
        print(f"初始投资: {initial_value:.2f} USDT")
        print(f"最终资产: {final_value:.2f} USDT")
        print(f"总收益: {final_value - initial_value:.2f} USDT")
        print(f"收益率: {total_return:.2f}%")
        
        # 计算已实现和未实现盈亏
        total_realized_profit = 0
        if trades_df is not None and len(trades_df) > 0:
            sell_trades = trades_df[trades_df['type'] == 'SELL']
            if len(sell_trades) > 0:
                total_realized_profit = sell_trades['profit'].sum()
        
        unrealized_pnl = portfolio_df['unrealized_pnl'].iloc[-1]
        print(f"已实现盈亏: {total_realized_profit:.2f} USDT")
        print(f"未实现盈亏: {unrealized_pnl:.2f} USDT")
        
        # 风险指标
        peak = portfolio_df['total_value'].expanding().max()
        drawdown = (portfolio_df['total_value'] - peak) / peak * 100
        max_drawdown = abs(drawdown.min())
        print(f"最大回撤: {max_drawdown:.2f}%")
        
        # 资金状况详情
        print(f"\n=== 最终资金状况 ===")
        final_cash = portfolio_df['cash'].iloc[-1]
        final_position_value = portfolio_df['position_value'].iloc[-1]
        final_total = final_cash + final_position_value
        
        print(f"剩余现金: {final_cash:.2f} USDT ({final_cash/final_total*100:.1f}%)")
        print(f"持仓价值: {final_position_value:.2f} USDT ({final_position_value/final_total*100:.1f}%)")
        print(f"总资产价值: {final_total:.2f} USDT")
        
        # 整体盈亏分析
        print(f"\n=== 整体盈亏分析 ===")
        total_pnl = final_total - initial_value
        print(f"投入本金: {initial_value:.2f} USDT")
        print(f"当前总值: {final_total:.2f} USDT")
        print(f"净盈亏: {total_pnl:.2f} USDT")
        if total_pnl >= 0:
            print(f"盈利状态: 📈 盈利 {abs(total_pnl):.2f} USDT ({total_return:.2f}%)")
        else:
            print(f"亏损状态: 📉 亏损 {abs(total_pnl):.2f} USDT ({total_return:.2f}%)")
        
        # 资金利用率和持仓详情
        final_stats = self.strategy.get_strategy_stats(portfolio_df['price'].iloc[-1])
        total_positions = final_stats['total_position_count']
        if total_positions > 0:
            margin_used = total_positions * self.strategy.per_grid_amount
            margin_utilization = margin_used / initial_value * 100
            print(f"保证金利用率: {margin_utilization:.1f}%")
            print(f"当前持仓网格数: {total_positions} 个")
            print(f"  - 多头持仓: {final_stats['long_position_count']} 个")
            print(f"  - 空头持仓: {final_stats['short_position_count']} 个")
            print(f"  - 待执行订单: {final_stats['pending_orders_count']} 个")
        
        # 最近交易
        if trades_df is not None and len(trades_df) > 0:
            print(f"\n最近10笔交易:")
            recent_trades = trades_df.tail(10)[['timestamp', 'type', 'price', 'amount', 'profit']]
            print(recent_trades.to_string(index=False))
        
        # 生成图表和保存数据
        self._save_results(portfolio_df, trades_df)
        
        return {
            'total_return': total_return,
            'final_value': final_value,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades
        }
    
    def _save_results(self, portfolio_df: pd.DataFrame, trades_df: pd.DataFrame):
        """保存回测结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存组合价值数据
        portfolio_file = f"{PATHS['results_dir']}/portfolio_{timestamp}.csv"
        portfolio_df.to_csv(portfolio_file, index=False)
        print(f"\n组合数据已保存到: {portfolio_file}")
        
        # 保存交易记录
        if trades_df is not None and len(trades_df) > 0:
            trades_file = f"{PATHS['results_dir']}/trades_{timestamp}.csv"
            trades_df.to_csv(trades_file, index=False)
            print(f"交易记录已保存到: {trades_file}")
        
        # 生成图表
        self._plot_results_v2(portfolio_df, trades_df, timestamp)
    
    def generate_report(self, all_trades: List[Dict], portfolio_values: List[Dict], 
                       final_stats: Dict):
        """生成回测报告"""
        print("\n" + "="*60)
        print("网格策略回测报告")
        print("="*60)
        
        # 基本信息
        print(f"交易对: DOGEUSDT")
        print(f"网格数量: {GRID_CONFIG['grid_num']}")
        print(f"价格区间: {GRID_CONFIG['price_lower']:.6f} - {GRID_CONFIG['price_upper']:.6f} USDT")
        print(f"投资金额: {GRID_CONFIG['investment_amount']:.2f} USDT")
        print(f"网格模式: {GRID_CONFIG['grid_mode']}")
        
        # 交易统计
        print(f"\n交易统计:")
        print(f"总交易次数: {final_stats['trade_count']}")
        
        buy_trades = [t for t in all_trades if t['type'] == 'BUY']
        sell_trades = [t for t in all_trades if t['type'] == 'SELL']
        
        print(f"买入次数: {len(buy_trades)}")
        print(f"卖出次数: {len(sell_trades)}")
        
        if sell_trades:
            avg_profit_per_trade = sum(t.get('profit', 0) for t in sell_trades) / len(sell_trades)
            print(f"平均每笔交易利润: {avg_profit_per_trade:.4f} USDT")
        
        # 收益统计
        print(f"\n收益统计:")
        print(f"初始投资: {final_stats['initial_investment']:.2f} USDT")
        print(f"最终资产: {final_stats['total_value']:.2f} USDT")
        print(f"总收益: {final_stats['unrealized_pnl']:.2f} USDT")
        print(f"收益率: {final_stats['total_return']:.2f}%")
        print(f"已实现利润: {final_stats['realized_profit']:.2f} USDT")
        
        # 当前持仓
        print(f"\n当前状态:")
        print(f"现金余额: {final_stats['current_cash']:.2f} USDT")
        print(f"持仓数量: {final_stats['position_count']}")
        print(f"持仓价值: {final_stats['position_value']:.2f} USDT")
        
        # 最近10笔交易
        print(f"\n最近10笔交易:")
        recent_trades = all_trades[-10:] if len(all_trades) >= 10 else all_trades
        
        for trade in recent_trades:
            timestamp = trade['timestamp']
            trade_type = trade['type']
            price = trade['price']
            amount = trade['amount']
            profit = trade.get('profit', 0)
            
            if trade_type == 'BUY':
                print(f"[{timestamp}] 买入 - 价格: {price:.6f}, 金额: {amount:.2f} USDT")
            else:
                print(f"[{timestamp}] 卖出 - 价格: {price:.6f}, 金额: {amount:.2f} USDT, 利润: {profit:.4f} USDT")
        
        # 保存详细交易记录
        if all_trades:
            trades_df = pd.DataFrame(all_trades)
            trades_file = f"{PATHS['results_dir']}/trades_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            trades_df.to_csv(trades_file, index=False)
            print(f"\n详细交易记录已保存到: {trades_file}")
        
        # 保存组合价值变化
        portfolio_df = pd.DataFrame(portfolio_values)
        portfolio_file = f"{PATHS['results_dir']}/portfolio_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        portfolio_df.to_csv(portfolio_file, index=False)
        print(f"组合价值变化已保存到: {portfolio_file}")
        
        # 绘制图表
        self._plot_results(portfolio_df, all_trades)
        
        return {
            'trades_df': trades_df if all_trades else None,
            'portfolio_df': portfolio_df,
            'final_stats': final_stats
        }
    
    def _plot_results(self, portfolio_df: pd.DataFrame, all_trades: List[Dict]):
        """绘制回测结果图表"""
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 价格走势和交易点
        ax1.plot(self.data.index, self.data['close'], label='DOGEUSDT价格', alpha=0.7)
        
        # 标记买卖点
        if all_trades:
            buy_trades = [t for t in all_trades if t['type'] == 'BUY']
            sell_trades = [t for t in all_trades if t['type'] == 'SELL']
            
            if buy_trades:
                buy_times = [pd.to_datetime(t['timestamp']) for t in buy_trades]
                buy_prices = [t['price'] for t in buy_trades]
                ax1.scatter(buy_times, buy_prices, color='green', marker='^', s=30, label='买入', alpha=0.7)
            
            if sell_trades:
                sell_times = [pd.to_datetime(t['timestamp']) for t in sell_trades]
                sell_prices = [t['price'] for t in sell_trades]
                ax1.scatter(sell_times, sell_prices, color='red', marker='v', s=30, label='卖出', alpha=0.7)
        
        # 网格线
        for price in self.strategy.grid_prices[::10]:  # 每10条网格线显示一条
            ax1.axhline(y=price, color='gray', linestyle='--', alpha=0.3)
        
        ax1.set_title('DOGEUSDT价格走势与交易记录')
        ax1.set_ylabel('价格 (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 组合价值变化
        portfolio_times = pd.to_datetime(portfolio_df['timestamp'])
        ax2.plot(portfolio_times, portfolio_df['total_value'], label='总资产价值', color='blue')
        ax2.axhline(y=GRID_CONFIG['investment_amount'], color='red', linestyle='--', label='初始投资')
        ax2.set_title('投资组合价值变化')
        ax2.set_ylabel('价值 (USDT)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 现金和持仓价值
        ax3.plot(portfolio_times, portfolio_df['cash'], label='现金', color='green')
        ax3.plot(portfolio_times, portfolio_df['position_value'], label='持仓价值', color='orange')
        ax3.set_title('现金 vs 持仓价值')
        ax3.set_ylabel('价值 (USDT)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 累计盈亏
        ax4.plot(portfolio_times, portfolio_df['unrealized_pnl'], label='累计盈亏', color='purple')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax4.set_title('累计盈亏')
        ax4.set_ylabel('盈亏 (USDT)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = f"{PATHS['results_dir']}/backtest_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"回测图表已保存到: {chart_file}")
        
        plt.show()
    
    def _plot_results_v2(self, portfolio_df: pd.DataFrame, trades_df: pd.DataFrame, timestamp: str):
        """绘制回测结果图表 (新版本)"""
        import matplotlib
        matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 价格走势和交易点
        if hasattr(self, 'data') and self.data is not None:
            ax1.plot(self.data.index, self.data['close'], label='DOGEUSDT价格', alpha=0.7)
        else:
            # 如果没有原始价格数据，从组合数据中获取
            ax1.plot(portfolio_df.index, portfolio_df['price'], label='DOGEUSDT价格', alpha=0.7)
        
        # 标记买卖点
        if trades_df is not None and len(trades_df) > 0:
            buy_trades = trades_df[trades_df['type'] == 'BUY']
            sell_trades = trades_df[trades_df['type'] == 'SELL']
            
            if len(buy_trades) > 0:
                buy_times = pd.to_datetime(buy_trades['timestamp'])
                ax1.scatter(buy_times, buy_trades['price'], color='red', marker='^', 
                           s=30, alpha=0.7, label='买入')
            
            if len(sell_trades) > 0:
                sell_times = pd.to_datetime(sell_trades['timestamp'])
                ax1.scatter(sell_times, sell_trades['price'], color='green', marker='v', 
                           s=30, alpha=0.7, label='卖出')
        
        ax1.set_title('DOGEUSDT价格走势与交易记录')
        ax1.set_ylabel('价格 (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 投资组合价值变化
        portfolio_times = pd.to_datetime(portfolio_df['timestamp'])
        ax2.plot(portfolio_times, portfolio_df['total_value'], label='总资产价值', color='blue')
        ax2.axhline(y=self.strategy.investment_amount, color='red', linestyle='--', 
                   alpha=0.5, label='初始投资')
        ax2.set_title('投资组合价值变化')
        ax2.set_ylabel('价值 (USDT)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 现金 vs 持仓价值
        ax3.plot(portfolio_times, portfolio_df['cash'], label='现金', color='green')
        ax3.plot(portfolio_times, portfolio_df['position_value'], label='持仓价值', color='orange')
        ax3.set_title('现金 vs 持仓价值')
        ax3.set_ylabel('价值 (USDT)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 剩余资金变化（累计盈亏）
        if trades_df is not None and len(trades_df) > 0 and 'remaining_funds' in trades_df.columns:
            # 使用交易记录中的剩余资金数据
            trade_times = pd.to_datetime(trades_df['timestamp'])
            remaining_funds_pnl = trades_df['remaining_funds'] - self.strategy.investment_amount
            ax4.plot(trade_times, remaining_funds_pnl, label='剩余资金盈亏', color='purple', marker='o', markersize=2)
            ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5, label='盈亏平衡线')
            ax4.set_title('剩余资金盈亏变化')
            ax4.set_ylabel('盈亏 (USDT)')
            ax4.legend()
        else:
            # 如果没有交易记录，使用原来的方式
            ax4.plot(portfolio_times, portfolio_df['unrealized_pnl'], label='累计盈亏', color='purple')
            ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            ax4.set_title('累计盈亏')
            ax4.set_ylabel('盈亏 (USDT)')
            ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = f"{PATHS['results_dir']}/backtest_chart_{timestamp}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"回测图表已保存到: {chart_file}")
        
        plt.show()
    
    def _filter_data_by_time_range(self, data: pd.DataFrame) -> pd.DataFrame:
        """根据配置的时间范围过滤数据"""
        from config import BACKTEST_CONFIG
        
        # 获取配置的时间范围
        start_date = BACKTEST_CONFIG.get("start_date")
        end_date = BACKTEST_CONFIG.get("end_date")
        start_time = BACKTEST_CONFIG.get("start_time", "00:00:00")
        end_time = BACKTEST_CONFIG.get("end_time", "23:59:59")
        
        if not start_date and not end_date:
            print("未配置时间范围，使用全部数据")
            return data
        
        original_count = len(data)
        
        try:
            # 构建完整的开始和结束时间
            if start_date:
                start_datetime = pd.to_datetime(f"{start_date} {start_time}")
                data = data[data.index >= start_datetime]
                print(f"应用开始时间过滤: >= {start_datetime}")
            
            if end_date:
                end_datetime = pd.to_datetime(f"{end_date} {end_time}")
                data = data[data.index <= end_datetime]
                print(f"应用结束时间过滤: <= {end_datetime}")
            
            filtered_count = len(data)
            
            if filtered_count == 0:
                print("⚠️ 警告: 时间过滤后数据为空，请检查时间范围配置")
                return data
            
            print(f"时间过滤: {original_count} -> {filtered_count} 条记录")
            print(f"过滤后时间范围: {data.index[0]} 到 {data.index[-1]}")
            
        except Exception as e:
            print(f"时间过滤出错: {e}")
            print("使用原始数据")
        
        return data 