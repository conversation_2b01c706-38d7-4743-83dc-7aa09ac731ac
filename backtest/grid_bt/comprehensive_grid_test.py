"""
全面的网格数量优化测试
"""

import pandas as pd
import numpy as np
from grid_strategy import BinanceGridStrategy
from backtest_engine import BacktestEngine
from data_downloader import DataDownloader
from config import GRID_CONFIG, BACKTEST_CONFIG
from copy import deepcopy
import matplotlib.pyplot as plt
import matplotlib
from datetime import datetime

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def comprehensive_grid_optimization():
    """全面的网格数量优化"""
    
    # 加载数据（使用前10000条数据，约一周的数据）
    downloader = DataDownloader()
    data = downloader.load_data()
    if data is None:
        print("无法加载数据")
        return
    
    # 使用前10000条数据进行测试
    test_data = data.head(10000)
    print(f"使用测试数据: {len(test_data)} 条记录")
    print(f"价格范围: {test_data['low'].min():.6f} - {test_data['high'].max():.6f}")
    
    # 测试更多的网格数量
    grid_counts = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 120, 150, 180, 200, 250, 300]
    results = []
    
    print(f"开始测试 {len(grid_counts)} 个不同的网格数量...")
    
    for i, grid_count in enumerate(grid_counts):
        print(f"\n=== {i+1}/{len(grid_counts)} 测试网格数量: {grid_count} ===")
        
        try:
            # 创建策略
            strategy = BinanceGridStrategy(
                price_upper=GRID_CONFIG['price_upper'],
                price_lower=GRID_CONFIG['price_lower'], 
                grid_num=grid_count,
                investment_amount=GRID_CONFIG['investment_amount'],
                grid_mode=GRID_CONFIG['grid_mode']
            )
            
            # 创建回测引擎（关闭详细输出）
            engine = BacktestEngine(strategy, BACKTEST_CONFIG)
            
            # 运行回测
            portfolio_df, trades_df = engine.run_backtest(test_data)
            
            if portfolio_df is not None and len(portfolio_df) > 0:
                # 计算关键指标
                initial_value = portfolio_df['total_value'].iloc[0]
                final_value = portfolio_df['total_value'].iloc[-1]
                total_return = (final_value - initial_value) / initial_value * 100
                trade_count = len(trades_df) if trades_df is not None else 0
                
                # 计算最大回撤
                peak = portfolio_df['total_value'].expanding().max()
                drawdown = (portfolio_df['total_value'] - peak) / peak * 100
                max_drawdown = abs(drawdown.min())
                
                # 计算夏普比率
                portfolio_df['daily_return'] = portfolio_df['total_value'].pct_change()
                returns = portfolio_df['daily_return'].dropna()
                if len(returns) > 1 and returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(1440)  # 年化，1440分钟/天
                else:
                    sharpe_ratio = 0
                
                # 计算平均每笔利润
                avg_profit_per_trade = 0
                if trades_df is not None and len(trades_df) > 0:
                    sell_trades = trades_df[trades_df['type'] == 'SELL']
                    if len(sell_trades) > 0:
                        avg_profit_per_trade = sell_trades['profit'].mean()
                
                # 计算网格密度
                price_range = GRID_CONFIG['price_upper'] - GRID_CONFIG['price_lower']
                grid_density = grid_count / price_range
                
                result = {
                    'grid_count': grid_count,
                    'total_return': round(total_return, 2),
                    'final_value': round(final_value, 2),
                    'trade_count': trade_count,
                    'max_drawdown': round(max_drawdown, 2),
                    'sharpe_ratio': round(sharpe_ratio, 4),
                    'avg_profit_per_trade': round(avg_profit_per_trade, 4),
                    'grid_density': round(grid_density, 2)
                }
                results.append(result)
                
                print(f"收益率: {total_return:.2f}%, 交易次数: {trade_count}, 最大回撤: {max_drawdown:.2f}%")
            else:
                print("回测失败")
                
        except Exception as e:
            print(f"测试出错: {e}")
    
    # 分析和展示结果
    if results:
        df_results = pd.DataFrame(results)
        analyze_results(df_results)
        plot_results(df_results)
        
        return df_results
    
    return None

def analyze_results(df_results):
    """分析优化结果"""
    print(f"\n{'='*80}")
    print("网格数量优化分析结果")
    print(f"{'='*80}")
    
    # 按不同指标排序显示Top 5
    print(f"\n🏆 收益率排行榜 TOP 5:")
    top_return = df_results.nlargest(5, 'total_return')[['grid_count', 'total_return', 'trade_count', 'max_drawdown']]
    print(top_return.to_string(index=False))
    
    print(f"\n📈 夏普比率排行榜 TOP 5:")
    top_sharpe = df_results.nlargest(5, 'sharpe_ratio')[['grid_count', 'sharpe_ratio', 'total_return', 'max_drawdown']]
    print(top_sharpe.to_string(index=False))
    
    print(f"\n🛡️  最小回撤排行榜 TOP 5:")
    top_drawdown = df_results.nsmallest(5, 'max_drawdown')[['grid_count', 'max_drawdown', 'total_return', 'sharpe_ratio']]
    print(top_drawdown.to_string(index=False))
    
    # 综合评分
    print(f"\n🎯 综合评分分析:")
    df_results['composite_score'] = calculate_composite_score(df_results)
    top_composite = df_results.nlargest(5, 'composite_score')[['grid_count', 'composite_score', 'total_return', 'sharpe_ratio', 'max_drawdown']]
    print(top_composite.to_string(index=False))
    
    # 最优推荐
    best_grid = df_results.loc[df_results['composite_score'].idxmax()]
    print(f"\n⭐ 最优网格数量推荐: {best_grid['grid_count']}")
    print(f"   预期收益率: {best_grid['total_return']:.2f}%")
    print(f"   夏普比率: {best_grid['sharpe_ratio']:.4f}")
    print(f"   最大回撤: {best_grid['max_drawdown']:.2f}%")
    print(f"   预期交易次数: {best_grid['trade_count']}")
    print(f"   综合评分: {best_grid['composite_score']:.4f}")
    
    # 风险收益分析
    print(f"\n📊 风险收益分析:")
    risk_return_ratio = df_results['total_return'] / df_results['max_drawdown']
    best_risk_return_idx = risk_return_ratio.idxmax()
    best_risk_return = df_results.loc[best_risk_return_idx]
    print(f"   最佳风险收益比网格数: {best_risk_return['grid_count']}")
    print(f"   风险收益比: {risk_return_ratio.iloc[best_risk_return_idx]:.2f}")

def calculate_composite_score(df_results):
    """计算综合评分"""
    # 标准化各指标 (0-1)
    return_score = (df_results['total_return'] - df_results['total_return'].min()) / \
                   (df_results['total_return'].max() - df_results['total_return'].min())
    
    if df_results['sharpe_ratio'].max() > df_results['sharpe_ratio'].min():
        sharpe_score = (df_results['sharpe_ratio'] - df_results['sharpe_ratio'].min()) / \
                       (df_results['sharpe_ratio'].max() - df_results['sharpe_ratio'].min())
    else:
        sharpe_score = pd.Series([0.5] * len(df_results))
    
    if df_results['max_drawdown'].max() > df_results['max_drawdown'].min():
        drawdown_score = 1 - (df_results['max_drawdown'] - df_results['max_drawdown'].min()) / \
                           (df_results['max_drawdown'].max() - df_results['max_drawdown'].min())
    else:
        drawdown_score = pd.Series([1.0] * len(df_results))
    
    # 加权综合评分 (40% 收益 + 30% 夏普 + 30% 回撤)
    composite_score = 0.4 * return_score + 0.3 * sharpe_score + 0.3 * drawdown_score
    return composite_score

def plot_results(df_results):
    """绘制分析图表"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 收益率 vs 网格数量
    ax1.plot(df_results['grid_count'], df_results['total_return'], 'b-o', markersize=4)
    ax1.set_xlabel('网格数量')
    ax1.set_ylabel('总收益率 (%)')
    ax1.set_title('收益率 vs 网格数量')
    ax1.grid(True, alpha=0.3)
    
    # 标记最高点
    max_return_idx = df_results['total_return'].idxmax()
    max_grid = df_results.loc[max_return_idx, 'grid_count']
    max_return = df_results.loc[max_return_idx, 'total_return']
    ax1.plot(max_grid, max_return, 'ro', markersize=8, label=f'最高: {max_grid}格, {max_return:.1f}%')
    ax1.legend()
    
    # 2. 夏普比率 vs 网格数量
    ax2.plot(df_results['grid_count'], df_results['sharpe_ratio'], 'g-o', markersize=4)
    ax2.set_xlabel('网格数量')
    ax2.set_ylabel('夏普比率')
    ax2.set_title('夏普比率 vs 网格数量')
    ax2.grid(True, alpha=0.3)
    
    # 3. 最大回撤 vs 网格数量
    ax3.plot(df_results['grid_count'], df_results['max_drawdown'], 'r-o', markersize=4)
    ax3.set_xlabel('网格数量')
    ax3.set_ylabel('最大回撤 (%)')
    ax3.set_title('最大回撤 vs 网格数量')
    ax3.grid(True, alpha=0.3)
    
    # 4. 交易次数 vs 网格数量
    ax4.plot(df_results['grid_count'], df_results['trade_count'], 'm-o', markersize=4)
    ax4.set_xlabel('网格数量')
    ax4.set_ylabel('交易次数')
    ax4.set_title('交易次数 vs 网格数量')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    chart_file = f"results/comprehensive_grid_optimization_{timestamp}.png"
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"\n📊 分析图表已保存到: {chart_file}")
    plt.show()
    
    # 保存数据
    result_file = f"results/comprehensive_grid_optimization_{timestamp}.csv"
    df_results.to_csv(result_file, index=False)
    print(f"📄 详细数据已保存到: {result_file}")

if __name__ == "__main__":
    results = comprehensive_grid_optimization()
    if results is not None:
        print(f"\n✅ 网格数量优化完成！建议根据分析结果调整config.py中的grid_num参数。")