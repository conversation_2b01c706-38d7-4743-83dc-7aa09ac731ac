"""
网格策略配置文件
"""

# 交易对配置
SYMBOL = "DOGEUSDT"
EXCHANGE = "binance"

# 网格策略参数（基于用户提供的截图配置）
GRID_CONFIG = {
    "price_upper": 0.40,      # 价格上限 (USDT)
    "price_lower": 0.10,      # 价格下限 (USDT)
    "grid_num": 100,           # 网格数量 (优化后从100调整为90)
    "grid_mode": "arithmetic", # 网格模式：等比 (geometric) 或等差 (arithmetic)
    "investment_amount": 1000.0,  # 投资金额 (USDT)
    "per_grid_amount": 10.0,  # 每笔数量 (USDT) - 减少每格保证金需求
    "profit_ratio": 0.0106,   # 每格利润 1.06%
    "leverage": 40,           # 杠杆倍数
    "trading_direction": "both",  # 交易方向: "long"(仅做多), "short"(仅做空), "both"(双向交易)
}

# 数据下载配置
DATA_CONFIG = {
    "timeframe": "1m",        # K线间隔（1分钟）
    "limit": 44640,           # 获取数据条数（5月份31天的1分钟数据：31天 * 24小时 * 60分钟）
}

# 回测配置
BACKTEST_CONFIG = {
    "commission_rate": 0.0004,  # 手续费率 0.04%
    "slippage": 0.0001,        # 滑点 0.01%
    
    # 时间范围配置 - 可自由设置回测时间段
    "start_date": "2025-05-01", # 回测开始日期 (YYYY-MM-DD)
    "end_date": "2025-05-05",   # 回测结束日期 (YYYY-MM-DD)
    "start_time": "00:00:00",   # 回测开始时间 (HH:MM:SS)，可选
    "end_time": "23:59:59",     # 回测结束时间 (HH:MM:SS)，可选
    
    # 使用示例：
    # 只回测5月前10天: start_date="2025-05-01", end_date="2025-05-10"
    # 只回测交易时间: start_time="09:00:00", end_time="21:00:00"
    # 回测单日: start_date="2025-05-15", end_date="2025-05-15"
    # 不限制时间: start_date=None, end_date=None (使用全部数据)
}

# 文件路径配置
PATHS = {
    "data_dir": "data",
    "results_dir": "results",
    "logs_dir": "logs",
} 