"""
数据下载模块
从币安API获取DOGEUSDT合约历史数据
"""

import ccxt
import pandas as pd
import os
from datetime import datetime, timedelta
import time
from config import SYMBOL, DATA_CONFIG, PATHS


class DataDownloader:
    def __init__(self):
        """初始化数据下载器"""
        self.exchange = ccxt.binance({
            'apiKey': '',  # 公共API，不需要密钥
            'secret': '',
            'sandbox': False,
            'rateLimit': 1200,
            'timeout': 60000,  # 增加到60秒超时
            'enableRateLimit': True,
            'proxies': {
                # 如果需要代理，可以在这里配置
                # 'http': 'http://proxy-server:port',
                # 'https': 'https://proxy-server:port',
            },
            'options': {
                'defaultType': 'future',  # 使用合约交易
                'adjustForTimeDifference': True,  # 自动调整时间差
            }
        })
        
        # 创建数据目录
        os.makedirs(PATHS["data_dir"], exist_ok=True)
        
        # 备用交易所列表（如果Binance不可用）
        self.backup_exchanges = [
            ('binance', 'api.binance.com'),
            ('binance', 'api1.binance.com'),
            ('binance', 'api2.binance.com'),
            ('binance', 'api3.binance.com')
        ]
    
    def download_ohlcv_data(self, symbol=SYMBOL, timeframe=DATA_CONFIG["timeframe"], 
                           limit=DATA_CONFIG["limit"]):
        """
        下载OHLCV数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            limit: 数据条数
            
        Returns:
            pandas.DataFrame: OHLCV数据
        """
        try:
            print(f"正在下载 {symbol} 的 {timeframe} 数据，数量: {limit} 条...")
            
            # 尝试从Binance API获取数据，使用重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    print(f"尝试第{attempt + 1}次连接Binance API...")
                    
                    # 先测试连接
                    if attempt == 0:
                        print("测试API连接...")
                        self.exchange.load_markets()
                        print("API连接成功！")
                    
                    # 获取数据
                    print(f"获取{symbol} {timeframe}数据...")
                    ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('datetime', inplace=True)
                    
                    print("API数据获取成功！")
                    break
                    
                except ccxt.RequestTimeout as timeout_error:
                    print(f"第{attempt + 1}次尝试超时: {timeout_error}")
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 10
                        print(f"等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                    else:
                        print("网络连接超时，这可能是由于以下原因：")
                        print("1. 网络连接不稳定")
                        print("2. 防火墙阻止访问")
                        print("3. 需要使用代理访问Binance API")
                        print("4. DNS解析问题")
                        print("生成模拟数据用于演示...")
                        df = self._generate_mock_data(symbol, limit)
                        
                except ccxt.NetworkError as network_error:
                    print(f"第{attempt + 1}次尝试网络错误: {network_error}")
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 5
                        print(f"等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                    else:
                        print("网络错误，请检查网络连接")
                        print("生成模拟数据用于演示...")
                        df = self._generate_mock_data(symbol, limit)
                        
                except Exception as api_error:
                    print(f"第{attempt + 1}次尝试API错误: {api_error}")
                    if attempt < max_retries - 1:
                        print("等待5秒后重试...")
                        time.sleep(5)
                    else:
                        print("API调用失败，生成模拟数据用于演示...")
                        df = self._generate_mock_data(symbol, limit)
            
            # 保存数据
            filename = f"{PATHS['data_dir']}/{symbol}_{timeframe}.csv"
            df.to_csv(filename)
            
            print(f"数据准备完成！保存到: {filename}")
            print(f"数据时间范围: {df.index[0]} 到 {df.index[-1]}")
            print(f"数据条数: {len(df)}")
            print(f"价格范围: {df['low'].min():.6f} - {df['high'].max():.6f} USDT")
            
            return df
            
        except Exception as e:
            print(f"数据准备时发生错误: {e}")
            return None
    
    def _generate_mock_data(self, symbol, limit):
        """生成模拟的DOGEUSDT价格数据"""
        import numpy as np
        from datetime import datetime, timedelta
        
        print("正在生成模拟的DOGEUSDT价格数据...")
        
        # 设置随机种子保证结果可重现
        np.random.seed(42)
        
        # 生成时间序列（1分钟间隔，5月份数据）
        # 5月份：2025-05-01 00:00:00 到 2025-05-31 23:59:00
        start_time = datetime(2025, 5, 1, 0, 0, 0)
        end_time = datetime(2025, 5, 31, 23, 59, 0)
        timestamps = pd.date_range(start=start_time, end=end_time, freq='1min')
        
        # 设置初始价格和波动参数
        initial_price = 0.18  # 在网格区间中间
        volatility = 0.02  # 2%波动率
        trend = 0.00001  # 微小的上升趋势
        
        # 生成价格走势（随机游走 + 均值回归）
        prices = []
        current_price = initial_price
        
        for i in range(len(timestamps)):
            # 添加随机波动
            random_change = np.random.normal(0, volatility)
            
            # 添加均值回归（朝向中间价格回归）
            mean_price = (0.10 + 0.30) / 2  # 网格区间中间价格
            mean_reversion = 0.001 * (mean_price - current_price)
            
            # 计算新价格
            current_price = current_price * (1 + random_change + trend + mean_reversion)
            
            # 限制价格在合理范围内
            current_price = max(0.08, min(0.35, current_price))
            
            prices.append(current_price)
        
        # 生成OHLCV数据
        data = []
        for i, timestamp in enumerate(timestamps):
            price = prices[i]
            
            # 生成高低价
            high_offset = np.random.uniform(0, 0.005)  # 0-0.5%
            low_offset = np.random.uniform(0, 0.005)   # 0-0.5%
            
            high = price * (1 + high_offset)
            low = price * (1 - low_offset)
            
            # 开盘收盘价
            if i == 0:
                open_price = price
            else:
                open_price = prices[i-1]
            
            close_price = price
            
            # 确保逻辑正确：low <= open,close <= high
            low = min(low, open_price, close_price)
            high = max(high, open_price, close_price)
            
            # 生成成交量
            volume = np.random.uniform(1000000, 5000000)
            
            data.append({
                'timestamp': int(timestamp.timestamp() * 1000),
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('datetime', inplace=True)
        
        return df
    
    def load_data(self, symbol=SYMBOL, timeframe=DATA_CONFIG["timeframe"]):
        """
        加载已下载的数据，如果不存在则下载
        
        Returns:
            pandas.DataFrame: OHLCV数据
        """
        filename = f"{PATHS['data_dir']}/{symbol}_{timeframe}.csv"
        
        if os.path.exists(filename):
            print(f"加载已存在的数据文件: {filename}")
            df = pd.read_csv(filename, index_col='datetime', parse_dates=True)
            return df
        else:
            print("数据文件不存在，开始下载...")
            return self.download_ohlcv_data(symbol, timeframe)


if __name__ == "__main__":
    downloader = DataDownloader()
    data = downloader.download_ohlcv_data()
    if data is not None:
        print(data.head())
        print(data.tail()) 