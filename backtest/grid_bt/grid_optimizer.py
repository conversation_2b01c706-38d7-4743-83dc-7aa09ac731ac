"""
网格策略参数优化器
通过批量回测找到最优的网格数量配置
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import multiprocessing as mp
from copy import deepcopy
import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

from config import GRID_CONFIG, BACKTEST_CONFIG, PATHS
from grid_strategy import BinanceGridStrategy
from backtest_engine import BacktestEngine
from data_downloader import DataDownloader


class GridOptimizer:
    def __init__(self):
        """初始化网格优化器"""
        self.base_config = deepcopy(GRID_CONFIG)
        self.results = []
        
    def optimize_grid_count(self, grid_counts=None, processes=None):
        """
        优化网格数量
        
        Args:
            grid_counts: 要测试的网格数量列表，默认测试10-300
            processes: 并行进程数，默认使用CPU核心数
        
        Returns:
            pandas.DataFrame: 优化结果
        """
        if grid_counts is None:
            # 测试不同的网格数量：从少到多
            grid_counts = [
                # 稀疏网格
                10, 15, 20, 25, 30, 
                # 中等网格  
                40, 50, 60, 70, 80, 90, 100,
                # 密集网格
                120, 150, 180, 200, 250, 300
            ]
        
        if processes is None:
            processes = min(mp.cpu_count(), len(grid_counts))
        
        print(f"开始网格数量优化测试...")
        print(f"测试网格数量: {grid_counts}")
        print(f"并行进程数: {processes}")
        print(f"基础配置: 价格区间 {self.base_config['price_lower']}-{self.base_config['price_upper']}")
        
        # 加载数据
        downloader = DataDownloader()
        data = downloader.load_data()
        if data is None:
            raise Exception("无法加载数据")
        
        # 创建参数组合
        test_params = []
        for grid_count in grid_counts:
            config = deepcopy(self.base_config)
            config['grid_num'] = grid_count
            test_params.append((config, data, grid_count))
        
        # 并行回测
        if processes > 1:
            with mp.Pool(processes=processes) as pool:
                results = pool.map(self._run_single_backtest, test_params)
        else:
            results = [self._run_single_backtest(params) for params in test_params]
        
        # 整理结果
        self.results = [r for r in results if r is not None]
        df_results = pd.DataFrame(self.results)
        
        # 排序并显示结果
        df_results = df_results.sort_values('total_return', ascending=False)
        
        print(f"\n=== 网格数量优化结果 ===")
        print(df_results[['grid_count', 'total_return', 'sharpe_ratio', 'max_drawdown', 'trade_count']].head(10))
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"{PATHS['results_dir']}/grid_optimization_{timestamp}.csv"
        df_results.to_csv(result_file, index=False)
        print(f"结果已保存到: {result_file}")
        
        # 生成分析图表
        self._plot_optimization_results(df_results, timestamp)
        
        return df_results
    
    def _run_single_backtest(self, params):
        """
        运行单次回测
        
        Args:
            params: (config, data, grid_count) 元组
            
        Returns:
            dict: 回测结果
        """
        config, data, grid_count = params
        
        try:
            print(f"测试网格数量: {grid_count}")
            
            # 创建策略和回测引擎
            strategy = BinanceGridStrategy(
                price_upper=config['price_upper'],
                price_lower=config['price_lower'], 
                grid_num=config['grid_num'],
                investment_amount=config['investment_amount'],
                grid_mode=config['grid_mode']
            )
            engine = BacktestEngine(strategy, BACKTEST_CONFIG)
            
            # 运行回测
            portfolio_df, trades_df = engine.run_backtest(data)
            
            if portfolio_df is None or len(portfolio_df) == 0:
                print(f"网格数量 {grid_count} 回测失败")
                return None
            
            # 计算关键指标
            initial_value = portfolio_df['total_value'].iloc[0]
            final_value = portfolio_df['total_value'].iloc[-1]
            total_return = (final_value - initial_value) / initial_value * 100
            
            # 计算夏普比率
            portfolio_df['daily_return'] = portfolio_df['total_value'].pct_change()
            sharpe_ratio = self._calculate_sharpe_ratio(portfolio_df['daily_return'])
            
            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(portfolio_df['total_value'])
            
            # 交易次数
            trade_count = len(trades_df) if trades_df is not None else 0
            
            # 计算平均每笔收益
            avg_trade_profit = 0
            if trades_df is not None and len(trades_df) > 0:
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                if len(sell_trades) > 0:
                    avg_trade_profit = sell_trades['profit'].mean()
            
            # 计算理论网格间距
            price_range = config['price_upper'] - config['price_lower']
            if config['grid_mode'] == 'geometric':
                grid_spacing = (config['price_upper'] / config['price_lower']) ** (1 / grid_count) - 1
            else:
                grid_spacing = price_range / grid_count
            
            result = {
                'grid_count': grid_count,
                'total_return': round(total_return, 2),
                'final_value': round(final_value, 2),
                'sharpe_ratio': round(sharpe_ratio, 4),
                'max_drawdown': round(max_drawdown, 2),
                'trade_count': trade_count,
                'avg_trade_profit': round(avg_trade_profit, 4),
                'grid_spacing_pct': round(grid_spacing * 100, 4) if config['grid_mode'] == 'geometric' else round(grid_spacing / config['price_lower'] * 100, 4),
                'price_range': f"{config['price_lower']}-{config['price_upper']}",
                'investment_amount': config['investment_amount']
            }
            
            print(f"网格数量 {grid_count}: 收益率 {total_return:.2f}%, 交易次数 {trade_count}")
            return result
            
        except Exception as e:
            print(f"网格数量 {grid_count} 测试出错: {e}")
            return None
    
    def _calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """计算夏普比率"""
        if len(returns) <= 1:
            return 0
        
        returns = returns.dropna()
        if len(returns) <= 1:
            return 0
            
        excess_returns = returns - risk_free_rate / 252  # 假设年化无风险利率2%
        if returns.std() == 0:
            return 0
        return excess_returns.mean() / returns.std() * np.sqrt(252)
    
    def _calculate_max_drawdown(self, values):
        """计算最大回撤"""
        peak = values.expanding().max()
        drawdown = (values - peak) / peak * 100
        return abs(drawdown.min())
    
    def _plot_optimization_results(self, df_results, timestamp):
        """绘制优化结果图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 收益率 vs 网格数量
        ax1.plot(df_results['grid_count'], df_results['total_return'], 'b-o', markersize=4)
        ax1.set_xlabel('网格数量')
        ax1.set_ylabel('总收益率 (%)')
        ax1.set_title('收益率 vs 网格数量')
        ax1.grid(True, alpha=0.3)
        
        # 标记最优点
        best_idx = df_results['total_return'].idxmax()
        best_grid = df_results.loc[best_idx, 'grid_count']
        best_return = df_results.loc[best_idx, 'total_return']
        ax1.plot(best_grid, best_return, 'ro', markersize=8, label=f'最优: {best_grid}格, {best_return:.1f}%')
        ax1.legend()
        
        # 2. 夏普比率 vs 网格数量
        ax2.plot(df_results['grid_count'], df_results['sharpe_ratio'], 'g-o', markersize=4)
        ax2.set_xlabel('网格数量')
        ax2.set_ylabel('夏普比率')
        ax2.set_title('夏普比率 vs 网格数量')
        ax2.grid(True, alpha=0.3)
        
        # 3. 最大回撤 vs 网格数量
        ax3.plot(df_results['grid_count'], df_results['max_drawdown'], 'r-o', markersize=4)
        ax3.set_xlabel('网格数量')
        ax3.set_ylabel('最大回撤 (%)')
        ax3.set_title('最大回撤 vs 网格数量')
        ax3.grid(True, alpha=0.3)
        
        # 4. 交易次数 vs 网格数量
        ax4.plot(df_results['grid_count'], df_results['trade_count'], 'm-o', markersize=4)
        ax4.set_xlabel('网格数量')
        ax4.set_ylabel('交易次数')
        ax4.set_title('交易次数 vs 网格数量')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = f"{PATHS['results_dir']}/grid_optimization_chart_{timestamp}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"分析图表已保存到: {chart_file}")
        plt.show()
    
    def find_optimal_grid(self, df_results, weight_return=0.4, weight_sharpe=0.3, weight_drawdown=0.3):
        """
        根据多个指标综合评分找到最优网格数量
        
        Args:
            df_results: 优化结果DataFrame
            weight_return: 收益率权重
            weight_sharpe: 夏普比率权重  
            weight_drawdown: 最大回撤权重（负面指标）
            
        Returns:
            dict: 最优配置结果
        """
        if len(df_results) == 0:
            return None
        
        # 标准化各项指标 (0-1)
        df_score = df_results.copy()
        
        # 收益率：越高越好
        df_score['return_score'] = (df_score['total_return'] - df_score['total_return'].min()) / \
                                  (df_score['total_return'].max() - df_score['total_return'].min())
        
        # 夏普比率：越高越好  
        if df_score['sharpe_ratio'].max() > df_score['sharpe_ratio'].min():
            df_score['sharpe_score'] = (df_score['sharpe_ratio'] - df_score['sharpe_ratio'].min()) / \
                                      (df_score['sharpe_ratio'].max() - df_score['sharpe_ratio'].min())
        else:
            df_score['sharpe_score'] = 0.5
        
        # 最大回撤：越小越好
        if df_score['max_drawdown'].max() > df_score['max_drawdown'].min():
            df_score['drawdown_score'] = 1 - (df_score['max_drawdown'] - df_score['max_drawdown'].min()) / \
                                            (df_score['max_drawdown'].max() - df_score['max_drawdown'].min())
        else:
            df_score['drawdown_score'] = 1.0
        
        # 综合评分
        df_score['composite_score'] = (weight_return * df_score['return_score'] + 
                                     weight_sharpe * df_score['sharpe_score'] + 
                                     weight_drawdown * df_score['drawdown_score'])
        
        # 找到最优配置
        best_idx = df_score['composite_score'].idxmax()
        optimal_result = df_score.loc[best_idx].to_dict()
        
        print(f"\n=== 最优网格配置 ===")
        print(f"最优网格数量: {optimal_result['grid_count']}")
        print(f"预期总收益率: {optimal_result['total_return']:.2f}%")
        print(f"夏普比率: {optimal_result['sharpe_ratio']:.4f}")
        print(f"最大回撤: {optimal_result['max_drawdown']:.2f}%")
        print(f"预期交易次数: {optimal_result['trade_count']}")
        print(f"综合评分: {optimal_result['composite_score']:.4f}")
        
        return optimal_result


def main():
    """主函数：运行网格优化"""
    optimizer = GridOptimizer()
    
    # 运行优化
    results = optimizer.optimize_grid_count()
    
    # 找到最优配置
    optimal = optimizer.find_optimal_grid(results)
    
    if optimal:
        print(f"\n建议将网格数量从 {GRID_CONFIG['grid_num']} 调整为 {optimal['grid_count']}")


if __name__ == "__main__":
    main()