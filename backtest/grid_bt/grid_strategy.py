"""
币安风格网格交易策略 - 成对交易版本
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict
from config import GRID_CONFIG, BACKTEST_CONFIG


class BinanceGridStrategy:
    def __init__(self, price_upper: float, price_lower: float, grid_num: int, 
                 investment_amount: float, grid_mode: str = "geometric"):
        """
        初始化币安风格网格策略
        
        Args:
            price_upper: 价格上限
            price_lower: 价格下限
            grid_num: 网格数量
            investment_amount: 投资金额
            grid_mode: 网格模式 ("geometric" 或 "arithmetic")
        """
        self.price_upper = price_upper
        self.price_lower = price_lower
        self.grid_num = grid_num
        self.investment_amount = investment_amount
        self.grid_mode = grid_mode
        self.leverage = GRID_CONFIG.get("leverage", 1)
        
        # 计算网格价格
        self.grid_prices = self._calculate_grid_prices()

        print(f"网格价格: {self.grid_prices}")
        
        # 计算每格投资金额
        self.per_grid_amount = GRID_CONFIG.get("per_grid_amount", investment_amount / (grid_num // 2))
        
        # 初始化状态
        self.cash = investment_amount
        self.total_profit = 0.0
        self.trade_count = 0
        self.trade_records = []
        
        # 币安风格：持仓配对管理
        self.grid_positions = {}  # {grid_price: {'short': quantity, 'long': quantity, 'active': bool}}
        self.pending_orders = {}  # {grid_price: {'type': 'short'/'long', 'quantity': amount}}
        
        # 价格跟踪
        self.initial_price = None
        self.last_price = None
        
        # 初始化所有网格位置
        self._initialize_grid_positions()
        
        print(f"币安风格网格策略初始化完成:")
        print(f"价格区间: {price_lower:.6f} - {price_upper:.6f}")
        print(f"网格数量: {grid_num}")
        print(f"网格模式: {grid_mode}")
        print(f"杠杆倍数: {self.leverage}x")
        print(f"投资金额: {investment_amount} USDT")
        print(f"每格金额: {self.per_grid_amount:.2f} USDT")
    
    def _calculate_grid_prices(self) -> List[float]:
        """计算网格价格"""
        if self.grid_mode == "geometric":
            ratio = (self.price_upper / self.price_lower) ** (1 / (self.grid_num - 1))
            prices = [self.price_lower * (ratio ** i) for i in range(self.grid_num)]
        else:
            step = (self.price_upper - self.price_lower) / (self.grid_num - 1)
            prices = [self.price_lower + step * i for i in range(self.grid_num)]
        
        return sorted(prices)
    
    def _initialize_grid_positions(self):
        """初始化所有网格位置"""
        for price in self.grid_prices:
            self.grid_positions[price] = {
                'short': 0,      # 空头数量
                'long': 0,       # 多头数量
                'active': True   # 是否激活
            }
    
    def process_price(self, current_price: float, timestamp: str) -> List[Dict]:
        """
        处理价格变化，执行币安风格网格交易
        """
        trades = []
        
        # 价格超出范围不交易
        if current_price < self.price_lower or current_price > self.price_upper:
            return trades
        
        # 设置初始价格
        if self.initial_price is None:
            self.initial_price = current_price
            self.last_price = current_price
            print(f"初始价格设定为: {self.initial_price:.6f}")
            return trades
        
        # 币安网格逻辑：价格穿越网格线时触发交易
        trades.extend(self._check_grid_crosses(current_price, timestamp))
        
        self.last_price = current_price
        return trades
    
    def _check_grid_crosses(self, current_price: float, timestamp: str) -> List[Dict]:
        """检查价格穿越网格线触发的交易 - 改进版本避免无意义对冲"""
        trades = []
        
        # 找到当前价格和上次价格之间穿越的所有网格线
        crossed_grids = self._find_crossed_grids(self.last_price, current_price)
        
        # 先检查平仓机会
        trades.extend(self._check_close_orders(current_price, timestamp))
        
        # 然后检查开仓机会，但要避免无意义的对冲交易
        for grid_price, direction in crossed_grids:
            # 检查是否已有该网格的持仓或挂单
            has_position = (self.grid_positions[grid_price]['long'] > 0 or 
                           self.grid_positions[grid_price]['short'] > 0)
            has_pending = grid_price in self.pending_orders
            
            # 如果该网格已有活动，跳过以避免对冲
            if has_position or has_pending:
                continue
                
            if direction == "up":
                # 价格向上穿越网格线 -> 做空 + 设置对应做多平仓单
                trade = self._execute_short_with_long_pair(grid_price, current_price, timestamp)
                if trade:
                    trades.extend(trade)
            elif direction == "down":
                # 价格向下穿越网格线 -> 做多 + 设置对应做空平仓单
                trade = self._execute_long_with_short_pair(grid_price, current_price, timestamp)
                if trade:
                    trades.extend(trade)
        
        return trades
    
    def _find_crossed_grids(self, last_price: float, current_price: float) -> List[Tuple[float, str]]:
        """找到价格穿越的网格线"""
        crossed = []
        
        # 添加最小价格移动要求，避免微小波动
        min_move_threshold = 0.00001  # 降低阈值，让更多交易通过
        if abs(current_price - last_price) < min_move_threshold:
            return crossed
        
        for grid_price in self.grid_prices:
            if last_price < current_price:  # 价格上涨
                # 简化穿越条件，使用更小的缓冲区
                if last_price <= grid_price and current_price > grid_price:
                    crossed.append((grid_price, "up"))
            else:  # 价格下跌
                # 简化穿越条件，使用更小的缓冲区
                if current_price <= grid_price and last_price > grid_price:
                    crossed.append((grid_price, "down"))
        
        return crossed
    
    def _execute_short_with_long_pair(self, grid_price: float, current_price: float, timestamp: str) -> List[Dict]:
        """执行做空并设置对应的做多平仓单（币安风格）"""
        trades = []
        
        # 检查该网格是否可以做空
        if not self._can_open_short(grid_price):
            return trades
        
        # 计算交易数量（做空和做多必须相等）
        position_value = self.per_grid_amount * self.leverage
        quantity = position_value / current_price
        margin_required = self.per_grid_amount
        commission = margin_required * BACKTEST_CONFIG["commission_rate"]
        total_cost = margin_required + commission
        
        if self.cash >= total_cost:
            # 1. 执行做空
            self.grid_positions[grid_price]['short'] = quantity
            self.cash -= total_cost
            self.trade_count += 1
            
            # 记录做空交易
            positions_pnl = self._calculate_positions_pnl(current_price)
            remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
            
            short_trade = {
                'timestamp': timestamp,
                'type': 'SELL_SHORT',
                'direction': 'SHORT',
                'price': current_price,
                'quantity': quantity,
                'amount': position_value,
                'margin': margin_required,
                'commission': commission,
                'grid_price': grid_price,
                'cash': self.cash,
                'total_value': self._calculate_total_value(current_price),
                'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
                'position_count': positions_pnl['position_count'],
                'remaining_funds': remaining_funds,
                'positions_pnl_detail': str(positions_pnl['positions_detail']),
                'pair_info': f'对应做多平仓价格: {self._get_long_close_price(grid_price):.6f}'
            }
            
            self.trade_records.append(short_trade)
            trades.append(short_trade)
            
            # 2. 设置对应的做多平仓单（相同数量）
            long_close_price = self._get_long_close_price(grid_price)
            if long_close_price:
                self.pending_orders[long_close_price] = {
                    'type': 'close_short',
                    'quantity': quantity,
                    'original_grid': grid_price,
                    'open_price': current_price
                }
        
        return trades
    
    def _execute_long_with_short_pair(self, grid_price: float, current_price: float, timestamp: str) -> List[Dict]:
        """执行做多并设置对应的做空平仓单（币安风格）"""
        trades = []
        
        # 检查该网格是否可以做多
        if not self._can_open_long(grid_price):
            return trades
        
        # 计算交易数量（做多和做空必须相等）
        position_value = self.per_grid_amount * self.leverage
        quantity = position_value / current_price
        margin_required = self.per_grid_amount
        commission = margin_required * BACKTEST_CONFIG["commission_rate"]
        total_cost = margin_required + commission
        
        if self.cash >= total_cost:
            # 1. 执行做多
            self.grid_positions[grid_price]['long'] = quantity
            self.cash -= total_cost
            self.trade_count += 1
            
            # 记录做多交易
            positions_pnl = self._calculate_positions_pnl(current_price)
            remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
            
            long_trade = {
                'timestamp': timestamp,
                'type': 'BUY_LONG',
                'direction': 'LONG',
                'price': current_price,
                'quantity': quantity,
                'amount': position_value,
                'margin': margin_required,
                'commission': commission,
                'grid_price': grid_price,
                'cash': self.cash,
                'total_value': self._calculate_total_value(current_price),
                'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
                'position_count': positions_pnl['position_count'],
                'remaining_funds': remaining_funds,
                'positions_pnl_detail': str(positions_pnl['positions_detail']),
                'pair_info': f'对应做空平仓价格: {self._get_short_close_price(grid_price):.6f}'
            }
            
            self.trade_records.append(long_trade)
            trades.append(long_trade)
            
            # 2. 设置对应的做空平仓单（相同数量）
            short_close_price = self._get_short_close_price(grid_price)
            if short_close_price:
                self.pending_orders[short_close_price] = {
                    'type': 'close_long',
                    'quantity': quantity,
                    'original_grid': grid_price,
                    'open_price': current_price
                }
        
        return trades
    
    def _check_close_orders(self, current_price: float, timestamp: str) -> List[Dict]:
        """检查平仓单是否被触发"""
        trades = []
        orders_to_remove = []
        
        for close_price, order_info in self.pending_orders.items():
            if abs(current_price - close_price) <= close_price * 0.001:  # 价格接近平仓点
                
                if order_info['type'] == 'close_short':
                    # 平空（买入平仓）
                    trade = self._close_short_position(
                        order_info['original_grid'], 
                        order_info['quantity'],
                        order_info['open_price'],
                        current_price, 
                        timestamp
                    )
                    if trade:
                        trades.append(trade)
                        orders_to_remove.append(close_price)
                        
                elif order_info['type'] == 'close_long':
                    # 平多（卖出平仓）
                    trade = self._close_long_position(
                        order_info['original_grid'],
                        order_info['quantity'], 
                        order_info['open_price'],
                        current_price,
                        timestamp
                    )
                    if trade:
                        trades.append(trade)
                        orders_to_remove.append(close_price)
        
        # 移除已执行的订单
        for price in orders_to_remove:
            del self.pending_orders[price]
        
        return trades
    
    def _close_short_position(self, grid_price: float, quantity: float, open_price: float, 
                            close_price: float, timestamp: str) -> Dict:
        """平空头持仓"""
        if grid_price not in self.grid_positions or self.grid_positions[grid_price]['short'] <= 0:
            return None
        
        # 计算盈亏
        position_value = quantity * close_price
        commission = position_value * BACKTEST_CONFIG["commission_rate"]
        pnl = quantity * (open_price - close_price) - commission  # 空头盈亏
        
        # 释放保证金和盈亏
        margin_released = self.per_grid_amount
        self.cash += margin_released + pnl
        self.total_profit += pnl
        self.trade_count += 1
        
        # 清除持仓
        self.grid_positions[grid_price]['short'] = 0
        
        # 记录交易
        positions_pnl = self._calculate_positions_pnl(close_price)
        remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
        
        return {
            'timestamp': timestamp,
            'type': 'BUY_SHORT',
            'direction': 'SHORT',
            'price': close_price,
            'quantity': quantity,
            'amount': position_value,
            'commission': commission,
            'grid_price': grid_price,
            'profit': pnl,
            'margin_released': margin_released,
            'cash': self.cash,
            'total_value': self._calculate_total_value(close_price),
            'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
            'position_count': positions_pnl['position_count'],
            'remaining_funds': remaining_funds,
            'positions_pnl_detail': str(positions_pnl['positions_detail']),
            'pair_info': f'平仓空头开仓价格: {open_price:.6f}'
        }
    
    def _close_long_position(self, grid_price: float, quantity: float, open_price: float,
                           close_price: float, timestamp: str) -> Dict:
        """平多头持仓"""
        if grid_price not in self.grid_positions or self.grid_positions[grid_price]['long'] <= 0:
            return None
        
        # 计算盈亏
        position_value = quantity * close_price
        commission = position_value * BACKTEST_CONFIG["commission_rate"]
        pnl = quantity * (close_price - open_price) - commission  # 多头盈亏
        
        # 释放保证金和盈亏
        margin_released = self.per_grid_amount
        self.cash += margin_released + pnl
        self.total_profit += pnl
        self.trade_count += 1
        
        # 清除持仓
        self.grid_positions[grid_price]['long'] = 0
        
        # 记录交易
        positions_pnl = self._calculate_positions_pnl(close_price)
        remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
        
        return {
            'timestamp': timestamp,
            'type': 'SELL_LONG',
            'direction': 'LONG',
            'price': close_price,
            'quantity': quantity,
            'amount': position_value,
            'commission': commission,
            'grid_price': grid_price,
            'profit': pnl,
            'margin_released': margin_released,
            'cash': self.cash,
            'total_value': self._calculate_total_value(close_price),
            'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
            'position_count': positions_pnl['position_count'],
            'remaining_funds': remaining_funds,
            'positions_pnl_detail': str(positions_pnl['positions_detail']),
            'pair_info': f'平仓多头开仓价格: {open_price:.6f}'
        }
    
    def _can_open_short(self, grid_price: float) -> bool:
        """检查是否可以在该网格做空"""
        return (grid_price in self.grid_positions and 
                self.grid_positions[grid_price]['short'] == 0 and
                self.grid_positions[grid_price]['long'] == 0 and  # 不能有反向持仓
                self.grid_positions[grid_price]['active'])
    
    def _can_open_long(self, grid_price: float) -> bool:
        """检查是否可以在该网格做多"""
        return (grid_price in self.grid_positions and 
                self.grid_positions[grid_price]['long'] == 0 and
                self.grid_positions[grid_price]['short'] == 0 and  # 不能有反向持仓
                self.grid_positions[grid_price]['active'])
    
    def _get_long_close_price(self, short_grid_price: float) -> float:
        """获取做空对应的做多平仓价格（下一个网格）"""
        grid_index = self.grid_prices.index(short_grid_price)
        if grid_index > 0:
            return self.grid_prices[grid_index - 1]
        return None
    
    def _get_short_close_price(self, long_grid_price: float) -> float:
        """获取做多对应的做空平仓价格（上一个网格）"""
        grid_index = self.grid_prices.index(long_grid_price)
        if grid_index < len(self.grid_prices) - 1:
            return self.grid_prices[grid_index + 1]
        return None
    
    def _calculate_total_value(self, current_price: float) -> float:
        """计算总资产价值"""
        total_value = self.cash
        
        for grid_price, position in self.grid_positions.items():
            # 多头持仓价值
            if position['long'] > 0:
                total_value += position['long'] * current_price
            
            # 空头持仓价值（盈亏）
            if position['short'] > 0:
                short_pnl = position['short'] * (grid_price - current_price)
                total_value += short_pnl
        
        return total_value
    
    def _calculate_positions_pnl(self, current_price: float) -> Dict:
        """计算所有持仓的盈亏情况"""
        positions_pnl = {}
        total_unrealized_pnl = 0
        total_positions = 0
        
        for grid_price, position in self.grid_positions.items():
            # 多头持仓
            if position['long'] > 0:
                original_value = self.per_grid_amount * self.leverage
                current_value = position['long'] * current_price
                pnl = current_value - original_value
                
                positions_pnl[f"long_{grid_price:.6f}"] = {
                    'type': 'LONG',
                    'entry_price': grid_price,
                    'quantity': position['long'],
                    'original_value': original_value,
                    'current_value': current_value,
                    'pnl': pnl
                }
                total_unrealized_pnl += pnl
                total_positions += 1
            
            # 空头持仓
            if position['short'] > 0:
                original_value = self.per_grid_amount * self.leverage
                pnl = position['short'] * (grid_price - current_price)
                
                positions_pnl[f"short_{grid_price:.6f}"] = {
                    'type': 'SHORT',
                    'entry_price': grid_price,
                    'quantity': position['short'],
                    'original_value': original_value,
                    'current_value': position['short'] * current_price,
                    'pnl': pnl
                }
                total_unrealized_pnl += pnl
                total_positions += 1
        
        return {
            'positions_detail': positions_pnl,
            'total_unrealized_pnl': total_unrealized_pnl,
            'position_count': total_positions
        }
    
    def get_strategy_stats(self, current_price: float) -> Dict:
        """获取策略统计信息"""
        total_value = self._calculate_total_value(current_price)
        unrealized_pnl = total_value - self.investment_amount
        
        # 统计持仓
        long_count = sum(1 for pos in self.grid_positions.values() if pos['long'] > 0)
        short_count = sum(1 for pos in self.grid_positions.values() if pos['short'] > 0)
        
        return {
            'initial_investment': self.investment_amount,
            'current_cash': self.cash,
            'long_position_count': long_count,
            'short_position_count': short_count,
            'total_position_count': long_count + short_count,
            'pending_orders_count': len(self.pending_orders),
            'total_value': total_value,
            'realized_profit': self.total_profit,
            'unrealized_pnl': unrealized_pnl,
            'total_return': (total_value / self.investment_amount - 1) * 100,
            'trade_count': self.trade_count,
            'grid_prices': self.grid_prices,
            'grid_positions': self.grid_positions,
            'pending_orders': self.pending_orders
        }