"""
网格交易策略实现
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict
from config import GRID_CONFIG, BACKTEST_CONFIG


class GridStrategy:
    def __init__(self, price_upper: float, price_lower: float, grid_num: int, 
                 investment_amount: float, grid_mode: str = "geometric", 
                 trading_direction: str = "both"):
        """
        初始化网格策略
        
        Args:
            price_upper: 价格上限
            price_lower: 价格下限
            grid_num: 网格数量
            investment_amount: 投资金额
            grid_mode: 网格模式 ("geometric" 或 "arithmetic")
            trading_direction: 交易方向 ("long", "short", "both")
        """
        self.price_upper = price_upper
        self.price_lower = price_lower
        self.grid_num = grid_num
        self.investment_amount = investment_amount
        self.grid_mode = grid_mode
        self.trading_direction = trading_direction or GRID_CONFIG.get("trading_direction", "both")
        self.leverage = GRID_CONFIG.get("leverage", 1)  # 获取杠杆倍数
        
        # 计算网格价格
        self.grid_prices = self._calculate_grid_prices()
        
        # 计算每格投资金额
        # 优先使用配置文件中的值，如果没有则计算
        self.per_grid_amount = GRID_CONFIG.get("per_grid_amount", investment_amount / (grid_num // 2))
        
        # 初始化持仓状态
        self.long_positions = {}   # {price: amount} 多头持仓
        self.short_positions = {}  # {price: amount} 空头持仓
        self.cash = investment_amount  # 可用现金
        self.total_profit = 0.0
        self.trade_count = 0
        self.trade_records = []
        
        # 添加初始价格跟踪
        self.initial_price = None  # 第一次价格
        self.last_price = None     # 上一次价格
        
        # 为了保持向后兼容性，保留 positions 属性（指向 long_positions）
        self.positions = self.long_positions
        
        print(f"网格策略初始化完成:")
        print(f"价格区间: {price_lower:.6f} - {price_upper:.6f}")
        print(f"网格数量: {grid_num}")
        print(f"网格模式: {grid_mode}")
        print(f"交易方向: {self.trading_direction}")
        print(f"杠杆倍数: {self.leverage}x")
        print(f"投资金额: {investment_amount} USDT")
        print(f"每格金额: {self.per_grid_amount:.2f} USDT")
    
    def _calculate_grid_prices(self) -> List[float]:
        """计算网格价格"""
        if self.grid_mode == "geometric":
            # 等比数列
            ratio = (self.price_upper / self.price_lower) ** (1 / (self.grid_num - 1))
            prices = [self.price_lower * (ratio ** i) for i in range(self.grid_num)]
        else:
            # 等差数列
            step = (self.price_upper - self.price_lower) / (self.grid_num - 1)
            prices = [self.price_lower + step * i for i in range(self.grid_num)]
        
        return sorted(prices)
    
    def _find_grid_level(self, price: float) -> int:
        """找到价格对应的网格层级"""
        for i, grid_price in enumerate(self.grid_prices):
            if price <= grid_price:
                return i
        return len(self.grid_prices) - 1
    
    def process_price(self, current_price: float, timestamp: str) -> List[Dict]:
        """
        处理价格变化，执行网格交易
        
        Args:
            current_price: 当前价格
            timestamp: 时间戳
            
        Returns:
            List[Dict]: 交易记录列表
        """
        trades = []
        
        # 如果价格超出网格范围，不执行交易
        if current_price < self.price_lower or current_price > self.price_upper:
            return trades
        
        # 设置初始价格
        if self.initial_price is None:
            self.initial_price = current_price
            self.last_price = current_price
            print(f"初始价格设定为: {self.initial_price:.6f}")
            return trades  # 第一次不交易，只记录价格
        
        # 根据交易方向检查交易机会
        if self.trading_direction in ["long", "both"]:
            trades.extend(self._check_long_opportunities(current_price, timestamp))
            trades.extend(self._check_long_close_opportunities(current_price, timestamp))
        
        if self.trading_direction in ["short", "both"]:
            trades.extend(self._check_short_opportunities(current_price, timestamp))
            trades.extend(self._check_short_close_opportunities(current_price, timestamp))
        
        # 更新上次价格
        self.last_price = current_price
        return trades
    
    def _check_long_opportunities(self, current_price: float, timestamp: str) -> List[Dict]:
        """检查买入机会（价格跌到网格线时）"""
        trades = []
        
        # 找到最接近当前价格的网格（从下往上找第一个大于当前价格的网格）
        target_grid = None
        for i in range(len(self.grid_prices)):
            grid_price = self.grid_prices[i]
            
            # 买入条件：当前价格刚好跌破网格价格，且该网格没有多头持仓
            if (current_price <= grid_price and  
                grid_price not in self.long_positions and
                (self.last_price is None or self.last_price > grid_price)):  # 价格是刚跌破的
                target_grid = grid_price
                break
        
        # 只买入一个最接近的网格
        if target_grid is not None:
            # 执行买入（合约交易使用杠杆）
            position_value = self.per_grid_amount * self.leverage  # 实际持仓价值
            quantity = position_value / current_price  # 持仓数量
            
            # 计算所需保证金（实际持仓价值 / 杠杆倍数）
            margin_required = self.per_grid_amount
            commission = margin_required * BACKTEST_CONFIG["commission_rate"]
            total_cost = margin_required + commission
            
            if self.cash >= total_cost:
                self.long_positions[target_grid] = quantity
                self.cash -= total_cost
                self.trade_count += 1
                
                # 计算当前所有持仓的盈亏情况
                positions_pnl = self._calculate_positions_pnl(current_price)
                
                # 计算剩余资金总额 (现金 + 未实现盈亏)
                remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
                
                trade_record = {
                    'timestamp': timestamp,
                    'type': 'BUY_LONG',
                    'direction': 'LONG',
                    'price': current_price,
                    'quantity': quantity,
                    'amount': position_value,  # 实际持仓价值
                    'margin': margin_required,  # 所需保证金
                    'commission': commission,
                    'grid_price': target_grid,
                    'cash': self.cash,
                    'total_value': self._calculate_total_value(current_price),
                    'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
                    'position_count': positions_pnl['position_count'],
                    'remaining_funds': remaining_funds,  # 剩余资金总额
                    'positions_pnl_detail': str(positions_pnl['positions_detail'])  # 转为字符串以便CSV保存
                }
                
                self.trade_records.append(trade_record)
                trades.append(trade_record)
        
        return trades
    
    def _check_long_close_opportunities(self, current_price: float, timestamp: str) -> List[Dict]:
        """检查多头平仓机会（价格上涨时）"""
        trades = []
        positions_to_remove = []
        
        for grid_price, quantity in self.long_positions.items():
            # 找到当前持仓对应的卖出价格
            grid_index = self.grid_prices.index(grid_price)
            if grid_index < len(self.grid_prices) - 1:
                sell_price = self.grid_prices[grid_index + 1]
                
                # 卖出条件：价格确实突破上一个网格线
                if current_price >= sell_price * 1.001:  # 需要确实突破
                    # 执行卖出（合约交易）
                    position_value = quantity * current_price  # 当前持仓价值
                    commission = position_value * BACKTEST_CONFIG["commission_rate"]
                    
                    # 计算利润（持仓价值变化 - 手续费）
                    original_position_value = self.per_grid_amount * self.leverage
                    pnl = (position_value - original_position_value) - commission
                    self.total_profit += pnl
                    
                    # 释放保证金 + 盈亏
                    margin_released = self.per_grid_amount  # 释放的保证金
                    self.cash += margin_released + pnl
                    self.trade_count += 1
                    
                    # 计算当前所有持仓的盈亏情况（卖出后）
                    positions_pnl = self._calculate_positions_pnl(current_price)
                    
                    # 计算剩余资金总额 (现金 + 未实现盈亏)
                    remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
                    
                    trade_record = {
                        'timestamp': timestamp,
                        'type': 'SELL_LONG',
                        'direction': 'LONG',
                        'price': current_price,
                        'quantity': quantity,
                        'amount': position_value,
                        'commission': commission,
                        'grid_price': grid_price,
                        'sell_price': sell_price,
                        'profit': pnl,
                        'margin_released': margin_released,
                        'cash': self.cash,
                        'total_value': self._calculate_total_value(current_price),
                        'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
                        'position_count': positions_pnl['position_count'],
                        'remaining_funds': remaining_funds,  # 剩余资金总额
                        'positions_pnl_detail': str(positions_pnl['positions_detail'])  # 转为字符串以便CSV保存
                    }
                    
                    self.trade_records.append(trade_record)
                    trades.append(trade_record)
                    positions_to_remove.append(grid_price)
        
        # 移除已卖出的多头持仓
        for grid_price in positions_to_remove:
            del self.long_positions[grid_price]
        
        return trades
    
    def _check_short_opportunities(self, current_price: float, timestamp: str) -> List[Dict]:
        """检查做空机会（价格涨到网格线时）"""
        trades = []
        
        # 找到最接近当前价格的网格（从上往下找第一个小于当前价格的网格）
        target_grid = None
        for i in range(len(self.grid_prices) - 1, -1, -1):  # 从高价格往低价格找
            grid_price = self.grid_prices[i]
            
            # 做空条件：当前价格刚好突破网格价格，且该网格没有空头持仓
            if (current_price >= grid_price and  
                grid_price not in self.short_positions and
                (self.last_price is None or self.last_price < grid_price)):  # 价格是刚突破的
                target_grid = grid_price
                break
        
        # 只开空一个最接近的网格
        if target_grid is not None:
            # 执行做空（合约交易使用杠杆）
            position_value = self.per_grid_amount * self.leverage  # 实际持仓价值
            quantity = position_value / current_price  # 持仓数量（空头为负数）
            
            # 计算所需保证金（实际持仓价值 / 杠杆倍数）
            margin_required = self.per_grid_amount
            commission = margin_required * BACKTEST_CONFIG["commission_rate"]
            total_cost = margin_required + commission
            
            if self.cash >= total_cost:
                self.short_positions[target_grid] = quantity  # 空头持仓
                self.cash -= total_cost
                self.trade_count += 1
                
                # 计算当前所有持仓的盈亏情况
                positions_pnl = self._calculate_positions_pnl(current_price)
                
                # 计算剩余资金总额 (现金 + 未实现盈亏)
                remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
                
                trade_record = {
                    'timestamp': timestamp,
                    'type': 'SELL_SHORT',
                    'direction': 'SHORT',
                    'price': current_price,
                    'quantity': quantity,
                    'amount': position_value,  # 实际持仓价值
                    'margin': margin_required,  # 所需保证金
                    'commission': commission,
                    'grid_price': target_grid,
                    'cash': self.cash,
                    'total_value': self._calculate_total_value(current_price),
                    'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
                    'position_count': positions_pnl['position_count'],
                    'remaining_funds': remaining_funds,  # 剩余资金总额
                    'positions_pnl_detail': str(positions_pnl['positions_detail'])  # 转为字符串以便CSV保存
                }
                
                self.trade_records.append(trade_record)
                trades.append(trade_record)
        
        return trades
    
    def _check_short_close_opportunities(self, current_price: float, timestamp: str) -> List[Dict]:
        """检查空头平仓机会（价格下跌时）"""
        trades = []
        positions_to_remove = []
        
        for grid_price, quantity in self.short_positions.items():
            # 找到当前空头持仓对应的平仓价格（下一个更低的网格）
            grid_index = self.grid_prices.index(grid_price)
            if grid_index > 0:  # 确保不是最低的网格
                cover_price = self.grid_prices[grid_index - 1]
                
                # 平空条件：价格确实跌破下一个网格线
                if current_price <= cover_price * 0.999:  # 需要确实跌破
                    # 执行平空（买入平仓）
                    position_value = quantity * current_price  # 当前持仓价值
                    commission = position_value * BACKTEST_CONFIG["commission_rate"]
                    
                    # 计算利润（空头盈利：开空价格高于平空价格）
                    original_position_value = self.per_grid_amount * self.leverage
                    # 空头盈利 = 开空时卖出价值 - 平空时买入价值
                    pnl = (quantity * grid_price) - (quantity * current_price) - commission
                    self.total_profit += pnl
                    
                    # 释放保证金 + 盈亏
                    margin_released = self.per_grid_amount  # 释放的保证金
                    self.cash += margin_released + pnl
                    self.trade_count += 1
                    
                    # 计算当前所有持仓的盈亏情况（平仓后）
                    positions_pnl = self._calculate_positions_pnl(current_price)
                    
                    # 计算剩余资金总额 (现金 + 未实现盈亏)
                    remaining_funds = self.cash + positions_pnl['total_unrealized_pnl']
                    
                    trade_record = {
                        'timestamp': timestamp,
                        'type': 'BUY_SHORT',
                        'direction': 'SHORT',
                        'price': current_price,
                        'quantity': quantity,
                        'amount': position_value,
                        'commission': commission,
                        'grid_price': grid_price,
                        'cover_price': cover_price,
                        'profit': pnl,
                        'margin_released': margin_released,
                        'cash': self.cash,
                        'total_value': self._calculate_total_value(current_price),
                        'total_unrealized_pnl': positions_pnl['total_unrealized_pnl'],
                        'position_count': positions_pnl['position_count'],
                        'remaining_funds': remaining_funds,  # 剩余资金总额
                        'positions_pnl_detail': str(positions_pnl['positions_detail'])  # 转为字符串以便CSV保存
                    }
                    
                    self.trade_records.append(trade_record)
                    trades.append(trade_record)
                    positions_to_remove.append(grid_price)
        
        # 移除已平空的持仓
        for grid_price in positions_to_remove:
            del self.short_positions[grid_price]
        
        return trades
    
    def _calculate_total_value(self, current_price: float) -> float:
        """计算总资产价值"""
        # 多头持仓价值
        long_value = sum(quantity * current_price for quantity in self.long_positions.values())
        
        # 空头持仓价值（空头盈利时价值为正）
        short_value = 0
        for grid_price, quantity in self.short_positions.items():
            # 空头未实现盈亏 = 开空价格 - 当前价格
            short_pnl = quantity * (grid_price - current_price)
            short_value += short_pnl
        
        return self.cash + long_value + short_value
    
    def _calculate_positions_pnl(self, current_price: float) -> Dict:
        """计算所有持仓的盈亏情况"""
        positions_pnl = {}
        total_unrealized_pnl = 0
        
        # 计算多头持仓盈亏
        for grid_price, quantity in self.long_positions.items():
            original_position_value = self.per_grid_amount * self.leverage
            current_position_value = quantity * current_price
            position_pnl = current_position_value - original_position_value
            
            positions_pnl[f"long_{grid_price:.6f}"] = {
                'type': 'LONG',
                'entry_price': grid_price,
                'quantity': quantity,
                'original_value': original_position_value,
                'current_value': current_position_value,
                'pnl': position_pnl
            }
            total_unrealized_pnl += position_pnl
        
        # 计算空头持仓盈亏
        for grid_price, quantity in self.short_positions.items():
            original_position_value = self.per_grid_amount * self.leverage
            # 空头盈亏 = 开空价格 * 数量 - 当前价格 * 数量
            position_pnl = quantity * (grid_price - current_price)
            current_position_value = quantity * current_price
            
            positions_pnl[f"short_{grid_price:.6f}"] = {
                'type': 'SHORT',
                'entry_price': grid_price,
                'quantity': quantity,
                'original_value': original_position_value,
                'current_value': current_position_value,
                'pnl': position_pnl
            }
            total_unrealized_pnl += position_pnl
        
        total_positions = len(self.long_positions) + len(self.short_positions)
        
        return {
            'positions_detail': positions_pnl,
            'total_unrealized_pnl': total_unrealized_pnl,
            'position_count': total_positions
        }
    
    def get_strategy_stats(self, current_price: float) -> Dict:
        """获取策略统计信息"""
        total_value = self._calculate_total_value(current_price)
        unrealized_pnl = total_value - self.investment_amount
        
        # 计算持仓价值
        long_position_value = sum(quantity * current_price for quantity in self.long_positions.values())
        short_position_value = sum(quantity * (grid_price - current_price) 
                                 for grid_price, quantity in self.short_positions.items())
        total_position_value = long_position_value + short_position_value
        
        return {
            'initial_investment': self.investment_amount,
            'current_cash': self.cash,
            'long_position_count': len(self.long_positions),
            'short_position_count': len(self.short_positions),
            'total_position_count': len(self.long_positions) + len(self.short_positions),
            'position_value': total_position_value,
            'long_position_value': long_position_value,
            'short_position_value': short_position_value,
            'total_value': total_value,
            'realized_profit': self.total_profit,
            'unrealized_pnl': unrealized_pnl,
            'total_return': (total_value / self.investment_amount - 1) * 100,
            'trade_count': self.trade_count,
            'grid_prices': self.grid_prices,
            'long_positions': self.long_positions,
            'short_positions': self.short_positions,
            'positions': self.long_positions  # 保持向后兼容性
        } 