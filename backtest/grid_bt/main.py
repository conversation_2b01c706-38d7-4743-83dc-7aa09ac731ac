"""
网格策略回测主程序
"""

import os
import sys
from datetime import datetime

from data_downloader import DataDownloader
from backtest_engine import BacktestEngine
from config import GRID_CONFIG, SYMBOL


def main():
    """主函数"""
    print("="*60)
    print("DOGEUSDT 网格策略回测系统")
    print("="*60)
    
    try:
        # 1. 下载或加载数据
        print("\n1. 准备数据...")
        downloader = DataDownloader()
        data = downloader.load_data()
        
        if data is None or data.empty:
            print("错误：无法获取数据")
            return
        
        print(f"数据加载成功: {len(data)} 条记录")
        print(f"时间范围: {data.index[0]} 到 {data.index[-1]}")
        print(f"价格范围: {data['low'].min():.6f} - {data['high'].max():.6f} USDT")
        
        # 检查价格是否在网格范围内
        price_min, price_max = data['low'].min(), data['high'].max()
        grid_lower, grid_upper = GRID_CONFIG["price_lower"], GRID_CONFIG["price_upper"]
        
        if price_max < grid_lower or price_min > grid_upper:
            print(f"警告: 历史价格范围 [{price_min:.6f}, {price_max:.6f}] 与网格范围 [{grid_lower:.6f}, {grid_upper:.6f}] 不重叠")
            print("请调整网格参数或选择其他时间段的数据")
        
        # 2. 运行回测
        print("\n2. 运行网格策略回测...")
        engine = BacktestEngine()  # 使用默认配置创建
        portfolio_df, trades_df = engine.run_backtest(data)
        
        # 3. 生成报告
        print("\n3. 生成回测报告...")
        results = engine.generate_report_v2(portfolio_df, trades_df)
        
        print("\n回测完成！")
        
    except KeyboardInterrupt:
        print("\n回测被用户中断")
    except Exception as e:
        print(f"回测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 