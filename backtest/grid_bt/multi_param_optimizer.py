"""
多参数网格策略优化器
同时优化网格数量、价格区间、每格资金等多个参数
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import multiprocessing as mp
from copy import deepcopy
import matplotlib.pyplot as plt
import matplotlib
from itertools import product

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

from config import GRID_CONFIG, BACKTEST_CONFIG, PATHS
from grid_strategy import BinanceGridStrategy
from backtest_engine import BacktestEngine
from data_downloader import DataDownloader


class MultiParamOptimizer:
    def __init__(self):
        """初始化多参数优化器"""
        self.base_config = deepcopy(GRID_CONFIG)
        self.results = []
        
    def optimize_multiple_params(self, 
                                grid_counts=None,
                                price_ranges=None, 
                                per_grid_amounts=None,
                                processes=None):
        """
        多参数优化
        
        Args:
            grid_counts: 网格数量列表
            price_ranges: 价格区间列表 [(下限, 上限), ...]
            per_grid_amounts: 每格资金列表
            processes: 并行进程数
            
        Returns:
            pandas.DataFrame: 优化结果
        """
        # 默认参数范围
        if grid_counts is None:
            grid_counts = [20, 30, 50, 70, 100, 150, 200]
            
        if price_ranges is None:
            # 基于当前数据范围设置不同的价格区间
            price_ranges = [
                (0.08, 0.35),  # 当前配置
                (0.10, 0.30),  # 稍微收窄
                (0.12, 0.28),  # 更窄区间
                (0.08, 0.40),  # 扩大区间
                (0.10, 0.35),  # 混合调整
            ]
            
        if per_grid_amounts is None:
            per_grid_amounts = [5.0, 8.0, 10.0, 15.0, 20.0]
        
        if processes is None:
            processes = min(mp.cpu_count(), 8)  # 限制最大进程数避免内存不足
        
        print(f"开始多参数优化...")
        print(f"网格数量: {grid_counts}")
        print(f"价格区间: {price_ranges}")
        print(f"每格资金: {per_grid_amounts}")
        print(f"并行进程数: {processes}")
        
        # 加载数据
        downloader = DataDownloader()
        data = downloader.load_data()
        if data is None:
            raise Exception("无法加载数据")
        
        print(f"数据加载完成，价格范围: {data['low'].min():.6f} - {data['high'].max():.6f}")
        
        # 创建所有参数组合
        all_combinations = list(product(grid_counts, price_ranges, per_grid_amounts))
        total_tests = len(all_combinations)
        print(f"总测试组合数: {total_tests}")
        
        # 过滤不合理的组合
        valid_combinations = []
        for grid_count, (price_lower, price_upper), per_grid_amount in all_combinations:
            # 检查资金是否足够
            max_grids_can_afford = self.base_config['investment_amount'] / per_grid_amount
            if grid_count <= max_grids_can_afford * 0.8:  # 留一些缓冲
                valid_combinations.append((grid_count, price_lower, price_upper, per_grid_amount))
        
        print(f"有效组合数: {len(valid_combinations)}")
        
        # 创建测试参数
        test_params = []
        for i, (grid_count, price_lower, price_upper, per_grid_amount) in enumerate(valid_combinations):
            config = deepcopy(self.base_config)
            config['grid_num'] = grid_count
            config['price_lower'] = price_lower
            config['price_upper'] = price_upper
            config['per_grid_amount'] = per_grid_amount
            
            test_params.append((config, data, i+1, len(valid_combinations)))
        
        # 并行回测
        if processes > 1:
            print(f"启动 {processes} 个并行进程...")
            with mp.Pool(processes=processes) as pool:
                results = pool.map(self._run_multi_param_backtest, test_params)
        else:
            results = [self._run_multi_param_backtest(params) for params in test_params]
        
        # 整理结果
        self.results = [r for r in results if r is not None]
        df_results = pd.DataFrame(self.results)
        
        if len(df_results) == 0:
            print("没有成功的回测结果")
            return None
        
        # 排序并显示结果
        df_results = df_results.sort_values('composite_score', ascending=False)
        
        print(f"\n=== 多参数优化结果 TOP 10 ===")
        display_cols = ['grid_count', 'price_range', 'per_grid_amount', 'total_return', 'sharpe_ratio', 'max_drawdown', 'composite_score']
        print(df_results[display_cols].head(10).to_string(index=False))
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"{PATHS['results_dir']}/multi_param_optimization_{timestamp}.csv"
        df_results.to_csv(result_file, index=False)
        print(f"\n结果已保存到: {result_file}")
        
        # 生成分析图表
        self._plot_multi_param_results(df_results, timestamp)
        
        return df_results
    
    def _run_multi_param_backtest(self, params):
        """运行单次多参数回测"""
        config, data, test_num, total_tests = params
        
        try:
            grid_count = config['grid_num']
            price_range = f"{config['price_lower']:.2f}-{config['price_upper']:.2f}"
            per_grid = config['per_grid_amount']
            
            if test_num % 10 == 0:
                print(f"进度: {test_num}/{total_tests} - 网格{grid_count}, 区间{price_range}, 每格{per_grid}")
            
            # 创建策略和回测引擎
            strategy = BinanceGridStrategy(
                price_upper=config['price_upper'],
                price_lower=config['price_lower'], 
                grid_num=config['grid_num'],
                investment_amount=config['investment_amount'],
                grid_mode=config['grid_mode']
            )
            engine = BacktestEngine(strategy, BACKTEST_CONFIG)
            
            # 运行回测
            portfolio_df, trades_df = engine.run_backtest(data)
            
            if portfolio_df is None or len(portfolio_df) == 0:
                return None
            
            # 计算关键指标
            initial_value = portfolio_df['total_value'].iloc[0]
            final_value = portfolio_df['total_value'].iloc[-1]
            total_return = (final_value - initial_value) / initial_value * 100
            
            # 计算夏普比率
            portfolio_df['daily_return'] = portfolio_df['total_value'].pct_change()
            sharpe_ratio = self._calculate_sharpe_ratio(portfolio_df['daily_return'])
            
            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(portfolio_df['total_value'])
            
            # 交易次数和平均利润
            trade_count = len(trades_df) if trades_df is not None else 0
            avg_trade_profit = 0
            if trades_df is not None and len(trades_df) > 0:
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                if len(sell_trades) > 0:
                    avg_trade_profit = sell_trades['profit'].mean()
            
            # 计算理论网格密度（每个价格单位的网格数）
            price_span = config['price_upper'] - config['price_lower']
            grid_density = grid_count / price_span
            
            # 计算资金利用率
            max_position_value = portfolio_df['position_value'].max()
            capital_utilization = max_position_value / config['investment_amount'] * 100
            
            # 计算综合评分
            composite_score = self._calculate_composite_score(total_return, sharpe_ratio, max_drawdown)
            
            result = {
                'grid_count': grid_count,
                'price_lower': config['price_lower'],
                'price_upper': config['price_upper'],
                'price_range': price_range,
                'per_grid_amount': per_grid,
                'total_return': round(total_return, 2),
                'final_value': round(final_value, 2),
                'sharpe_ratio': round(sharpe_ratio, 4),
                'max_drawdown': round(max_drawdown, 2),
                'trade_count': trade_count,
                'avg_trade_profit': round(avg_trade_profit, 4),
                'grid_density': round(grid_density, 2),
                'capital_utilization': round(capital_utilization, 2),
                'composite_score': round(composite_score, 4),
                'investment_amount': config['investment_amount'],
                'leverage': config['leverage']
            }
            
            return result
            
        except Exception as e:
            if test_num % 50 == 0:  # 只显示部分错误避免刷屏
                print(f"测试 {test_num} 出错: {e}")
            return None
    
    def _calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """计算夏普比率"""
        if len(returns) <= 1:
            return 0
        
        returns = returns.dropna()
        if len(returns) <= 1:
            return 0
            
        excess_returns = returns - risk_free_rate / 252
        if returns.std() == 0:
            return 0
        return excess_returns.mean() / returns.std() * np.sqrt(252)
    
    def _calculate_max_drawdown(self, values):
        """计算最大回撤"""
        peak = values.expanding().max()
        drawdown = (values - peak) / peak * 100
        return abs(drawdown.min())
    
    def _calculate_composite_score(self, total_return, sharpe_ratio, max_drawdown):
        """计算综合评分"""
        # 标准化评分 (0-1)
        return_score = min(total_return / 10000, 1.0)  # 假设10000%为满分
        sharpe_score = min(max(sharpe_ratio, 0) / 3.0, 1.0)  # 假设3.0为优秀夏普比率
        drawdown_score = max(1 - max_drawdown / 50, 0)  # 假设50%回撤为0分
        
        # 加权综合评分
        return 0.5 * return_score + 0.3 * sharpe_score + 0.2 * drawdown_score
    
    def _plot_multi_param_results(self, df_results, timestamp):
        """绘制多参数优化结果图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 网格数量 vs 收益率（按价格区间着色）
        scatter = ax1.scatter(df_results['grid_count'], df_results['total_return'], 
                            c=df_results['price_upper'] - df_results['price_lower'], 
                            cmap='viridis', alpha=0.6)
        ax1.set_xlabel('网格数量')
        ax1.set_ylabel('总收益率 (%)')
        ax1.set_title('网格数量 vs 收益率（颜色=价格区间宽度）')
        ax1.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax1, label='价格区间宽度')
        
        # 2. 每格资金 vs 收益率
        scatter2 = ax2.scatter(df_results['per_grid_amount'], df_results['total_return'], 
                             c=df_results['grid_count'], cmap='plasma', alpha=0.6)
        ax2.set_xlabel('每格资金 (USDT)')
        ax2.set_ylabel('总收益率 (%)')
        ax2.set_title('每格资金 vs 收益率（颜色=网格数量）')
        ax2.grid(True, alpha=0.3)
        plt.colorbar(scatter2, ax=ax2, label='网格数量')
        
        # 3. 综合评分 vs 网格数量
        ax3.scatter(df_results['grid_count'], df_results['composite_score'], 
                   c=df_results['max_drawdown'], cmap='RdYlBu_r', alpha=0.6)
        ax3.set_xlabel('网格数量')
        ax3.set_ylabel('综合评分')
        ax3.set_title('网格数量 vs 综合评分（颜色=最大回撤）')
        ax3.grid(True, alpha=0.3)
        
        # 标记最优点
        best_idx = df_results['composite_score'].idxmax()
        best_grid = df_results.loc[best_idx, 'grid_count']
        best_score = df_results.loc[best_idx, 'composite_score']
        ax3.plot(best_grid, best_score, 'ro', markersize=10, label=f'最优: {best_grid}格')
        ax3.legend()
        
        # 4. 交易次数 vs 收益率
        ax4.scatter(df_results['trade_count'], df_results['total_return'], 
                   c=df_results['grid_count'], cmap='cool', alpha=0.6)
        ax4.set_xlabel('交易次数')
        ax4.set_ylabel('总收益率 (%)')
        ax4.set_title('交易次数 vs 收益率（颜色=网格数量）')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = f"{PATHS['results_dir']}/multi_param_optimization_chart_{timestamp}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"分析图表已保存到: {chart_file}")
        plt.show()
        
        # 生成热力图
        self._plot_heatmap(df_results, timestamp)
    
    def _plot_heatmap(self, df_results, timestamp):
        """生成参数热力图"""
        # 创建网格数量 vs 每格资金的热力图
        pivot_data = df_results.pivot_table(
            values='composite_score', 
            index='per_grid_amount', 
            columns='grid_count', 
            aggfunc='mean'
        )
        
        plt.figure(figsize=(12, 8))
        plt.imshow(pivot_data.values, cmap='RdYlBu', aspect='auto', origin='lower')
        plt.colorbar(label='综合评分')
        plt.xlabel('网格数量')
        plt.ylabel('每格资金 (USDT)')
        plt.title('参数组合热力图（综合评分）')
        
        # 设置刻度标签
        plt.xticks(range(len(pivot_data.columns)), pivot_data.columns)
        plt.yticks(range(len(pivot_data.index)), [f'{x:.1f}' for x in pivot_data.index])
        
        # 保存热力图
        heatmap_file = f"{PATHS['results_dir']}/param_heatmap_{timestamp}.png"
        plt.savefig(heatmap_file, dpi=300, bbox_inches='tight')
        print(f"参数热力图已保存到: {heatmap_file}")
        plt.show()
    
    def get_optimal_config(self, df_results):
        """获取最优配置"""
        if len(df_results) == 0:
            return None
        
        best_result = df_results.iloc[0]  # 已按综合评分排序
        
        optimal_config = {
            'grid_num': int(best_result['grid_count']),
            'price_lower': best_result['price_lower'],
            'price_upper': best_result['price_upper'],
            'per_grid_amount': best_result['per_grid_amount'],
        }
        
        print(f"\n=== 最优配置建议 ===")
        print(f"网格数量: {optimal_config['grid_num']} (当前: {self.base_config['grid_num']})")
        print(f"价格下限: {optimal_config['price_lower']:.3f} (当前: {self.base_config['price_lower']})")
        print(f"价格上限: {optimal_config['price_upper']:.3f} (当前: {self.base_config['price_upper']})")
        print(f"每格资金: {optimal_config['per_grid_amount']:.1f} (当前: {self.base_config['per_grid_amount']})")
        print(f"\n预期效果:")
        print(f"总收益率: {best_result['total_return']:.2f}%")
        print(f"夏普比率: {best_result['sharpe_ratio']:.4f}")
        print(f"最大回撤: {best_result['max_drawdown']:.2f}%")
        print(f"交易次数: {best_result['trade_count']}")
        print(f"综合评分: {best_result['composite_score']:.4f}")
        
        return optimal_config


def main():
    """主函数：运行多参数优化"""
    optimizer = MultiParamOptimizer()
    
    # 运行多参数优化
    results = optimizer.optimize_multiple_params()
    
    if results is not None and len(results) > 0:
        # 获取最优配置
        optimal_config = optimizer.get_optimal_config(results)
        
        if optimal_config:
            print(f"\n建议更新config.py中的配置参数")


if __name__ == "__main__":
    main()