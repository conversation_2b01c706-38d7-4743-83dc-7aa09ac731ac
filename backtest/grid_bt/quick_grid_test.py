"""
快速网格数量测试
"""

import pandas as pd
import numpy as np
from grid_strategy import BinanceGridStrategy
from backtest_engine import BacktestEngine
from data_downloader import DataDownloader
from config import GRID_CONFIG, BACKTEST_CONFIG
from copy import deepcopy

def test_grid_counts():
    """快速测试不同网格数量的表现"""
    
    # 加载数据（使用前1000条数据进行快速测试）
    downloader = DataDownloader()
    data = downloader.load_data()
    if data is None:
        print("无法加载数据")
        return
    
    # 只使用前1000条数据进行快速测试
    test_data = data.head(1000)
    print(f"使用测试数据: {len(test_data)} 条记录")
    print(f"价格范围: {test_data['low'].min():.6f} - {test_data['high'].max():.6f}")
    
    # 测试不同的网格数量
    grid_counts = [20, 50, 100, 200]
    results = []
    
    for grid_count in grid_counts:
        print(f"\n=== 测试网格数量: {grid_count} ===")
        
        try:
            # 创建策略
            strategy = BinanceGridStrategy(
                price_upper=GRID_CONFIG['price_upper'],
                price_lower=GRID_CONFIG['price_lower'], 
                grid_num=grid_count,
                investment_amount=GRID_CONFIG['investment_amount'],
                grid_mode=GRID_CONFIG['grid_mode']
            )
            
            # 创建回测引擎
            engine = BacktestEngine(strategy, BACKTEST_CONFIG)
            
            # 运行回测
            portfolio_df, trades_df = engine.run_backtest(test_data)
            
            if portfolio_df is not None and len(portfolio_df) > 0:
                # 计算基本指标
                initial_value = portfolio_df['total_value'].iloc[0]
                final_value = portfolio_df['total_value'].iloc[-1]
                total_return = (final_value - initial_value) / initial_value * 100
                trade_count = len(trades_df) if trades_df is not None else 0
                
                result = {
                    'grid_count': grid_count,
                    'total_return': round(total_return, 2),
                    'final_value': round(final_value, 2),
                    'trade_count': trade_count
                }
                results.append(result)
                
                print(f"收益率: {total_return:.2f}%")
                print(f"最终价值: {final_value:.2f} USDT")
                print(f"交易次数: {trade_count}")
            else:
                print("回测失败")
                
        except Exception as e:
            print(f"测试出错: {e}")
    
    # 显示结果对比
    if results:
        print(f"\n=== 结果对比 ===")
        print("网格数量 | 收益率(%) | 最终价值 | 交易次数")
        print("-" * 45)
        for r in results:
            print(f"{r['grid_count']:^8} | {r['total_return']:^8} | {r['final_value']:^8} | {r['trade_count']:^8}")
        
        # 找到最优配置
        best = max(results, key=lambda x: x['total_return'])
        print(f"\n最优网格数量: {best['grid_count']} (收益率: {best['total_return']:.2f}%)")
    
    return results

if __name__ == "__main__":
    test_grid_counts()