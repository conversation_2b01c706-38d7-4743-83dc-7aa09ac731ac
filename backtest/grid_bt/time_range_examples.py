"""
时间范围回测示例
演示如何使用不同的时间范围进行回测
"""

from datetime import datetime
from backtest_engine import BacktestEngine
from data_downloader import DataDownloader
from config import BACKTEST_CONFIG

def run_backtest_with_time_range(start_date, end_date, start_time="00:00:00", end_time="23:59:59", description=""):
    """运行指定时间范围的回测"""
    print(f"\n{'='*60}")
    print(f"回测示例: {description}")
    print(f"时间范围: {start_date} {start_time} 到 {end_date} {end_time}")
    print(f"{'='*60}")
    
    # 临时修改配置
    original_config = BACKTEST_CONFIG.copy()
    BACKTEST_CONFIG.update({
        "start_date": start_date,
        "end_date": end_date,
        "start_time": start_time,
        "end_time": end_time
    })
    
    try:
        # 加载数据
        downloader = DataDownloader()
        data = downloader.load_data()
        
        # 运行回测
        engine = BacktestEngine()
        portfolio_df, trades_df = engine.run_backtest(data)
        
        # 简化报告
        if len(portfolio_df) > 0:
            initial_value = portfolio_df['total_value'].iloc[0]
            final_value = portfolio_df['total_value'].iloc[-1]
            total_return = (final_value - initial_value) / initial_value * 100
            trade_count = len(trades_df) if trades_df is not None else 0
            
            print(f"\n📊 回测结果:")
            print(f"   初始资金: {initial_value:.2f} USDT")
            print(f"   最终资金: {final_value:.2f} USDT")
            print(f"   收益率: {total_return:.2f}%")
            print(f"   交易次数: {trade_count}")
        
    except Exception as e:
        print(f"回测出错: {e}")
    
    finally:
        # 恢复原始配置
        BACKTEST_CONFIG.update(original_config)

def main():
    """运行各种时间范围示例"""
    
    print("网格策略时间范围回测示例")
    print("=" * 60)
    
    # 示例1: 回测5月前10天
    run_backtest_with_time_range(
        "2025-05-01", "2025-05-10", 
        description="5月前10天完整回测"
    )
    
    # 示例2: 回测单个交易日
    run_backtest_with_time_range(
        "2025-05-15", "2025-05-15",
        description="单日回测(5月15日)"
    )
    
    # 示例3: 回测交易时间段
    run_backtest_with_time_range(
        "2025-05-01", "2025-05-31",
        "09:00:00", "21:00:00",
        description="交易时间段回测(9AM-9PM)"
    )
    
    # 示例4: 回测一周
    run_backtest_with_time_range(
        "2025-05-20", "2025-05-26",
        description="一周回测(5月20-26日)"
    )
    
    print(f"\n✅ 时间范围回测示例完成!")
    print(f"💡 提示: 修改 config.py 中的 BACKTEST_CONFIG 可以设置自定义时间范围")

if __name__ == "__main__":
    main()