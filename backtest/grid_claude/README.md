# DOGE/USDT 网格交易策略回测系统

这是一个完整的币安DOGE/USDT网格交易策略回测系统，使用Python实现，支持历史数据下载、策略回测、性能分析和可视化。

## 功能特性

- 🔄 **自动数据下载**: 从币安API自动下载DOGE/USDT历史数据
- 📊 **网格策略回测**: 完整的网格交易策略实现和回测
- 📈 **性能分析**: 收益率、最大回撤、夏普比率等关键指标
- 🎯 **参数优化**: 网格数量和价格区间的敏感性分析
- 📉 **可视化图表**: 多种图表展示策略表现
- 📝 **详细报告**: 自动生成回测报告

## 项目结构

```
grid_claude/
├── main.py                 # 主程序入口
├── data_downloader.py      # 币安数据下载模块
├── grid_strategy.py        # 网格策略核心逻辑
├── backtest_engine.py      # 回测引擎和性能指标
├── visualization.py        # 可视化模块
├── requirements.txt        # 依赖包列表
├── config.json            # 配置文件(运行后自动生成)
├── data/                  # 数据存储目录
└── results/               # 结果输出目录
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

1. **运行完整回测**:
```bash
python main.py
```

2. **仅下载数据**:
```bash
python main.py --download-only
```

3. **使用现有数据运行回测**:
```bash
python main.py --no-download
```

4. **运行参数敏感性分析**:
```bash
python main.py --analysis
```

5. **不生成图表**:
```bash
python main.py --no-plots
```

## 配置参数

系统会自动生成`config.json`配置文件，主要参数说明：

### 交易参数
- `initial_capital`: 初始资金 (默认: 1000 USDT)
- `lower_price`: 网格下限价格 (默认: 0.05 USDT)
- `upper_price`: 网格上限价格 (默认: 0.15 USDT)
- `grid_count`: 网格数量 (默认: 10个)
- `fee_rate`: 手续费率 (默认: 0.04%)

### 数据参数
- `symbol`: 交易对 (默认: DOGEUSDT)
- `interval`: 数据粒度 (默认: 5m)
- `months`: 历史数据月数 (默认: 3个月)

## 策略说明

### 网格交易原理
网格交易是一种量化交易策略，在预设价格区间内：
1. 将价格区间分成若干网格
2. 在每个网格价位放置买卖订单
3. 价格下跌时买入，上涨时卖出
4. 通过频繁的低买高卖获取收益

### 关键逻辑
- **初始化**: 在所有网格价位放置买单
- **价格触发**: 当价格触及某个网格时执行订单
- **订单配对**: 买单成交后在上一级放置卖单
- **盈利机制**: 通过价格波动的买卖差价获利

## 性能指标

系统计算以下关键指标：

- **总收益率**: 策略总体收益表现
- **最大回撤**: 资产价值最大跌幅
- **夏普比率**: 风险调整后收益
- **胜率**: 盈利交易占比
- **超额收益**: 相对买入持有的超额表现

## 可视化图表

1. **策略表现对比**: 网格策略 vs 买入持有
2. **交易信号图**: 价格走势与买卖信号
3. **回撤分析**: 策略回撤情况
4. **性能指标**: 各项指标对比
5. **参数敏感性**: 不同参数组合的表现

## 使用示例

### 基本回测
```python
from grid_strategy import GridConfig
from backtest_engine import GridBacktester
from data_downloader import BinanceDataDownloader

# 下载数据
downloader = BinanceDataDownloader()
data = downloader.download_historical_data("DOGEUSDT", "5m", 3)

# 配置策略
config = GridConfig(
    initial_capital=1000,
    lower_price=0.05,
    upper_price=0.15,
    grid_count=10
)

# 运行回测
backtester = GridBacktester(config)
result = backtester.run_backtest(data)
```

### 参数优化
```python
# 测试不同网格数量
grid_counts = [5, 10, 15, 20]
price_ranges = [(0.04, 0.12), (0.05, 0.15), (0.06, 0.18)]

sensitivity_df = backtester.parameter_sensitivity_analysis(
    data, grid_counts, price_ranges
)
```

## 注意事项

1. **网络连接**: 需要稳定的网络连接下载币安数据
2. **数据质量**: 建议使用最近的数据进行回测
3. **参数设置**: 网格区间应覆盖价格主要波动范围
4. **手续费影响**: 频繁交易会产生较多手续费
5. **风险管理**: 策略适用于震荡市场，趋势市场表现有限

## 许可证

本项目仅供学习和研究使用，投资有风险，请谨慎使用。

## 更新日志

- v1.0.0: 初始版本，包含完整的网格交易回测功能