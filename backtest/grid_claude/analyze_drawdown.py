#!/usr/bin/env python3
"""
分析回撤问题
"""

import pandas as pd
import numpy as np
from grid_strategy import GridConfig, GridTradingStrategy
from backtest_engine import GridBacktester

def analyze_drawdown_issue():
    print("=== 分析回撤问题 ===")
    
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.1,
        upper_price=0.3,
        grid_count=100,
        fee_rate=0.0004,
        min_order_amount=10.0,
        is_futures=True,
        leverage=20,
        position_side="LONG"
    )
    
    # 加载数据
    price_data = pd.read_csv('data/DOGEUSDT_5m_3months.csv')
    price_data['datetime'] = pd.to_datetime(price_data['datetime'])
    
    # 运行回测
    backtester = GridBacktester(config)
    result = backtester.run_backtest(price_data)
    
    # 分析组合价值变化
    portfolio_values = result.portfolio_values
    print(f"组合价值数据点数: {len(portfolio_values)}")
    print(f"初始价值: ${portfolio_values.iloc[0]:.2f}")
    print(f"最终价值: ${portfolio_values.iloc[-1]:.2f}")
    print(f"最高价值: ${portfolio_values.max():.2f}")
    print(f"最低价值: ${portfolio_values.min():.2f}")
    
    # 计算回撤序列
    peak = portfolio_values.expanding().max()
    drawdown = (portfolio_values - peak) / peak
    max_drawdown = abs(drawdown.min())
    
    print(f"\n回撤分析:")
    print(f"最大回撤: {max_drawdown:.4%}")
    print(f"回撤序列统计:")
    print(f"  均值: {drawdown.mean():.6f}")
    print(f"  标准差: {drawdown.std():.6f}")
    print(f"  最小值: {drawdown.min():.6f}")
    print(f"  非零回撤点数: {(drawdown < 0).sum()}")
    
    # 检查组合价值是否始终单调递增
    value_changes = portfolio_values.diff()
    negative_changes = value_changes[value_changes < 0]
    
    print(f"\n价值变化分析:")
    print(f"总变化点数: {len(value_changes) - 1}")
    print(f"负变化点数: {len(negative_changes)}")
    print(f"负变化占比: {len(negative_changes) / (len(value_changes) - 1):.2%}")
    
    if len(negative_changes) > 0:
        print(f"最大单次下跌: ${negative_changes.min():.4f}")
        print(f"负变化均值: ${negative_changes.mean():.4f}")
    
    # 分析价值变化原因
    print(f"\n价值变化模式:")
    print(f"价值变化范围: ${value_changes.min():.4f} 到 ${value_changes.max():.4f}")
    
    # 检查是否存在未配对的交易
    strategy = backtester.strategy
    if hasattr(strategy, 'trade_pairs'):
        total_orders = len(strategy.filled_orders)
        paired_orders = len(strategy.trade_pairs) * 2
        unpaired_orders = total_orders - paired_orders
        
        print(f"\n交易配对分析:")
        print(f"总订单数: {total_orders}")
        print(f"配对数量: {len(strategy.trade_pairs)}")
        print(f"已配对订单数: {paired_orders}")
        print(f"未配对订单数: {unpaired_orders}")
        
        if unpaired_orders > 0:
            print("⚠️  存在未配对的订单，这可能导致回撤计算不准确")
    
    # 查看组合价值序列的前100个点
    print(f"\n前20个组合价值变化:")
    for i in range(min(20, len(portfolio_values))):
        if i == 0:
            change = 0
        else:
            change = portfolio_values.iloc[i] - portfolio_values.iloc[i-1]
        print(f"  {i}: ${portfolio_values.iloc[i]:.2f} (变化: ${change:+.4f})")

if __name__ == "__main__":
    analyze_drawdown_issue()