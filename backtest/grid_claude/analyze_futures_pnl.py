#!/usr/bin/env python3
"""
深度分析合约交易盈亏计算问题
"""

import pandas as pd
from grid_strategy import GridConfig, GridTradingStrategy, OrderType

def analyze_futures_pnl_logic():
    print("=== 分析合约交易盈亏逻辑 ===")
    
    # 使用极简配置测试
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.18,
        upper_price=0.22,
        grid_count=3,  # 只有3个网格点，便于分析
        fee_rate=0.0001,  # 极低手续费
        min_order_amount=10.0,
        is_futures=True,
        leverage=2,  # 低杠杆
        position_side="LONG"
    )
    
    strategy = GridTradingStrategy(config)
    print(f"网格价位: {strategy.grid_levels}")
    print(f"初始保证金: ${strategy.available_margin:.2f}")
    
    # 设置初始价格并放置订单
    initial_price = 0.20
    strategy._place_initial_orders(initial_price)
    
    print(f"\n=== 初始状态 ===")
    print(f"活跃订单数: {len(strategy.active_orders)}")
    print(f"可用保证金: ${strategy.available_margin:.2f}")
    print(f"已用保证金: ${strategy.used_margin:.2f}")
    
    print("活跃订单:")
    for order_id, order in strategy.active_orders.items():
        print(f"  {order.order_type.value}: ${order.price:.2f}, 数量{order.quantity:.1f}")
    
    # 模拟完整的网格交易循环
    print(f"\n=== 模拟网格交易循环 ===")
    
    # 第1步：价格下跌，触发买单
    timestamp = pd.Timestamp.now()
    buy_price = 0.18  # 触发0.18的买单
    print(f"\n1. 价格下跌到 ${buy_price:.2f}")
    
    executed_orders = strategy.process_tick(timestamp, buy_price)
    print(f"   执行的订单数: {len(executed_orders)}")
    if executed_orders:
        for order in executed_orders:
            print(f"   执行买单: ${order.price:.2f}, 数量{order.quantity:.1f}")
    else:
        print("   没有订单被执行")
        # 检查订单状态
        for order_id, order in strategy.active_orders.items():
            should_execute = strategy._should_execute_order(order, buy_price)
            print(f"   订单 {order.order_type.value} ${order.price:.2f}: 应该执行={should_execute}")
    
    print(f"   状态: 保证金${strategy.available_margin:.2f}, 持仓{strategy.position_size:.1f}")
    print(f"   组合价值: ${strategy.get_portfolio_value(buy_price):.2f}")
    
    # 第2步：价格上涨，触发卖单
    sell_price = 0.22  # 触发0.22的卖单
    print(f"\n2. 价格上涨到 ${sell_price:.2f}")
    
    executed_orders = strategy.process_tick(timestamp, sell_price)
    print(f"   执行的订单数: {len(executed_orders)}")
    if executed_orders:
        for order in executed_orders:
            print(f"   执行卖单: ${order.price:.2f}, 数量{order.quantity:.1f}")
    else:
        print("   没有订单被执行")
    
    print(f"   状态: 保证金${strategy.available_margin:.2f}, 持仓{strategy.position_size:.1f}")
    print(f"   组合价值: ${strategy.get_portfolio_value(sell_price):.2f}")
    
    # 计算理论收益
    print(f"\n=== 理论收益分析 ===")
    buy_executed = any(o.price == 0.19 for o in strategy.filled_orders if o.order_type == OrderType.BUY)
    sell_executed = any(o.price == 0.21 for o in strategy.filled_orders if o.order_type == OrderType.SELL)
    
    if buy_executed and sell_executed:
        # 找到具体的买卖订单
        buy_order = next(o for o in strategy.filled_orders if o.order_type == OrderType.BUY and o.price == 0.19)
        sell_order = next(o for o in strategy.filled_orders if o.order_type == OrderType.SELL and o.price == 0.21)
        
        price_diff = sell_order.price - buy_order.price
        theoretical_profit = price_diff * min(buy_order.quantity, sell_order.quantity)
        fees = (buy_order.quantity * buy_order.price + sell_order.quantity * sell_order.price) * config.fee_rate
        net_profit = theoretical_profit - fees
        
        print(f"买入价格: ${buy_order.price:.2f}")
        print(f"卖出价格: ${sell_order.price:.2f}")
        print(f"价差: ${price_diff:.2f}")
        print(f"理论毛利: ${theoretical_profit:.4f}")
        print(f"手续费: ${fees:.4f}")
        print(f"理论净利: ${net_profit:.4f}")
    
    # 最终状态
    final_value = strategy.get_portfolio_value(sell_price)
    actual_pnl = final_value - config.initial_capital
    print(f"\n=== 最终结果 ===")
    print(f"初始资金: ${config.initial_capital:.2f}")
    print(f"最终价值: ${final_value:.2f}")
    print(f"实际盈亏: ${actual_pnl:.2f}")
    print(f"总手续费: ${strategy.total_fee_paid:.4f}")
    
    if buy_executed and sell_executed:
        print(f"收益率: {actual_pnl / config.initial_capital:.2%}")
        if abs(actual_pnl - net_profit) > 0.01:
            print(f"⚠️  实际盈亏与理论盈亏不符！差异: ${abs(actual_pnl - net_profit):.4f}")
        else:
            print("✅ 盈亏计算正确")

if __name__ == "__main__":
    analyze_futures_pnl_logic()