#!/usr/bin/env python3
"""
分析合约交易盈亏问题
"""

import pandas as pd
from grid_strategy import GridConfig, GridTradingStrategy
from backtest_engine import GridBacktester

def analyze_pnl_issue():
    print("=== 分析合约交易盈亏问题 ===")
    
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.1,
        upper_price=0.3,
        grid_count=100,
        fee_rate=0.0004,
        min_order_amount=10.0,
        is_futures=True,
        leverage=20,
        position_side="LONG"
    )
    
    # 加载数据
    price_data = pd.read_csv('data/DOGEUSDT_5m_3months.csv')
    price_data['datetime'] = pd.to_datetime(price_data['datetime'])
    
    # 运行回测
    backtester = GridBacktester(config)
    result = backtester.run_backtest(price_data)
    
    print(f"最终收益率: {result.total_return:.2%}")
    print(f"买入持有收益率: {result.buy_hold_return:.2%}")
    
    # 分析组合价值变化
    portfolio_values = result.portfolio_values
    print(f"\n组合价值变化分析:")
    print(f"初始价值: ${portfolio_values.iloc[0]:.2f}")
    print(f"最终价值: ${portfolio_values.iloc[-1]:.2f}")
    print(f"最高价值: ${portfolio_values.max():.2f}")
    print(f"最低价值: ${portfolio_values.min():.2f}")
    
    # 查找收益为正的时间点
    returns = (portfolio_values - config.initial_capital) / config.initial_capital
    positive_periods = returns[returns > 0]
    
    print(f"\n正收益时间段:")
    if len(positive_periods) > 0:
        print(f"正收益天数: {len(positive_periods)}")
        print(f"最大正收益: {positive_periods.max():.2%}")
        print(f"首次正收益时间: {positive_periods.index[0]}")
        print(f"最后正收益时间: {positive_periods.index[-1]}")
    else:
        print("没有正收益时间段")
    
    # 分析交易记录
    if not result.trade_log.empty:
        trade_log = result.trade_log.copy()
        trade_log['datetime'] = pd.to_datetime(trade_log['timestamp'])
        
        print(f"\n交易分析:")
        print(f"总交易次数: {len(trade_log)}")
        
        # 按类型分析
        buy_trades = trade_log[trade_log['type'] == 'buy']
        sell_trades = trade_log[trade_log['type'] == 'sell']
        
        print(f"买单次数: {len(buy_trades)}")
        print(f"卖单次数: {len(sell_trades)}")
        print(f"买单平均价格: ${buy_trades['price'].mean():.4f}")
        print(f"卖单平均价格: ${sell_trades['price'].mean():.4f}")
        
        if len(sell_trades) > 0 and len(buy_trades) > 0:
            avg_spread = sell_trades['price'].mean() - buy_trades['price'].mean()
            print(f"平均价差: ${avg_spread:.4f}")
            print(f"价差收益率: {avg_spread / buy_trades['price'].mean():.2%}")
    
    # 分析价格走势
    print(f"\n价格走势分析:")
    initial_price = price_data.iloc[0]['close']
    final_price = price_data.iloc[-1]['close']
    min_price = price_data['close'].min()
    max_price = price_data['close'].max()
    
    print(f"初始价格: ${initial_price:.4f}")
    print(f"最终价格: ${final_price:.4f}")
    print(f"价格变化: {(final_price - initial_price) / initial_price:.2%}")
    print(f"价格区间: ${min_price:.4f} - ${max_price:.4f}")
    print(f"波动幅度: {(max_price - min_price) / min_price:.2%}")
    
    # 分析网格覆盖率
    in_grid = price_data[(price_data['close'] >= config.lower_price) & 
                        (price_data['close'] <= config.upper_price)]
    coverage = len(in_grid) / len(price_data)
    print(f"网格覆盖率: {coverage:.1%}")
    
    return result

if __name__ == "__main__":
    analyze_pnl_issue()