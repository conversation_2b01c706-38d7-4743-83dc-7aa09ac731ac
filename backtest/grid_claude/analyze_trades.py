#!/usr/bin/env python3
"""
分析网格交易的下单金额
"""

import pandas as pd
from grid_strategy import GridConfig
from backtest_engine import GridBacktester

def analyze_trade_amounts():
    # 加载配置
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.1,
        upper_price=0.4,
        grid_count=100,
        fee_rate=0.0004,
        min_order_amount=10.0
    )
    
    print("=== 网格交易资金分配分析 ===")
    print(f"初始资金: ${config.initial_capital}")
    print(f"网格价格范围: ${config.lower_price} - ${config.upper_price}")
    print(f"网格数量: {config.grid_count}")
    print(f"最小下单金额: ${config.min_order_amount}")
    
    # 计算理论下单金额
    price_step = (config.upper_price - config.lower_price) / (config.grid_count - 1)
    grid_levels = [config.lower_price + i * price_step for i in range(config.grid_count)]
    
    # 假设当前价格在中间位置
    current_price = (config.lower_price + config.upper_price) / 2
    buy_levels = [price for price in grid_levels if price < current_price]
    sell_levels = [price for price in grid_levels if price > current_price]
    
    print(f"\n=== 理论资金分配 ===")
    print(f"当前价格（假设）: ${current_price:.4f}")
    print(f"买入网格数量: {len(buy_levels)}")
    print(f"卖出网格数量: {len(sell_levels)}")
    
    if buy_levels:
        # 80%资金用于买单
        usdt_for_buy = config.initial_capital * 0.8
        usdt_per_buy_level = usdt_for_buy / len(buy_levels)
        print(f"\n买单资金分配:")
        print(f"  总买单资金: ${usdt_for_buy:.2f}")
        print(f"  每个买单网格资金: ${usdt_per_buy_level:.2f}")
        
        # 计算几个不同价位的买单数量和金额
        sample_prices = buy_levels[:5] if len(buy_levels) >= 5 else buy_levels
        print(f"  示例买单:")
        for i, price in enumerate(sample_prices):
            quantity = usdt_per_buy_level / price
            amount = quantity * price
            print(f"    网格{i+1}: 价格${price:.4f}, 数量{quantity:.2f}, 金额${amount:.2f}")
    
    if sell_levels:
        # 20%资金先买入现货用于卖单
        initial_base_value = config.initial_capital * 0.2
        initial_base_quantity = initial_base_value / current_price
        base_per_sell_level = initial_base_quantity / len(sell_levels)
        
        print(f"\n卖单资金分配:")
        print(f"  用于卖单的初始现货价值: ${initial_base_value:.2f}")
        print(f"  初始现货数量: {initial_base_quantity:.2f}")
        print(f"  每个卖单网格现货数量: {base_per_sell_level:.2f}")
        
        # 计算几个不同价位的卖单金额
        sample_prices = sell_levels[:5] if len(sell_levels) >= 5 else sell_levels
        print(f"  示例卖单:")
        for i, price in enumerate(sample_prices):
            amount = base_per_sell_level * price
            print(f"    网格{i+1}: 价格${price:.4f}, 数量{base_per_sell_level:.2f}, 金额${amount:.2f}")
    
    # 运行实际回测获取真实交易记录
    print(f"\n=== 实际交易记录分析 ===")
    backtester = GridBacktester(config)
    price_data = pd.read_csv('data/DOGEUSDT_5m_3months.csv')
    result = backtester.run_backtest(price_data)
    
    trade_log = result.trade_log
    if not trade_log.empty:
        print(f"总交易次数: {len(trade_log)}")
        print(f"\n前10笔交易详情:")
        print(trade_log[['timestamp', 'type', 'price', 'quantity', 'amount', 'grid_level']].head(10).to_string(index=False))
        
        print(f"\n=== 交易金额统计 ===")
        print(f"平均交易金额: ${trade_log['amount'].mean():.2f}")
        print(f"最小交易金额: ${trade_log['amount'].min():.2f}")
        print(f"最大交易金额: ${trade_log['amount'].max():.2f}")
        print(f"交易金额标准差: ${trade_log['amount'].std():.2f}")
        
        buy_trades = trade_log[trade_log['type'] == 'buy']
        sell_trades = trade_log[trade_log['type'] == 'sell']
        
        if not buy_trades.empty:
            print(f"\n买入交易统计:")
            print(f"  买入次数: {len(buy_trades)}")
            print(f"  平均买入金额: ${buy_trades['amount'].mean():.2f}")
            print(f"  买入金额范围: ${buy_trades['amount'].min():.2f} - ${buy_trades['amount'].max():.2f}")
        
        if not sell_trades.empty:
            print(f"\n卖出交易统计:")
            print(f"  卖出次数: {len(sell_trades)}")
            print(f"  平均卖出金额: ${sell_trades['amount'].mean():.2f}")
            print(f"  卖出金额范围: ${sell_trades['amount'].min():.2f} - ${sell_trades['amount'].max():.2f}")
    else:
        print("没有交易记录")

if __name__ == "__main__":
    analyze_trade_amounts()