import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from dataclasses import dataclass
from grid_strategy import GridTradingStrategy, GridConfig


@dataclass
class BacktestResult:
    initial_capital: float
    final_capital: float
    total_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    total_fees: float
    buy_hold_return: float
    excess_return: float
    portfolio_values: pd.Series
    trade_log: pd.DataFrame


class PerformanceAnalyzer:
    def __init__(self):
        pass
    
    def calculate_returns(self, portfolio_values: pd.Series) -> pd.Series:
        """计算收益率序列"""
        return portfolio_values.pct_change().fillna(0)
    
    def calculate_max_drawdown(self, portfolio_values: pd.Series) -> float:
        """计算最大回撤"""
        peak = portfolio_values.expanding().max()
        drawdown = (portfolio_values - peak) / peak
        return abs(drawdown.min())
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.0) -> float:
        """计算夏普比率"""
        if returns.std() == 0:
            return 0.0
        excess_returns = returns - risk_free_rate / 252
        return np.sqrt(252) * excess_returns.mean() / returns.std()
    
    def calculate_win_rate(self, trade_returns: List[float]) -> float:
        """计算胜率"""
        if not trade_returns:
            return 0.0
        winning_trades = sum(1 for ret in trade_returns if ret > 0)
        return winning_trades / len(trade_returns)


class GridBacktester:
    def __init__(self, strategy_config: GridConfig):
        self.config = strategy_config
        self.analyzer = PerformanceAnalyzer()
    
    def run_backtest(self, price_data: pd.DataFrame, start_date: str = None, end_date: str = None) -> BacktestResult:
        """运行回测"""
        df = price_data.copy()
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.set_index('datetime').sort_index()
        
        if start_date:
            df = df[df.index >= start_date]
        if end_date:
            df = df[df.index <= end_date]
        
        if df.empty:
            raise ValueError("回测数据为空")
        
        print(f"回测期间: {df.index[0]} 到 {df.index[-1]}")
        print(f"数据点数: {len(df)}")
        
        strategy = GridTradingStrategy(self.config)
        
        portfolio_values = []
        trade_records = []
        
        initial_price = df.iloc[0]['close']
        buy_hold_shares = self.config.initial_capital / initial_price
        
        strategy._place_initial_orders(initial_price)
        
        for timestamp, row in df.iterrows():
            current_price = row['close']
            
            executed_orders = strategy.process_tick(timestamp, current_price)
            
            for order in executed_orders:
                trade_records.append({
                    'timestamp': timestamp,
                    'order_id': order.order_id,
                    'type': order.order_type.value,
                    'price': current_price,
                    'quantity': order.quantity,
                    'amount': order.quantity * current_price,
                    'grid_level': order.grid_level
                })
            
            portfolio_value = strategy.get_portfolio_value(current_price)
            
            if strategy.config.is_futures:
                balance_info = {
                    'available_balance': strategy.available_balance,
                    'position_size': strategy.position_size,
                    'trade_pairs': len(strategy.trade_pairs)
                }
            else:
                balance_info = {
                    'usdt_balance': strategy.available_usdt,
                    'base_balance': strategy.available_base
                }
            
            portfolio_values.append({
                'timestamp': timestamp,
                'price': current_price,
                'portfolio_value': portfolio_value,
                **balance_info
            })
        
        portfolio_df = pd.DataFrame(portfolio_values).set_index('timestamp')
        trade_df = pd.DataFrame(trade_records)
        
        final_price = df.iloc[-1]['close']
        buy_hold_final_value = buy_hold_shares * final_price
        buy_hold_return = (buy_hold_final_value - self.config.initial_capital) / self.config.initial_capital
        
        returns = self.analyzer.calculate_returns(portfolio_df['portfolio_value'])
        max_drawdown = self.analyzer.calculate_max_drawdown(portfolio_df['portfolio_value'])
        sharpe_ratio = self.analyzer.calculate_sharpe_ratio(returns)
        
        trade_returns = []
        if len(trade_df) >= 2:
            buy_trades = trade_df[trade_df['type'] == 'buy']
            sell_trades = trade_df[trade_df['type'] == 'sell']
            
            for _, sell_trade in sell_trades.iterrows():
                matching_buys = buy_trades[buy_trades['timestamp'] < sell_trade['timestamp']]
                if not matching_buys.empty:
                    avg_buy_price = matching_buys['price'].mean()
                    trade_return = (sell_trade['price'] - avg_buy_price) / avg_buy_price
                    trade_returns.append(trade_return)
        
        win_rate = self.analyzer.calculate_win_rate(trade_returns)
        
        stats = strategy.get_statistics()
        final_value = portfolio_df['portfolio_value'].iloc[-1]
        total_return = (final_value - self.config.initial_capital) / self.config.initial_capital
        
        return BacktestResult(
            initial_capital=self.config.initial_capital,
            final_capital=final_value,
            total_return=total_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=stats['total_trades'],
            total_fees=stats['total_fee_paid'],
            buy_hold_return=buy_hold_return,
            excess_return=total_return - buy_hold_return,
            portfolio_values=portfolio_df['portfolio_value'],
            trade_log=trade_df
        )
    
    def parameter_sensitivity_analysis(self, price_data: pd.DataFrame, 
                                     grid_counts: List[int] = None,
                                     price_ranges: List[Tuple[float, float]] = None) -> pd.DataFrame:
        """参数敏感性分析"""
        if grid_counts is None:
            grid_counts = [5, 10, 15, 20]
        
        if price_ranges is None:
            price_min = price_data['close'].min() * 0.8
            price_max = price_data['close'].max() * 1.2
            price_ranges = [
                (price_min, price_max),
                (price_min * 1.1, price_max * 0.9),
                (price_min * 1.2, price_max * 0.8)
            ]
        
        results = []
        
        for grid_count in grid_counts:
            for lower_price, upper_price in price_ranges:
                config = GridConfig(
                    initial_capital=self.config.initial_capital,
                    lower_price=lower_price,
                    upper_price=upper_price,
                    grid_count=grid_count,
                    fee_rate=self.config.fee_rate
                )
                
                try:
                    temp_backtester = GridBacktester(config)
                    backtest_result = temp_backtester.run_backtest(price_data)
                    
                    results.append({
                        'grid_count': grid_count,
                        'lower_price': lower_price,
                        'upper_price': upper_price,
                        'price_range': upper_price - lower_price,
                        'total_return': backtest_result.total_return,
                        'max_drawdown': backtest_result.max_drawdown,
                        'sharpe_ratio': backtest_result.sharpe_ratio,
                        'total_trades': backtest_result.total_trades,
                        'win_rate': backtest_result.win_rate,
                        'excess_return': backtest_result.excess_return
                    })
                    
                except Exception as e:
                    print(f"参数 grid_count={grid_count}, range=({lower_price:.4f}, {upper_price:.4f}) 回测失败: {e}")
        
        return pd.DataFrame(results)


def print_backtest_results(result: BacktestResult):
    """打印回测结果"""
    print("=" * 60)
    print("回测结果汇总")
    print("=" * 60)
    print(f"初始资金: ${result.initial_capital:,.2f}")
    print(f"最终资金: ${result.final_capital:,.2f}")
    print(f"总收益率: {result.total_return:.2%}")
    print(f"买入持有收益率: {result.buy_hold_return:.2%}")
    print(f"超额收益: {result.excess_return:.2%}")
    print(f"最大回撤: {result.max_drawdown:.2%}")
    print(f"夏普比率: {result.sharpe_ratio:.3f}")
    print(f"胜率: {result.win_rate:.2%}")
    print(f"总交易次数: {result.total_trades}")
    print(f"总手续费: ${result.total_fees:.2f}")
    print("=" * 60)


if __name__ == "__main__":
    config = GridConfig(
        initial_capital=1000,
        lower_price=0.05,
        upper_price=0.15,
        grid_count=10,
        fee_rate=0.0004
    )
    
    backtester = GridBacktester(config)
    print("回测引擎初始化完成")