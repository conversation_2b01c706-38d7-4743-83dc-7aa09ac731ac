import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import os
from typing import Optional, List


class BinanceDataDownloader:
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.data_dir = "data"
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def get_klines(self, symbol: str, interval: str, start_time: int, end_time: int, limit: int = 1500) -> List:
        """获取K线数据"""
        url = f"{self.base_url}/fapi/v1/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': start_time,
            'endTime': end_time,
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching data: {e}")
            return []
    
    def download_historical_data(self, symbol: str = "DOGEUSDT", interval: str = "5m", 
                                months: int = 3) -> pd.DataFrame:
        """下载历史数据"""
        print(f"开始下载 {symbol} {interval} 历史数据，时间跨度: {months}个月")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=months * 30)
        
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(end_time.timestamp() * 1000)
        
        all_data = []
        current_start = start_timestamp
        
        while current_start < end_timestamp:
            current_end = min(current_start + (1500 * 5 * 60 * 1000), end_timestamp)
            
            data = self.get_klines(symbol, interval, current_start, current_end)
            if not data:
                break
                
            all_data.extend(data)
            current_start = current_end
            
            time.sleep(0.1)
            print(f"已下载到: {datetime.fromtimestamp(current_start/1000)}")
        
        if not all_data:
            raise Exception("无法获取数据")
        
        df = pd.DataFrame(all_data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col])
        
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        filename = f"{self.data_dir}/{symbol}_{interval}_{months}months.csv"
        df.to_csv(filename, index=False)
        print(f"数据已保存到: {filename}")
        print(f"数据范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
        print(f"总计 {len(df)} 条记录")
        
        return df
    
    def load_data(self, filename: str) -> Optional[pd.DataFrame]:
        """加载本地数据"""
        filepath = f"{self.data_dir}/{filename}"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath)
            df['datetime'] = pd.to_datetime(df['datetime'])
            return df
        return None


if __name__ == "__main__":
    downloader = BinanceDataDownloader()
    df = downloader.download_historical_data("DOGEUSDT", "5m", 3)
    print(f"下载完成，数据形状: {df.shape}")
    print(df.head())