#!/usr/bin/env python3
"""
调试合约交易执行逻辑
"""

import pandas as pd
from grid_strategy import GridConfig, GridTradingStrategy, OrderType

def debug_futures_execution():
    print("=== 调试合约交易执行逻辑 ===")
    
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.16,
        upper_price=0.20,
        grid_count=5,
        fee_rate=0.0004,
        min_order_amount=10.0,
        is_futures=True,
        leverage=2,  # 低杠杆测试
        position_side="LONG"
    )
    
    strategy = GridTradingStrategy(config)
    
    print(f"初始状态:")
    print(f"  可用保证金: ${strategy.available_margin:.2f}")
    print(f"  网格价位: {strategy.grid_levels}")
    
    # 设置初始价格并放置订单
    initial_price = 0.18
    strategy._place_initial_orders(initial_price)
    
    print(f"\n初始订单放置后:")
    print(f"  活跃订单数: {len(strategy.active_orders)}")
    print(f"  可用保证金: ${strategy.available_margin:.2f}")
    print(f"  已用保证金: ${strategy.used_margin:.2f}")
    print(f"  持仓大小: {strategy.position_size:.2f}")
    
    # 显示订单详情
    for order_id, order in strategy.active_orders.items():
        print(f"    {order.order_type.value}: 价格${order.price:.4f}, 数量{order.quantity:.2f}")
    
    # 模拟价格下跌，触发买单
    print(f"\n=== 测试买单执行 ===")
    trigger_price = 0.17
    timestamp = pd.Timestamp.now()
    
    print(f"价格下跌到: ${trigger_price:.4f}")
    executed_orders = strategy.process_tick(timestamp, trigger_price)
    
    for order in executed_orders:
        print(f"执行 {order.order_type.value}单: 价格${order.price:.4f}, 数量{order.quantity:.2f}")
    
    print(f"\n买单执行后状态:")
    print(f"  可用保证金: ${strategy.available_margin:.2f}")
    print(f"  已用保证金: ${strategy.used_margin:.2f}")
    print(f"  持仓大小: {strategy.position_size:.2f}")
    print(f"  平均开仓价: ${strategy.average_entry_price:.4f}")
    print(f"  组合价值: ${strategy.get_portfolio_value(trigger_price):.2f}")
    
    # 模拟价格上涨，应该产生盈利
    print(f"\n=== 测试价格上涨产生盈利 ===")
    higher_price = 0.19
    print(f"价格上涨到: ${higher_price:.4f}")
    
    # 计算未实现盈亏
    if strategy.position_size > 0:
        unrealized_pnl = (higher_price - strategy.average_entry_price) * strategy.position_size
        print(f"未实现盈亏: ${unrealized_pnl:.2f}")
    elif strategy.position_size < 0:
        unrealized_pnl = (strategy.average_entry_price - higher_price) * abs(strategy.position_size)
        print(f"未实现盈亏: ${unrealized_pnl:.2f}")
    
    portfolio_value = strategy.get_portfolio_value(higher_price)
    print(f"组合价值: ${portfolio_value:.2f}")
    print(f"盈亏: ${portfolio_value - config.initial_capital:.2f}")
    
    # 触发卖单平仓
    executed_orders = strategy.process_tick(timestamp, higher_price)
    
    for order in executed_orders:
        print(f"执行 {order.order_type.value}单: 价格${order.price:.4f}, 数量{order.quantity:.2f}")
    
    print(f"\n卖单执行后状态:")
    print(f"  可用保证金: ${strategy.available_margin:.2f}")
    print(f"  已用保证金: ${strategy.used_margin:.2f}")
    print(f"  持仓大小: {strategy.position_size:.2f}")
    print(f"  组合价值: ${strategy.get_portfolio_value(higher_price):.2f}")
    print(f"  总盈亏: ${strategy.get_portfolio_value(higher_price) - config.initial_capital:.2f}")
    
    # 分析交易统计
    stats = strategy.get_statistics()
    print(f"\n交易统计:")
    print(f"  总交易次数: {stats['total_trades']}")
    print(f"  总手续费: ${stats['total_fee_paid']:.2f}")

if __name__ == "__main__":
    debug_futures_execution()