#!/usr/bin/env python3
"""
调试5月中下旬没有交易的问题
"""

import pandas as pd
from grid_strategy import GridConfig, GridTradingStrategy
from backtest_engine import GridBacktester
from datetime import datetime

def debug_may_trading():
    # 配置参数
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.1,
        upper_price=0.3,
        grid_count=100,
        fee_rate=0.0004,
        min_order_amount=10.0,
        is_futures=True,
        leverage=20,
        position_side="LONG"
    )
    
    # 加载数据
    price_data = pd.read_csv('data/DOGEUSDT_5m_3months.csv')
    price_data['datetime'] = pd.to_datetime(price_data['datetime'])
    
    # 筛选5月15日-31日的数据
    may_data = price_data[
        (price_data['datetime'] >= '2025-05-15') & 
        (price_data['datetime'] <= '2025-05-31')
    ].copy()
    
    print("=== 5月中下旬价格分析 ===")
    print(f"5月15-31日数据点数: {len(may_data)}")
    print(f"价格范围: ${may_data['close'].min():.4f} - ${may_data['close'].max():.4f}")
    print(f"网格范围: ${config.lower_price:.4f} - ${config.upper_price:.4f}")
    
    # 分析价格分布
    in_grid_count = len(may_data[(may_data['close'] >= config.lower_price) & 
                                 (may_data['close'] <= config.upper_price)])
    above_grid_count = len(may_data[may_data['close'] > config.upper_price])
    below_grid_count = len(may_data[may_data['close'] < config.lower_price])
    
    print(f"\n价格分布:")
    print(f"  在网格范围内: {in_grid_count} ({in_grid_count/len(may_data)*100:.1f}%)")
    print(f"  超出网格上限: {above_grid_count} ({above_grid_count/len(may_data)*100:.1f}%)")
    print(f"  低于网格下限: {below_grid_count} ({below_grid_count/len(may_data)*100:.1f}%)")
    
    # 运行完整回测并分析交易时间分布
    print(f"\n=== 完整回测交易分析 ===")
    backtester = GridBacktester(config)
    result = backtester.run_backtest(price_data)
    
    if not result.trade_log.empty:
        trade_log = result.trade_log.copy()
        trade_log['datetime'] = pd.to_datetime(trade_log['timestamp'])
        trade_log['month'] = trade_log['datetime'].dt.month
        trade_log['date'] = trade_log['datetime'].dt.date
        
        print(f"总交易次数: {len(trade_log)}")
        
        # 按月统计交易
        monthly_trades = trade_log.groupby('month').size()
        print(f"\n按月交易统计:")
        month_names = {3: '3月', 4: '4月', 5: '5月', 6: '6月'}
        for month, count in monthly_trades.items():
            print(f"  {month_names.get(month, month)}: {count}笔")
        
        # 5月份详细分析
        may_trades = trade_log[trade_log['month'] == 5]
        if not may_trades.empty:
            print(f"\n5月份交易详情:")
            print(f"  5月总交易: {len(may_trades)}笔")
            print(f"  5月交易日期分布:")
            daily_may_trades = may_trades.groupby('date').size()
            for date, count in daily_may_trades.items():
                print(f"    {date}: {count}笔")
            
            # 5月15日后的交易
            may_mid_late = may_trades[may_trades['datetime'] >= '2025-05-15']
            print(f"\n  5月15日后交易: {len(may_mid_late)}笔")
            if not may_mid_late.empty:
                print("  详细记录:")
                print(may_mid_late[['datetime', 'type', 'price', 'amount']].to_string(index=False))
        else:
            print(f"\n5月份没有任何交易!")
    
    # 手动测试5月15日的策略状态
    print(f"\n=== 手动测试5月15日策略状态 ===")
    
    # 创建策略实例
    strategy = GridTradingStrategy(config)
    
    # 用5月15日之前的数据运行到5月14日
    pre_may_data = price_data[price_data['datetime'] < '2025-05-15'].copy()
    print(f"5月15日前数据点数: {len(pre_may_data)}")
    
    if not pre_may_data.empty:
        # 初始化策略
        initial_price = pre_may_data.iloc[0]['close']
        strategy._place_initial_orders(initial_price)
        
        print(f"初始价格: ${initial_price:.4f}")
        print(f"初始活跃订单数: {len(strategy.active_orders)}")
        
        # 运行到5月14日
        for _, row in pre_may_data.iterrows():
            current_price = row['close']
            timestamp = pd.to_datetime(row['datetime'])
            executed_orders = strategy.process_tick(timestamp, current_price)
        
        print(f"\n5月14日结束时策略状态:")
        print(f"  最后价格: ${current_price:.4f}")
        print(f"  活跃订单数: {len(strategy.active_orders)}")
        if strategy.config.is_futures:
            print(f"  可用保证金: ${strategy.available_margin:.2f}")
            print(f"  已用保证金: ${strategy.used_margin:.2f}")
            print(f"  持仓大小: {strategy.position_size:.2f}")
        else:
            print(f"  可用USDT: ${strategy.available_usdt:.2f}")
            print(f"  持有现货: {strategy.available_base:.2f}")
        
        # 分析活跃订单
        if strategy.active_orders:
            buy_orders = [o for o in strategy.active_orders.values() if o.order_type.value == 'buy']
            sell_orders = [o for o in strategy.active_orders.values() if o.order_type.value == 'sell']
            
            print(f"  买单数量: {len(buy_orders)}")
            print(f"  卖单数量: {len(sell_orders)}")
            
            if buy_orders:
                buy_prices = [o.price for o in buy_orders]
                print(f"  买单价格范围: ${min(buy_prices):.4f} - ${max(buy_prices):.4f}")
            
            if sell_orders:
                sell_prices = [o.price for o in sell_orders]
                print(f"  卖单价格范围: ${min(sell_prices):.4f} - ${max(sell_prices):.4f}")
        
        # 测试5月15日第一个价格点
        may_15_first = may_data.iloc[0]
        may_15_price = may_15_first['close']
        print(f"\n5月15日第一个价格: ${may_15_price:.4f}")
        
        # 检查是否有订单会被触发
        triggered_orders = []
        for order_id, order in strategy.active_orders.items():
            if strategy._should_execute_order(order, may_15_price):
                triggered_orders.append(order)
        
        print(f"5月15日第一个价格点会触发的订单数: {len(triggered_orders)}")
        if triggered_orders:
            for order in triggered_orders:
                print(f"  {order.order_type.value}单: 价格${order.price:.4f}, 数量{order.quantity:.2f}")

if __name__ == "__main__":
    debug_may_trading()