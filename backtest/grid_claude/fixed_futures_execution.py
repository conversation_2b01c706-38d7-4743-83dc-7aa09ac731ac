def _execute_order_fixed(self, order: GridOrder, timestamp: pd.Timestamp, execution_price: float) -> GridOrder:
    """修复的合约交易执行逻辑"""
    order.status = OrderStatus.FILLED
    order.filled_time = timestamp
    
    fee = order.quantity * execution_price * self.config.fee_rate
    self.total_fee_paid += fee
    
    if self.config.is_futures:
        # 合约交易逻辑 - 修复版本
        if order.order_type == OrderType.BUY:
            # 买入：开多仓或平空仓
            if self.position_size < 0:  # 持有空仓，平仓
                close_quantity = min(order.quantity, abs(self.position_size))
                remaining_quantity = order.quantity - close_quantity
                
                # 平空仓：应该盈利
                if close_quantity > 0:
                    close_pnl = (self.average_entry_price - execution_price) * close_quantity
                    close_fee = fee * (close_quantity / order.quantity)
                    # 平仓盈亏进入可用保证金
                    self.available_margin += close_pnl - close_fee
                    self.position_size += close_quantity  # 减少空仓
                    self.used_margin -= close_quantity * self.average_entry_price / self.config.leverage
                
                # 开新多仓
                if remaining_quantity > 0:
                    if self.position_size == 0:
                        self.average_entry_price = execution_price
                        self.position_size = remaining_quantity
                    else:
                        # 加仓：更新平均价格
                        total_value = self.position_size * self.average_entry_price + remaining_quantity * execution_price
                        self.position_size += remaining_quantity
                        self.average_entry_price = total_value / self.position_size
                    
                    # 开仓只需要保证金
                    required_margin = remaining_quantity * execution_price / self.config.leverage
                    self.available_margin -= required_margin
                    self.used_margin += required_margin
                    # 开仓手续费单独扣除
                    self.available_margin -= fee * (remaining_quantity / order.quantity)
            else:
                # 开多仓或加多仓
                if self.position_size == 0:
                    self.average_entry_price = execution_price
                    self.position_size = order.quantity
                else:
                    # 加仓
                    total_value = self.position_size * self.average_entry_price + order.quantity * execution_price
                    self.position_size += order.quantity
                    self.average_entry_price = total_value / self.position_size
                
                # 需要保证金
                required_margin = order.quantity * execution_price / self.config.leverage
                self.available_margin -= required_margin + fee  # 开仓手续费
                self.used_margin += required_margin
        
        else:  # SELL
            # 卖出：开空仓或平多仓
            if self.position_size > 0:  # 持有多仓，平仓
                close_quantity = min(order.quantity, self.position_size)
                remaining_quantity = order.quantity - close_quantity
                
                # 平多仓：应该盈利
                if close_quantity > 0:
                    close_pnl = (execution_price - self.average_entry_price) * close_quantity
                    close_fee = fee * (close_quantity / order.quantity)
                    # 平仓盈亏进入可用保证金
                    self.available_margin += close_pnl - close_fee
                    self.position_size -= close_quantity  # 减少多仓
                    self.used_margin -= close_quantity * self.average_entry_price / self.config.leverage
                
                # 开新空仓
                if remaining_quantity > 0:
                    if self.position_size == 0:
                        self.average_entry_price = execution_price
                        self.position_size = -remaining_quantity
                    else:
                        # 转为空仓
                        total_value = abs(self.position_size) * self.average_entry_price + remaining_quantity * execution_price
                        self.position_size -= remaining_quantity
                        self.average_entry_price = total_value / abs(self.position_size)
                    
                    # 开仓需要保证金
                    required_margin = remaining_quantity * execution_price / self.config.leverage
                    self.available_margin -= required_margin
                    self.used_margin += required_margin
                    # 开仓手续费单独扣除
                    self.available_margin -= fee * (remaining_quantity / order.quantity)
            else:
                # 开空仓或加空仓
                if self.position_size == 0:
                    self.average_entry_price = execution_price
                    self.position_size = -order.quantity
                else:
                    # 加空仓
                    total_value = abs(self.position_size) * self.average_entry_price + order.quantity * execution_price
                    self.position_size -= order.quantity
                    self.average_entry_price = total_value / abs(self.position_size)
                
                # 需要保证金
                required_margin = order.quantity * execution_price / self.config.leverage
                self.available_margin -= required_margin + fee  # 开仓手续费
                self.used_margin += required_margin
    else:
        # 现货交易逻辑（保持不变）
        if order.order_type == OrderType.BUY:
            self.available_usdt -= (order.quantity * execution_price + fee)
            self.available_base += order.quantity
            self.position_size += order.quantity
        else:
            self.available_usdt += (order.quantity * execution_price - fee)
            self.available_base -= order.quantity
            self.position_size -= order.quantity
    
    self.filled_orders.append(order)
    return order