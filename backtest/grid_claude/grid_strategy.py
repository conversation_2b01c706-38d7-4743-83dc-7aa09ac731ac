import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class OrderType(Enum):
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    PENDING = "pending" 
    FILLED = "filled"
    CANCELLED = "cancelled"


@dataclass
class GridOrder:
    order_id: str
    price: float
    quantity: float
    order_type: OrderType
    status: OrderStatus
    created_time: pd.Timestamp
    filled_time: Optional[pd.Timestamp] = None
    grid_level: int = 0


@dataclass
class GridConfig:
    initial_capital: float = 1000.0
    lower_price: float = 0.05
    upper_price: float = 0.15
    grid_count: int = 10
    price_gap_percent: float = 0.02
    fee_rate: float = 0.0004
    min_order_amount: float = 10.0
    is_futures: bool = True  # 是否为合约交易
    leverage: int = 20  # 杠杆倍数
    position_side: str = "LONG"  # 仓位方向: LONG/SHORT/BOTH


class GridTradingStrategy:
    def __init__(self, config: GridConfig):
        self.config = config
        self.grid_levels = []
        self.active_orders: Dict[str, GridOrder] = {}
        self.filled_orders: List[GridOrder] = []
        
        # 合约交易相关属性
        if config.is_futures:
            # 现实的合约交易逻辑
            self.available_balance = config.initial_capital  # 已实现余额
            self.trade_pairs = []  # 配对交易记录
            self.open_positions = {}  # 未配对的仓位 {order_id: order}
            self.position_size = 0.0  # 净持仓大小
            self.average_entry_price = 0.0  # 平均开仓价格
            self.available_margin = config.initial_capital  # 兼容性
            self.used_margin = 0.0  # 兼容性
        else:
            # 现货交易
            self.position_size = 0.0
            self.available_usdt = config.initial_capital
            self.available_base = 0.0
        
        self.total_fee_paid = 0.0
        self.order_counter = 0
        
        self._setup_grid_levels()
    
    def _setup_grid_levels(self):
        """设置网格价位"""
        if self.config.grid_count <= 1:
            raise ValueError("Grid count must be greater than 1")
        
        price_step = (self.config.upper_price - self.config.lower_price) / (self.config.grid_count - 1)
        
        for i in range(self.config.grid_count):
            price = self.config.lower_price + i * price_step
            self.grid_levels.append(price)
        
        self.grid_levels.sort()
        print(f"设置网格价位: {len(self.grid_levels)} 个, 从 {self.grid_levels[0]:.6f} 到 {self.grid_levels[-1]:.6f}")
    
    def _place_initial_orders(self, current_price: float = None):
        """放置初始订单"""
        if current_price is None:
            current_price = (self.config.lower_price + self.config.upper_price) / 2
        
        buy_levels = [price for price in self.grid_levels if price < current_price]
        sell_levels = [price for price in self.grid_levels if price > current_price]
        
        if self.config.is_futures:
            # 简化的合约交易：直接放置订单，不预扣保证金
            # 限制网格数量
            max_grids_per_side = min(10, max(len(buy_levels), len(sell_levels)))
            
            # 计算每个订单的统一大小
            order_value = self.config.min_order_amount * 2  # 统一的订单价值
            
            # 放置买单
            for price in buy_levels[:max_grids_per_side]:
                quantity = order_value / price
                grid_level = self.grid_levels.index(price)
                order = self._create_order(price, quantity, OrderType.BUY, grid_level)
                self.active_orders[order.order_id] = order
            
            # 放置卖单
            for price in sell_levels[:max_grids_per_side]:
                quantity = order_value / price
                grid_level = self.grid_levels.index(price)
                order = self._create_order(price, quantity, OrderType.SELL, grid_level)
                self.active_orders[order.order_id] = order
        else:
            # 现货交易：原有逻辑
            if buy_levels:
                usdt_per_buy_level = self.available_usdt * 0.8 / len(buy_levels)
                for i, price in enumerate(buy_levels):
                    if usdt_per_buy_level >= self.config.min_order_amount:
                        quantity = usdt_per_buy_level / price
                        grid_level = self.grid_levels.index(price)
                        order = self._create_order(price, quantity, OrderType.BUY, grid_level)
                        self.active_orders[order.order_id] = order
            
            initial_base_quantity = self.available_usdt * 0.2 / current_price
            if sell_levels and initial_base_quantity > 0:
                self.available_base = initial_base_quantity
                self.available_usdt -= initial_base_quantity * current_price
                
                base_per_sell_level = self.available_base / len(sell_levels)
                for i, price in enumerate(sell_levels):
                    if base_per_sell_level * price >= self.config.min_order_amount:
                        grid_level = self.grid_levels.index(price)
                        order = self._create_order(price, base_per_sell_level, OrderType.SELL, grid_level)
                        self.active_orders[order.order_id] = order
    
    def _create_order(self, price: float, quantity: float, order_type: OrderType, grid_level: int) -> GridOrder:
        """创建订单"""
        self.order_counter += 1
        order_id = f"{order_type.value}_{self.order_counter}"
        
        return GridOrder(
            order_id=order_id,
            price=price,
            quantity=quantity,
            order_type=order_type,
            status=OrderStatus.PENDING,
            created_time=pd.Timestamp.now(),
            grid_level=grid_level
        )
    
    def process_tick(self, timestamp: pd.Timestamp, price: float) -> List[GridOrder]:
        """处理价格tick，检查订单执行"""
        executed_orders = []
        orders_to_remove = []
        
        for order_id, order in list(self.active_orders.items()):
            if self._should_execute_order(order, price):
                executed_order = self._execute_order(order, timestamp, price)
                executed_orders.append(executed_order)
                orders_to_remove.append(order_id)
        
        for order_id in orders_to_remove:
            del self.active_orders[order_id]
        
        for executed_order in executed_orders:
            self._place_opposite_order(executed_order)
        
        # 动态调整：如果没有活跃订单或活跃订单都太远，重新平衡
        self._rebalance_if_needed(price)
        
        return executed_orders
    
    def _should_execute_order(self, order: GridOrder, current_price: float) -> bool:
        """判断订单是否应该执行"""
        if order.order_type == OrderType.BUY:
            return current_price <= order.price
        else:
            return current_price >= order.price
    
    def _execute_order(self, order: GridOrder, timestamp: pd.Timestamp, execution_price: float) -> GridOrder:
        """执行订单"""
        order.status = OrderStatus.FILLED
        order.filled_time = timestamp
        
        fee = order.quantity * execution_price * self.config.fee_rate
        self.total_fee_paid += fee
        
        if self.config.is_futures:
            # 简化的合约交易逻辑：配对交易
            if order.order_type == OrderType.BUY:
                # 买单执行：寻找可配对的卖单
                self._try_pair_with_sell_order(order, execution_price, fee)
            else:
                # 卖单执行：寻找可配对的买单
                self._try_pair_with_buy_order(order, execution_price, fee)
        else:
            # 现货交易逻辑（原有逻辑）
            if order.order_type == OrderType.BUY:
                self.available_usdt -= (order.quantity * execution_price + fee)
                self.available_base += order.quantity
                self.position_size += order.quantity
            else:
                self.available_usdt += (order.quantity * execution_price - fee)
                self.available_base -= order.quantity
                self.position_size -= order.quantity
        
        self.filled_orders.append(order)
        return order
    
    def _try_pair_with_sell_order(self, buy_order: GridOrder, buy_price: float, buy_fee: float):
        """尝试与卖单配对"""
        # 寻找价格更高的已执行卖单进行配对
        for sell_order in self.filled_orders:
            if (sell_order.order_type == OrderType.SELL and 
                sell_order.price > buy_price and
                not self._is_already_paired(sell_order)):
                
                # 找到配对，计算盈亏
                pair_quantity = min(buy_order.quantity, sell_order.quantity)
                price_diff = sell_order.price - buy_price
                gross_profit = price_diff * pair_quantity * self.config.leverage
                
                # 计算该配对的总手续费
                sell_fee = sell_order.quantity * sell_order.price * self.config.fee_rate
                total_fees = buy_fee + sell_fee
                
                net_profit = gross_profit - total_fees
                self.available_balance += net_profit
                
                # 记录配对
                self.trade_pairs.append((buy_order, sell_order))
                break
    
    def _try_pair_with_buy_order(self, sell_order: GridOrder, sell_price: float, sell_fee: float):
        """尝试与买单配对"""
        # 寻找价格更低的已执行买单进行配对
        for buy_order in self.filled_orders:
            if (buy_order.order_type == OrderType.BUY and 
                buy_order.price < sell_price and
                not self._is_already_paired(buy_order)):
                
                # 找到配对，计算盈亏
                pair_quantity = min(sell_order.quantity, buy_order.quantity)
                price_diff = sell_price - buy_order.price
                gross_profit = price_diff * pair_quantity * self.config.leverage
                
                # 计算该配对的总手续费
                buy_fee = buy_order.quantity * buy_order.price * self.config.fee_rate
                total_fees = sell_fee + buy_fee
                
                net_profit = gross_profit - total_fees
                self.available_balance += net_profit
                
                # 记录配对
                self.trade_pairs.append((buy_order, sell_order))
                break
    
    def _is_already_paired(self, order: GridOrder) -> bool:
        """检查订单是否已经配对"""
        for buy_order, sell_order in self.trade_pairs:
            if order.order_id == buy_order.order_id or order.order_id == sell_order.order_id:
                return True
        return False
    
    def _rebalance_if_needed(self, current_price: float):
        """根据当前价格重新平衡网格订单"""
        if not self.active_orders:
            # 如果没有活跃订单，重新放置订单
            self._place_orders_around_price(current_price)
            return
        
        # 检查当前订单是否太远离当前价格
        buy_orders = [o for o in self.active_orders.values() if o.order_type == OrderType.BUY]
        sell_orders = [o for o in self.active_orders.values() if o.order_type == OrderType.SELL]
        
        need_rebalance = False
        
        # 如果没有买单且有可用资金，需要放置买单
        available_capital = self.available_margin if self.config.is_futures else self.available_usdt
        
        # 合约交易：如果没有卖单且可以开仓，需要放置卖单
        # 现货交易：如果没有卖单且持有现货，需要放置卖单
        if self.config.is_futures:
            if not sell_orders and available_capital > self.config.min_order_amount:
                need_rebalance = True
        else:
            if not sell_orders and self.available_base > 0:
                need_rebalance = True
        if not buy_orders and available_capital > self.config.min_order_amount:
            need_rebalance = True
        
        # 如果最近的订单距离当前价格太远（超过20个网格），重新平衡
        if buy_orders:
            highest_buy_price = max(o.price for o in buy_orders)
            if current_price - highest_buy_price > 20 * (self.grid_levels[1] - self.grid_levels[0]):
                need_rebalance = True
        
        if sell_orders:
            lowest_sell_price = min(o.price for o in sell_orders)
            if lowest_sell_price - current_price > 20 * (self.grid_levels[1] - self.grid_levels[0]):
                need_rebalance = True
        
        if need_rebalance:
            # 清除距离太远的订单并重新放置
            self._clear_distant_orders(current_price)
            self._place_orders_around_price(current_price)
    
    def _clear_distant_orders(self, current_price: float):
        """清除距离当前价格太远的订单"""
        grid_spacing = self.grid_levels[1] - self.grid_levels[0]
        max_distance = 10 * grid_spacing  # 最大允许距离为10个网格
        
        orders_to_remove = []
        for order_id, order in self.active_orders.items():
            distance = abs(order.price - current_price)
            if distance > max_distance:
                orders_to_remove.append(order_id)
        
        for order_id in orders_to_remove:
            del self.active_orders[order_id]
    
    def _place_orders_around_price(self, current_price: float):
        """在当前价格周围放置网格订单"""
        # 找到最接近当前价格的网格级别
        closest_level = 0
        min_diff = abs(self.grid_levels[0] - current_price)
        for i, price in enumerate(self.grid_levels):
            diff = abs(price - current_price)
            if diff < min_diff:
                min_diff = diff
                closest_level = i
        
        # 在当前价格下方放置买单
        levels_to_place = 3  # 减少网格数量
        
        for i in range(max(0, closest_level - levels_to_place), closest_level):
            price = self.grid_levels[i]
            
            # 检查是否已有这个价格的买单
            existing_buy = any(o.price == price and o.order_type == OrderType.BUY 
                             for o in self.active_orders.values())
            if not existing_buy:
                if self.config.is_futures:
                    # 简化的合约交易：固定订单大小
                    order_value = self.config.min_order_amount * 2
                    quantity = order_value / price
                    order = self._create_order(price, quantity, OrderType.BUY, i)
                    self.active_orders[order.order_id] = order
                else:
                    # 现货交易
                    available_capital = self.available_usdt
                    if available_capital > self.config.min_order_amount:
                        quantity = min(available_capital * 0.2, self.config.min_order_amount) / price
                        if quantity * price >= self.config.min_order_amount:
                            order = self._create_order(price, quantity, OrderType.BUY, i)
                            self.active_orders[order.order_id] = order
        
        # 在当前价格上方放置卖单
        for i in range(closest_level + 1, min(len(self.grid_levels), closest_level + levels_to_place + 1)):
            price = self.grid_levels[i]
            
            existing_sell = any(o.price == price and o.order_type == OrderType.SELL 
                              for o in self.active_orders.values())
            if not existing_sell:
                if self.config.is_futures:
                    # 简化的合约交易：固定订单大小
                    order_value = self.config.min_order_amount * 2
                    quantity = order_value / price
                    order = self._create_order(price, quantity, OrderType.SELL, i)
                    self.active_orders[order.order_id] = order
                else:
                    # 现货交易：需要有现货才能卖
                    if self.available_base > 0:
                        quantity = min(self.available_base * 0.2, self.config.min_order_amount / price)
                        if quantity * price >= self.config.min_order_amount:
                            order = self._create_order(price, quantity, OrderType.SELL, i)
                            self.active_orders[order.order_id] = order
    
    def _place_opposite_order(self, executed_order: GridOrder):
        """在对应网格位置放置反向订单"""
        grid_level = executed_order.grid_level
        
        if executed_order.order_type == OrderType.BUY:
            if grid_level < len(self.grid_levels) - 1:
                sell_price = self.grid_levels[grid_level + 1]
                sell_order = self._create_order(sell_price, executed_order.quantity, OrderType.SELL, grid_level + 1)
                self.active_orders[sell_order.order_id] = sell_order
        else:
            if grid_level > 0:
                buy_price = self.grid_levels[grid_level - 1]
                if self.config.is_futures:
                    # 简化的合约交易：直接放置订单
                    buy_order = self._create_order(buy_price, executed_order.quantity, OrderType.BUY, grid_level - 1)
                    self.active_orders[buy_order.order_id] = buy_order
                else:
                    # 现货交易：检查资金
                    usdt_needed = buy_price * executed_order.quantity
                    if self.available_usdt >= usdt_needed + usdt_needed * self.config.fee_rate:
                        buy_order = self._create_order(buy_price, executed_order.quantity, OrderType.BUY, grid_level - 1)
                        self.active_orders[buy_order.order_id] = buy_order
    
    def get_portfolio_value(self, current_price: float) -> float:
        """计算当前组合价值"""
        if self.config.is_futures:
            # 包含未配对订单的未实现盈亏
            unrealized_pnl = 0.0
            
            # 计算未配对买单的未实现盈亏
            unpaired_buy_orders = []
            unpaired_sell_orders = []
            
            for order in self.filled_orders:
                if not self._is_already_paired(order):
                    if order.order_type == OrderType.BUY:
                        unpaired_buy_orders.append(order)
                    else:
                        unpaired_sell_orders.append(order)
            
            # 计算未配对买单的盈亏 (当前价格 - 买入价格)
            for buy_order in unpaired_buy_orders:
                pnl = (current_price - buy_order.price) * buy_order.quantity * self.config.leverage
                unrealized_pnl += pnl
            
            # 计算未配对卖单的盈亏 (卖出价格 - 当前价格)  
            for sell_order in unpaired_sell_orders:
                pnl = (sell_order.price - current_price) * sell_order.quantity * self.config.leverage
                unrealized_pnl += pnl
            
            return self.available_balance + unrealized_pnl
        else:
            # 现货交易
            base_value = self.available_base * current_price
            return self.available_usdt + base_value
    
    def get_statistics(self) -> Dict:
        """获取交易统计"""
        buy_orders = [o for o in self.filled_orders if o.order_type == OrderType.BUY]
        sell_orders = [o for o in self.filled_orders if o.order_type == OrderType.SELL]
        
        stats = {
            'total_trades': len(self.filled_orders),
            'buy_trades': len(buy_orders),
            'sell_trades': len(sell_orders),
            'total_fee_paid': self.total_fee_paid,
            'active_orders': len(self.active_orders),
            'position_size': self.position_size,
        }
        
        if self.config.is_futures:
            stats.update({
                'available_balance': self.available_balance,
                'trade_pairs': len(self.trade_pairs),
                'leverage': self.config.leverage,
                'is_futures': True
            })
        else:
            stats.update({
                'available_usdt': self.available_usdt,
                'available_base': self.available_base,
                'is_futures': False
            })
        
        return stats
    
    def reset(self):
        """重置策略状态"""
        self.active_orders.clear()
        self.filled_orders.clear()
        self.position_size = 0.0
        self.total_fee_paid = 0.0
        self.order_counter = 0
        
        if self.config.is_futures:
            self.available_margin = self.config.initial_capital
            self.used_margin = 0.0
            self.unrealized_pnl = 0.0
            self.average_entry_price = 0.0
        else:
            self.available_usdt = self.config.initial_capital
            self.available_base = 0.0


if __name__ == "__main__":
    config = GridConfig(
        initial_capital=1000,
        lower_price=0.05,
        upper_price=0.15,
        grid_count=10,
        fee_rate=0.0004
    )
    
    strategy = GridTradingStrategy(config)
    print("网格策略初始化完成")
    print(f"活跃订单数量: {len(strategy.active_orders)}")
    print(f"初始资金: {config.initial_capital} USDT")