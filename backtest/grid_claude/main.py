#!/usr/bin/env python3
"""
DOGE/USDT网格交易策略回测系统
主程序入口
"""

import argparse
import json
import os
from datetime import datetime
from typing import Dict, Any

from data_downloader import BinanceDataDownloader
from grid_strategy import GridConfig
from backtest_engine import GridBacktester, print_backtest_results
from visualization import GridTradingVisualizer, create_summary_report


def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        default_config = {
            "trading": {
                "symbol": "DOGEUSDT",
                "interval": "5m",
                "months": 3,
                "initial_capital": 1000.0,
                "lower_price": 0.1,
                "upper_price": 0.4,
                "grid_count": 100,
                "fee_rate": 0.0004,
                "min_order_amount": 10.0,
                "is_futures": True,
                "leverage": 20,
                "position_side": "LONG"
            },
            "analysis": {
                "enable_parameter_analysis": True,
                "grid_counts": [5, 8, 10, 12, 15],
                "price_range_multipliers": [0.8, 0.9, 1.0, 1.1, 1.2]
            },
            "visualization": {
                "save_plots": True,
                "plot_format": "png",
                "output_dir": "results"
            }
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        print(f"已创建默认配置文件: {config_path}")
        return default_config


def ensure_output_dir(output_dir: str):
    """确保输出目录存在"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出目录: {output_dir}")


def main():
    parser = argparse.ArgumentParser(description='DOGE/USDT网格交易策略回测系统')
    parser.add_argument('--config', type=str, default='config.json', 
                       help='配置文件路径')
    parser.add_argument('--download-only', action='store_true', 
                       help='仅下载数据，不运行回测')
    parser.add_argument('--no-download', action='store_true', 
                       help='跳过数据下载，使用现有数据')
    parser.add_argument('--no-plots', action='store_true', 
                       help='跳过图表生成')
    parser.add_argument('--analysis', action='store_true',
                       help='运行参数敏感性分析')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("DOGE/USDT 网格交易策略回测系统")
    print("=" * 60)
    
    config = load_config(args.config)
    trading_config = config['trading']
    
    if config['visualization']['save_plots']:
        ensure_output_dir(config['visualization']['output_dir'])
    
    downloader = BinanceDataDownloader()
    
    data_filename = f"{trading_config['symbol']}_{trading_config['interval']}_{trading_config['months']}months.csv"
    
    if not args.no_download:
        print("\n1. 下载历史数据...")
        try:
            price_data = downloader.download_historical_data(
                symbol=trading_config['symbol'],
                interval=trading_config['interval'],
                months=trading_config['months']
            )
        except Exception as e:
            print(f"数据下载失败: {e}")
            print("尝试加载本地数据...")
            price_data = downloader.load_data(data_filename)
            if price_data is None:
                print("无法获取数据，程序退出")
                return
    else:
        print("\n1. 加载本地数据...")
        price_data = downloader.load_data(data_filename)
        if price_data is None:
            print(f"未找到本地数据文件: {data_filename}")
            print("请先运行数据下载")
            return
    
    if args.download_only:
        print("数据下载完成，程序退出")
        return
    
    print("\n2. 初始化网格策略...")
    grid_config = GridConfig(
        initial_capital=trading_config['initial_capital'],
        lower_price=trading_config['lower_price'],
        upper_price=trading_config['upper_price'],
        grid_count=trading_config['grid_count'],
        fee_rate=trading_config['fee_rate'],
        min_order_amount=trading_config['min_order_amount'],
        is_futures=trading_config.get('is_futures', True),
        leverage=trading_config.get('leverage', 20),
        position_side=trading_config.get('position_side', 'LONG')
    )
    
    print(f"策略参数:")
    print(f"  - 交易类型: {'合约交易' if grid_config.is_futures else '现货交易'}")
    print(f"  - 初始资金: ${grid_config.initial_capital:,.2f}")
    if grid_config.is_futures:
        print(f"  - 杠杆倍数: {grid_config.leverage}x")
        print(f"  - 有效资金: ${grid_config.initial_capital * grid_config.leverage:,.2f}")
    print(f"  - 价格区间: ${grid_config.lower_price:.4f} - ${grid_config.upper_price:.4f}")
    print(f"  - 网格数量: {grid_config.grid_count}")
    print(f"  - 手续费率: {grid_config.fee_rate:.2%}")
    
    print("\n3. 运行回测...")
    backtester = GridBacktester(grid_config)
    result = backtester.run_backtest(price_data)
    
    print("\n4. 回测结果:")
    print_backtest_results(result)
    
    if args.analysis and config['analysis']['enable_parameter_analysis']:
        print("\n5. 参数敏感性分析...")
        try:
            base_price = price_data['close'].mean()
            price_ranges = []
            for multiplier in config['analysis']['price_range_multipliers']:
                lower = base_price * 0.8 * multiplier
                upper = base_price * 1.2 * multiplier
                price_ranges.append((lower, upper))
            
            sensitivity_result = backtester.parameter_sensitivity_analysis(
                price_data,
                grid_counts=config['analysis']['grid_counts'],
                price_ranges=price_ranges
            )
            
            print("参数敏感性分析完成")
            print(f"最佳参数组合:")
            best_params = sensitivity_result.loc[sensitivity_result['total_return'].idxmax()]
            print(f"  - 网格数量: {best_params['grid_count']}")
            print(f"  - 价格区间: {best_params['lower_price']:.4f} - {best_params['upper_price']:.4f}")
            print(f"  - 收益率: {best_params['total_return']:.2%}")
            
        except Exception as e:
            print(f"参数敏感性分析失败: {e}")
            sensitivity_result = None
    else:
        sensitivity_result = None
    
    if not args.no_plots:
        print("\n6. 生成可视化图表...")
        visualizer = GridTradingVisualizer()
        
        output_dir = config['visualization']['output_dir'] if config['visualization']['save_plots'] else None
        plot_format = config['visualization']['plot_format']
        
        try:
            from grid_strategy import GridTradingStrategy
            temp_strategy = GridTradingStrategy(grid_config)
            grid_levels = temp_strategy.grid_levels
            
            visualizer.plot_portfolio_performance(
                result, price_data, 
                save_path=f"{output_dir}/portfolio_performance.{plot_format}" if output_dir else None
            )
            
            visualizer.plot_trading_signals(
                result, price_data, grid_levels,
                save_path=f"{output_dir}/trading_signals.{plot_format}" if output_dir else None
            )
            
            visualizer.plot_drawdown(
                result,
                save_path=f"{output_dir}/drawdown.{plot_format}" if output_dir else None
            )
            
            visualizer.plot_performance_metrics(
                result,
                save_path=f"{output_dir}/performance_metrics.{plot_format}" if output_dir else None
            )
            
            if sensitivity_result is not None:
                visualizer.plot_parameter_sensitivity(
                    sensitivity_result,
                    save_path=f"{output_dir}/parameter_sensitivity.{plot_format}" if output_dir else None
                )
            
        except Exception as e:
            print(f"图表生成失败: {e}")
    
    print("\n7. 生成报告...")
    report = create_summary_report(result)
    print(report)
    
    if config['visualization']['save_plots']:
        report_path = f"{config['visualization']['output_dir']}/backtest_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {report_path}")
    
    print("\n回测完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()