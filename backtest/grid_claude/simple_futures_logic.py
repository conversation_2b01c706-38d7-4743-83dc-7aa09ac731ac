#!/usr/bin/env python3
"""
简化的合约交易逻辑 - 只做配对交易
"""

import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class OrderType(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending" 
    FILLED = "filled"

@dataclass
class SimpleOrder:
    order_id: str
    price: float
    quantity: float
    order_type: OrderType
    status: OrderStatus
    created_time: pd.Timestamp
    filled_time: Optional[pd.Timestamp] = None

class SimpleFuturesTrading:
    """简化的合约交易：只做配对的低买高卖"""
    
    def __init__(self, initial_capital: float, leverage: int = 1, fee_rate: float = 0.0004):
        self.initial_capital = initial_capital
        self.available_balance = initial_capital  # 可用余额
        self.leverage = leverage
        self.fee_rate = fee_rate
        
        self.active_orders: Dict[str, SimpleOrder] = {}
        self.filled_orders: List[SimpleOrder] = []
        self.trade_pairs: List[Tuple[SimpleOrder, SimpleOrder]] = []  # 配对交易记录
        
        self.order_counter = 0
        self.total_fee_paid = 0.0
        
    def place_buy_order(self, price: float, quantity: float) -> SimpleOrder:
        """放置买单"""
        self.order_counter += 1
        order = SimpleOrder(
            order_id=f"buy_{self.order_counter}",
            price=price,
            quantity=quantity,
            order_type=OrderType.BUY,
            status=OrderStatus.PENDING,
            created_time=pd.Timestamp.now()
        )
        self.active_orders[order.order_id] = order
        return order
    
    def place_sell_order(self, price: float, quantity: float) -> SimpleOrder:
        """放置卖单"""
        self.order_counter += 1
        order = SimpleOrder(
            order_id=f"sell_{self.order_counter}",
            price=price,
            quantity=quantity,
            order_type=OrderType.SELL,
            status=OrderStatus.PENDING,
            created_time=pd.Timestamp.now()
        )
        self.active_orders[order.order_id] = order
        return order
    
    def process_tick(self, current_price: float) -> List[SimpleOrder]:
        """处理价格tick"""
        executed_orders = []
        orders_to_remove = []
        
        for order_id, order in list(self.active_orders.items()):
            if self._should_execute_order(order, current_price):
                executed_order = self._execute_order(order, current_price)
                executed_orders.append(executed_order)
                orders_to_remove.append(order_id)
        
        # 移除已执行的订单
        for order_id in orders_to_remove:
            del self.active_orders[order_id]
        
        return executed_orders
    
    def _should_execute_order(self, order: SimpleOrder, current_price: float) -> bool:
        """判断订单是否应该执行"""
        if order.order_type == OrderType.BUY:
            return current_price <= order.price
        else:
            return current_price >= order.price
    
    def _execute_order(self, order: SimpleOrder, execution_price: float) -> SimpleOrder:
        """执行订单 - 简化版本，只计算配对盈亏"""
        order.status = OrderStatus.FILLED
        order.filled_time = pd.Timestamp.now()
        
        # 计算手续费
        fee = order.quantity * execution_price * self.fee_rate
        self.total_fee_paid += fee
        
        if order.order_type == OrderType.BUY:
            # 买单执行：寻找可配对的卖单
            self._try_pair_with_sell_order(order, execution_price, fee)
        else:
            # 卖单执行：寻找可配对的买单
            self._try_pair_with_buy_order(order, execution_price, fee)
        
        self.filled_orders.append(order)
        return order
    
    def _try_pair_with_sell_order(self, buy_order: SimpleOrder, buy_price: float, buy_fee: float):
        """尝试与卖单配对"""
        # 寻找价格更高的已执行卖单进行配对
        for sell_order in self.filled_orders:
            if (sell_order.order_type == OrderType.SELL and 
                sell_order.price > buy_price and
                not self._is_already_paired(sell_order)):
                
                # 找到配对，计算盈亏
                pair_quantity = min(buy_order.quantity, sell_order.quantity)
                price_diff = sell_order.price - buy_price
                gross_profit = price_diff * pair_quantity * self.leverage
                
                # 计算该配对的总手续费
                sell_fee = sell_order.quantity * sell_order.price * self.fee_rate
                total_fees = buy_fee + sell_fee
                
                net_profit = gross_profit - total_fees
                self.available_balance += net_profit
                
                # 记录配对
                self.trade_pairs.append((buy_order, sell_order))
                
                print(f"配对交易: 买入@${buy_price:.4f} vs 卖出@${sell_order.price:.4f}")
                print(f"  数量: {pair_quantity:.1f}, 杠杆: {self.leverage}x")
                print(f"  毛利: ${gross_profit:.4f}, 手续费: ${total_fees:.4f}")
                print(f"  净利: ${net_profit:.4f}")
                break
    
    def _try_pair_with_buy_order(self, sell_order: SimpleOrder, sell_price: float, sell_fee: float):
        """尝试与买单配对"""
        # 寻找价格更低的已执行买单进行配对
        for buy_order in self.filled_orders:
            if (buy_order.order_type == OrderType.BUY and 
                buy_order.price < sell_price and
                not self._is_already_paired(buy_order)):
                
                # 找到配对，计算盈亏
                pair_quantity = min(sell_order.quantity, buy_order.quantity)
                price_diff = sell_price - buy_order.price
                gross_profit = price_diff * pair_quantity * self.leverage
                
                # 计算该配对的总手续费
                buy_fee = buy_order.quantity * buy_order.price * self.fee_rate
                total_fees = sell_fee + buy_fee
                
                net_profit = gross_profit - total_fees
                self.available_balance += net_profit
                
                # 记录配对
                self.trade_pairs.append((buy_order, sell_order))
                
                print(f"配对交易: 买入@${buy_order.price:.4f} vs 卖出@${sell_price:.4f}")
                print(f"  数量: {pair_quantity:.1f}, 杠杆: {self.leverage}x")
                print(f"  毛利: ${gross_profit:.4f}, 手续费: ${total_fees:.4f}")
                print(f"  净利: ${net_profit:.4f}")
                break
    
    def _is_already_paired(self, order: SimpleOrder) -> bool:
        """检查订单是否已经配对"""
        for buy_order, sell_order in self.trade_pairs:
            if order.order_id == buy_order.order_id or order.order_id == sell_order.order_id:
                return True
        return False
    
    def get_portfolio_value(self) -> float:
        """获取组合价值"""
        return self.available_balance
    
    def get_statistics(self) -> Dict:
        """获取交易统计"""
        return {
            'initial_capital': self.initial_capital,
            'current_value': self.available_balance,
            'total_return': (self.available_balance - self.initial_capital) / self.initial_capital,
            'total_trades': len(self.filled_orders),
            'total_pairs': len(self.trade_pairs),
            'total_fees': self.total_fee_paid,
            'active_orders': len(self.active_orders)
        }

def test_simple_futures():
    """测试简化的合约交易逻辑"""
    print("=== 测试简化的合约交易逻辑 ===")
    
    trader = SimpleFuturesTrading(initial_capital=1000.0, leverage=2, fee_rate=0.0001)
    
    print(f"初始资金: ${trader.available_balance:.2f}")
    
    # 放置网格订单
    trader.place_buy_order(price=0.18, quantity=50)
    trader.place_sell_order(price=0.22, quantity=50)
    
    print(f"放置订单后，活跃订单数: {len(trader.active_orders)}")
    
    # 模拟价格变动
    print(f"\n=== 价格变动测试 ===")
    
    # 价格跌到0.18，触发买单
    print(f"\n1. 价格跌到 $0.18")
    executed = trader.process_tick(0.18)
    print(f"   执行订单数: {len(executed)}")
    print(f"   当前价值: ${trader.get_portfolio_value():.2f}")
    
    # 价格涨到0.22，触发卖单并配对
    print(f"\n2. 价格涨到 $0.22")
    executed = trader.process_tick(0.22)
    print(f"   执行订单数: {len(executed)}")
    print(f"   当前价值: ${trader.get_portfolio_value():.2f}")
    
    # 最终统计
    stats = trader.get_statistics()
    print(f"\n=== 最终统计 ===")
    print(f"初始资金: ${stats['initial_capital']:.2f}")
    print(f"最终价值: ${stats['current_value']:.2f}")
    print(f"总收益率: {stats['total_return']:.2%}")
    print(f"总交易数: {stats['total_trades']}")
    print(f"配对数: {stats['total_pairs']}")
    print(f"总手续费: ${stats['total_fees']:.4f}")

if __name__ == "__main__":
    test_simple_futures()