#!/usr/bin/env python3
"""
简单的合约交易逻辑测试
"""

import pandas as pd
from grid_strategy import GridConfig, GridTradingStrategy, OrderType

def simple_futures_test():
    print("=== 简单合约交易逻辑测试 ===")
    
    # 创建简单配置
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.18,
        upper_price=0.22,
        grid_count=5,
        fee_rate=0.0004,
        min_order_amount=10.0,
        is_futures=True,
        leverage=2,  # 使用2倍杠杆
        position_side="LONG"
    )
    
    strategy = GridTradingStrategy(config)
    
    print(f"初始状态:")
    print(f"  - 可用保证金: ${strategy.available_margin}")
    print(f"  - 网格价位: {strategy.grid_levels}")
    
    # 手动测试买入订单
    test_price = 0.19
    strategy._place_initial_orders(test_price)
    
    print(f"\n初始订单放置后:")
    print(f"  - 活跃订单数: {len(strategy.active_orders)}")
    print(f"  - 可用保证金: ${strategy.available_margin:.2f}")
    print(f"  - 已用保证金: ${strategy.used_margin:.2f}")
    
    # 显示活跃订单
    buy_orders = [o for o in strategy.active_orders.values() if o.order_type == OrderType.BUY]
    sell_orders = [o for o in strategy.active_orders.values() if o.order_type == OrderType.SELL]
    
    print(f"\n活跃订单详情:")
    print(f"  买单数量: {len(buy_orders)}")
    for order in buy_orders:
        print(f"    价格: ${order.price:.4f}, 数量: {order.quantity:.2f}, 价值: ${order.price * order.quantity:.2f}")
    
    print(f"  卖单数量: {len(sell_orders)}")
    for order in sell_orders:
        print(f"    价格: ${order.price:.4f}, 数量: {order.quantity:.2f}, 价值: ${order.price * order.quantity:.2f}")
    
    # 模拟价格变动，触发买单
    trigger_price = 0.185  # 低于某些买单价格
    timestamp = pd.Timestamp.now()
    
    print(f"\n价格变动到: ${trigger_price:.4f}")
    executed_orders = strategy.process_tick(timestamp, trigger_price)
    
    print(f"执行的订单数量: {len(executed_orders)}")
    for order in executed_orders:
        print(f"  {order.order_type.value}单执行: 价格${order.price:.4f}, 数量{order.quantity:.2f}")
    
    print(f"\n执行后状态:")
    print(f"  - 持仓大小: {strategy.position_size:.2f}")
    print(f"  - 可用保证金: ${strategy.available_margin:.2f}")
    print(f"  - 已用保证金: ${strategy.used_margin:.2f}")
    print(f"  - 活跃订单数: {len(strategy.active_orders)}")
    
    # 计算组合价值
    portfolio_value = strategy.get_portfolio_value(trigger_price)
    print(f"  - 组合价值: ${portfolio_value:.2f}")
    
    # 模拟价格上涨，触发卖单
    higher_price = 0.195
    print(f"\n价格上涨到: ${higher_price:.4f}")
    executed_orders = strategy.process_tick(timestamp, higher_price)
    
    print(f"执行的订单数量: {len(executed_orders)}")
    for order in executed_orders:
        print(f"  {order.order_type.value}单执行: 价格${order.price:.4f}, 数量{order.quantity:.2f}")
    
    print(f"\n最终状态:")
    print(f"  - 持仓大小: {strategy.position_size:.2f}")
    print(f"  - 可用保证金: ${strategy.available_margin:.2f}")
    print(f"  - 已用保证金: ${strategy.used_margin:.2f}")
    print(f"  - 活跃订单数: {len(strategy.active_orders)}")
    
    # 计算最终组合价值
    final_portfolio_value = strategy.get_portfolio_value(higher_price)
    print(f"  - 最终组合价值: ${final_portfolio_value:.2f}")
    print(f"  - 盈亏: ${final_portfolio_value - config.initial_capital:.2f}")

if __name__ == "__main__":
    simple_futures_test()