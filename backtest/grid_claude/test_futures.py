#!/usr/bin/env python3
"""
测试合约交易功能
"""

import pandas as pd
from grid_strategy import GridConfig, GridTradingStrategy
from backtest_engine import GridBacktester

def test_futures_trading():
    print("=== 测试合约交易功能 ===")
    
    # 创建更保守的合约交易配置
    config = GridConfig(
        initial_capital=1000.0,
        lower_price=0.15,
        upper_price=0.25,
        grid_count=20,  # 减少网格数量
        fee_rate=0.0004,
        min_order_amount=10.0,
        is_futures=True,
        leverage=5,  # 降低杠杆
        position_side="LONG"
    )
    
    print(f"配置参数:")
    print(f"  - 合约交易，杠杆: {config.leverage}x")
    print(f"  - 初始保证金: ${config.initial_capital}")
    print(f"  - 有效资金: ${config.initial_capital * config.leverage}")
    print(f"  - 价格区间: ${config.lower_price} - ${config.upper_price}")
    print(f"  - 网格数量: {config.grid_count}")
    
    # 加载数据
    try:
        price_data = pd.read_csv('data/DOGEUSDT_5m_3months.csv')
        price_data['datetime'] = pd.to_datetime(price_data['datetime'])
        print(f"\n数据加载成功，数据点数: {len(price_data)}")
        
        # 创建策略实例测试初始化
        strategy = GridTradingStrategy(config)
        print(f"\n策略初始化成功")
        print(f"  - 网格价位数量: {len(strategy.grid_levels)}")
        print(f"  - 可用保证金: ${strategy.available_margin}")
        print(f"  - 已用保证金: ${strategy.used_margin}")
        
        # 运行回测
        print(f"\n开始回测...")
        backtester = GridBacktester(config)
        result = backtester.run_backtest(price_data)
        
        print(f"\n=== 回测结果 ===")
        print(f"初始资金: ${result.initial_capital:,.2f}")
        print(f"最终资金: ${result.final_capital:,.2f}")
        print(f"总收益率: {result.total_return:.2%}")
        print(f"最大回撤: {result.max_drawdown:.2%}")
        print(f"总交易次数: {result.total_trades}")
        print(f"总手续费: ${result.total_fees:.2f}")
        print(f"胜率: {result.win_rate:.2%}")
        
        # 分析交易记录
        if not result.trade_log.empty:
            print(f"\n=== 交易分析 ===")
            buy_trades = result.trade_log[result.trade_log['type'] == 'buy']
            sell_trades = result.trade_log[result.trade_log['type'] == 'sell']
            print(f"买单数量: {len(buy_trades)}")
            print(f"卖单数量: {len(sell_trades)}")
            
            if len(buy_trades) > 0:
                print(f"买单平均价格: ${buy_trades['price'].mean():.4f}")
                print(f"买单平均数量: {buy_trades['quantity'].mean():.2f}")
            
            if len(sell_trades) > 0:
                print(f"卖单平均价格: ${sell_trades['price'].mean():.4f}")
                print(f"卖单平均数量: {sell_trades['quantity'].mean():.2f}")
        
        # 最终策略状态
        final_price = price_data.iloc[-1]['close']
        print(f"\n=== 最终策略状态 ===")
        print(f"最终价格: ${final_price:.4f}")
        
        stats = strategy.get_statistics()
        print(f"持仓大小: {stats['position_size']:.2f}")
        print(f"可用保证金: ${stats['available_margin']:.2f}")
        print(f"已用保证金: ${stats['used_margin']:.2f}")
        print(f"未实现盈亏: ${stats['unrealized_pnl']:.2f}")
        if stats['average_entry_price'] > 0:
            print(f"平均开仓价格: ${stats['average_entry_price']:.4f}")
        
        return result
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_futures_trading()