import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from typing import Dict, List, Optional
from backtest_engine import BacktestResult
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import matplotlib
import platform

# 配置字体
def setup_font():
    # 使用默认英文字体避免中文字体问题
    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
    matplotlib.rcParams['axes.unicode_minus'] = False
    print("使用英文标题避免字体显示问题")
    return False

USE_CHINESE = setup_font()


def get_title(chinese_title, english_title):
    """根据字体支持情况返回合适的标题"""
    return chinese_title if USE_CHINESE else english_title


class GridTradingVisualizer:
    def __init__(self, style='seaborn-v0_8'):
        plt.style.use('default')
        sns.set_palette("husl")
        self.colors = {
            'portfolio': '#2E86AB',
            'buy_hold': '#A23B72',
            'price': '#F18F01',
            'buy_signal': '#C73E1D',
            'sell_signal': '#06A77D',
            'grid_lines': '#E5E5E5'
        }
    
    def plot_portfolio_performance(self, result: BacktestResult, price_data: pd.DataFrame, 
                                 save_path: Optional[str] = None):
        """绘制组合表现对比图"""
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
        
        price_df = price_data.copy()
        price_df['datetime'] = pd.to_datetime(price_df['datetime'])
        price_df = price_df.set_index('datetime')
        
        portfolio_values = result.portfolio_values
        
        buy_hold_initial = result.initial_capital
        buy_hold_shares = buy_hold_initial / price_df['close'].iloc[0]
        buy_hold_values = price_df['close'] * buy_hold_shares
        buy_hold_values = buy_hold_values.reindex(portfolio_values.index, method='nearest')
        
        ax1.plot(portfolio_values.index, portfolio_values.values, 
                label=get_title('网格策略', 'Grid Strategy'), color=self.colors['portfolio'], linewidth=2)
        ax1.plot(buy_hold_values.index, buy_hold_values.values, 
                label=get_title('买入持有', 'Buy & Hold'), color=self.colors['buy_hold'], linewidth=2, linestyle='--')
        ax1.axhline(y=result.initial_capital, color='gray', linestyle=':', alpha=0.7, label=get_title('初始资金', 'Initial Capital'))
        ax1.set_title(get_title('策略表现对比', 'Strategy Performance Comparison'), fontsize=14, fontweight='bold')
        ax1.set_ylabel(get_title('资产价值 (USDT)', 'Portfolio Value (USDT)'))
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        price_aligned = price_df['close'].reindex(portfolio_values.index, method='nearest')
        ax2.plot(price_aligned.index, price_aligned.values, 
                color=self.colors['price'], linewidth=1.5, label=get_title('DOGE价格', 'DOGE Price'))
        ax2.set_title(get_title('DOGE/USDT 价格走势', 'DOGE/USDT Price Chart'), fontsize=14, fontweight='bold')
        ax2.set_ylabel(get_title('价格 (USDT)', 'Price (USDT)'))
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        portfolio_returns = portfolio_values.pct_change().fillna(0)
        buy_hold_returns = buy_hold_values.pct_change().fillna(0)
        
        cumulative_portfolio = (1 + portfolio_returns).cumprod()
        cumulative_buy_hold = (1 + buy_hold_returns).cumprod()
        
        ax3.plot(cumulative_portfolio.index, (cumulative_portfolio - 1) * 100, 
                label=get_title('网格策略', 'Grid Strategy'), color=self.colors['portfolio'], linewidth=2)
        ax3.plot(cumulative_buy_hold.index, (cumulative_buy_hold - 1) * 100, 
                label=get_title('买入持有', 'Buy & Hold'), color=self.colors['buy_hold'], linewidth=2, linestyle='--')
        ax3.axhline(y=0, color='gray', linestyle=':', alpha=0.7)
        ax3.set_title(get_title('累计收益率对比', 'Cumulative Returns Comparison'), fontsize=14, fontweight='bold')
        ax3.set_ylabel(get_title('累计收益率 (%)', 'Cumulative Returns (%)'))
        ax3.set_xlabel(get_title('时间', 'Time'))
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        for ax in [ax1, ax2, ax3]:
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_trading_signals(self, result: BacktestResult, price_data: pd.DataFrame, 
                           grid_levels: List[float], save_path: Optional[str] = None):
        """绘制交易信号和网格线"""
        fig, ax = plt.subplots(figsize=(15, 8))
        
        price_df = price_data.copy()
        price_df['datetime'] = pd.to_datetime(price_df['datetime'])
        price_df = price_df.set_index('datetime')
        
        ax.plot(price_df.index, price_df['close'], color=self.colors['price'], 
               linewidth=1, alpha=0.8, label=get_title('DOGE价格', 'DOGE Price'))
        
        for level in grid_levels:
            ax.axhline(y=level, color=self.colors['grid_lines'], 
                      linestyle='-', alpha=0.6, linewidth=0.8)
        
        if not result.trade_log.empty:
            trade_df = result.trade_log.copy()
            trade_df['timestamp'] = pd.to_datetime(trade_df['timestamp'])
            
            buy_trades = trade_df[trade_df['type'] == 'buy']
            sell_trades = trade_df[trade_df['type'] == 'sell']
            
            if not buy_trades.empty:
                buy_label = get_title(f'买入信号 ({len(buy_trades)})', f'Buy Signals ({len(buy_trades)})')
                ax.scatter(buy_trades['timestamp'], buy_trades['price'], 
                          color=self.colors['buy_signal'], marker='^', s=60, 
                          alpha=0.8, label=buy_label)
            
            if not sell_trades.empty:
                sell_label = get_title(f'卖出信号 ({len(sell_trades)})', f'Sell Signals ({len(sell_trades)})')
                ax.scatter(sell_trades['timestamp'], sell_trades['price'], 
                          color=self.colors['sell_signal'], marker='v', s=60, 
                          alpha=0.8, label=sell_label)
        
        ax.set_title(get_title('网格交易信号', 'Grid Trading Signals'), fontsize=14, fontweight='bold')
        ax.set_ylabel(get_title('价格 (USDT)', 'Price (USDT)'))
        ax.set_xlabel(get_title('时间', 'Time'))
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_drawdown(self, result: BacktestResult, save_path: Optional[str] = None):
        """绘制回撤图"""
        portfolio_values = result.portfolio_values
        peak = portfolio_values.expanding().max()
        drawdown = (portfolio_values - peak) / peak * 100
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        ax.fill_between(drawdown.index, drawdown.values, 0, 
                       color='red', alpha=0.3, label=get_title('回撤', 'Drawdown'))
        ax.plot(drawdown.index, drawdown.values, color='red', linewidth=1)
        
        max_dd_idx = drawdown.idxmin()
        ax.scatter(max_dd_idx, drawdown.min(), color='darkred', s=100, 
                  zorder=5, label=f'{get_title("最大回撤", "Max Drawdown")}: {result.max_drawdown:.2%}')
        
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.set_title(get_title('策略回撤分析', 'Strategy Drawdown Analysis'), fontsize=14, fontweight='bold')
        ax.set_ylabel(get_title('回撤 (%)', 'Drawdown (%)'))
        ax.set_xlabel(get_title('时间', 'Time'))
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_performance_metrics(self, result: BacktestResult, save_path: Optional[str] = None):
        """绘制性能指标对比"""
        metrics = {
            get_title('总收益率', 'Total Return'): f"{result.total_return:.2%}",
            get_title('买入持有收益率', 'Buy & Hold Return'): f"{result.buy_hold_return:.2%}",
            get_title('超额收益', 'Excess Return'): f"{result.excess_return:.2%}",
            get_title('最大回撤', 'Max Drawdown'): f"{result.max_drawdown:.2%}",
            get_title('夏普比率', 'Sharpe Ratio'): f"{result.sharpe_ratio:.3f}",
            get_title('胜率', 'Win Rate'): f"{result.win_rate:.2%}",
            get_title('总交易次数', 'Total Trades'): f"{result.total_trades}",
            get_title('总手续费', 'Total Fees'): f"${result.total_fees:.2f}"
        }
        
        fig, ax = plt.subplots(figsize=(10, 6))
        
        y_pos = np.arange(len(metrics))
        values = [result.total_return, result.buy_hold_return, result.excess_return,
                 -result.max_drawdown, result.sharpe_ratio/5, result.win_rate,
                 result.total_trades/1000, result.total_fees/100]
        
        colors = ['green' if v >= 0 else 'red' for v in values[:4]] + ['blue'] * 4
        
        bars = ax.barh(y_pos, values, color=colors, alpha=0.7)
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels(list(metrics.keys()))
        ax.set_xlabel(get_title('标准化数值', 'Normalized Values'))
        ax.set_title(get_title('策略性能指标', 'Strategy Performance Metrics'), fontsize=14, fontweight='bold')
        
        for i, (bar, value) in enumerate(zip(bars, metrics.values())):
            ax.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                   value, ha='left', va='center', fontweight='bold')
        
        ax.grid(True, alpha=0.3, axis='x')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_parameter_sensitivity(self, sensitivity_df: pd.DataFrame, 
                                 save_path: Optional[str] = None):
        """绘制参数敏感性分析"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        pivot_return = sensitivity_df.pivot_table(
            values='total_return', index='grid_count', columns='price_range'
        )
        sns.heatmap(pivot_return, annot=True, fmt='.2%', cmap='RdYlGn', ax=ax1)
        ax1.set_title(get_title('总收益率 vs 网格数量 & 价格区间', 'Total Return vs Grid Count & Price Range'))
        
        pivot_drawdown = sensitivity_df.pivot_table(
            values='max_drawdown', index='grid_count', columns='price_range'
        )
        sns.heatmap(pivot_drawdown, annot=True, fmt='.2%', cmap='RdYlGn_r', ax=ax2)
        ax2.set_title(get_title('最大回撤 vs 网格数量 & 价格区间', 'Max Drawdown vs Grid Count & Price Range'))
        
        ax3.scatter(sensitivity_df['grid_count'], sensitivity_df['total_return'], 
                   alpha=0.6, s=60)
        ax3.set_xlabel(get_title('网格数量', 'Grid Count'))
        ax3.set_ylabel(get_title('总收益率', 'Total Return'))
        ax3.set_title(get_title('收益率 vs 网格数量', 'Return vs Grid Count'))
        ax3.grid(True, alpha=0.3)
        
        ax4.scatter(sensitivity_df['price_range'], sensitivity_df['sharpe_ratio'], 
                   alpha=0.6, s=60)
        ax4.set_xlabel(get_title('价格区间', 'Price Range'))
        ax4.set_ylabel(get_title('夏普比率', 'Sharpe Ratio'))
        ax4.set_title(get_title('夏普比率 vs 价格区间', 'Sharpe Ratio vs Price Range'))
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_interactive_dashboard(self, result: BacktestResult, price_data: pd.DataFrame):
        """创建交互式仪表板"""
        price_df = price_data.copy()
        price_df['datetime'] = pd.to_datetime(price_df['datetime'])
        
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=(get_title('价格走势', 'Price Trend'), get_title('组合价值', 'Portfolio Value'), 
                           get_title('累计收益率', 'Cumulative Returns'), get_title('回撤', 'Drawdown'), 
                           get_title('交易分布', 'Trade Distribution'), get_title('性能指标', 'Performance Metrics')),
            specs=[[{"secondary_y": True}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        fig.add_trace(
            go.Scatter(x=price_df['datetime'], y=price_df['close'], 
                      name=get_title('DOGE价格', 'DOGE Price'), line=dict(color='orange')),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(x=result.portfolio_values.index, y=result.portfolio_values.values,
                      name=get_title('组合价值', 'Portfolio Value'), line=dict(color='blue')),
            row=1, col=2
        )
        
        portfolio_returns = result.portfolio_values.pct_change().fillna(0)
        cumulative_returns = (1 + portfolio_returns).cumprod() - 1
        
        fig.add_trace(
            go.Scatter(x=cumulative_returns.index, y=cumulative_returns.values * 100,
                      name=get_title('累计收益率(%)', 'Cumulative Returns(%)'), line=dict(color='green')),
            row=2, col=1
        )
        
        peak = result.portfolio_values.expanding().max()
        drawdown = (result.portfolio_values - peak) / peak * 100
        
        fig.add_trace(
            go.Scatter(x=drawdown.index, y=drawdown.values,
                      name=get_title('回撤(%)', 'Drawdown(%)'), fill='tonexty', line=dict(color='red')),
            row=2, col=2
        )
        
        if not result.trade_log.empty:
            trade_counts = result.trade_log['type'].value_counts()
            fig.add_trace(
                go.Bar(x=trade_counts.index, y=trade_counts.values, name=get_title('交易次数', 'Trade Count')),
                row=3, col=1
            )
        
        metrics_names = [get_title('总收益率', 'Total Return'), get_title('买入持有收益率', 'Buy & Hold Return'), 
                        get_title('最大回撤', 'Max Drawdown'), get_title('夏普比率', 'Sharpe Ratio')]
        metrics_values = [result.total_return*100, result.buy_hold_return*100, 
                         result.max_drawdown*100, result.sharpe_ratio]
        
        fig.add_trace(
            go.Bar(x=metrics_names, y=metrics_values, name=get_title('性能指标', 'Performance Metrics')),
            row=3, col=2
        )
        
        fig.update_layout(height=900, showlegend=True, 
                         title_text=get_title("网格交易策略回测仪表板", "Grid Trading Strategy Backtest Dashboard"))
        
        return fig


def create_summary_report(result: BacktestResult) -> str:
    """生成文字报告"""
    title = get_title('网格交易策略回测报告', 'Grid Trading Strategy Backtest Report')
    basic_info = get_title('基本信息:', 'Basic Information:')
    initial_capital = get_title('初始资金', 'Initial Capital')
    final_capital = get_title('最终资金', 'Final Capital')
    total_profit = get_title('回测总收益', 'Total Backtest Profit')
    
    return_analysis = get_title('收益分析:', 'Return Analysis:')
    strategy_return = get_title('策略总收益率', 'Strategy Total Return')
    buy_hold_return = get_title('买入持有收益率', 'Buy & Hold Return')
    excess_return = get_title('超额收益', 'Excess Return')
    annual_return = get_title('年化收益率', 'Annualized Return')
    three_months_note = get_title('(假设3个月数据)', '(Assuming 3 months data)')
    
    risk_analysis = get_title('风险分析:', 'Risk Analysis:')
    max_drawdown = get_title('最大回撤', 'Max Drawdown')
    sharpe_ratio = get_title('夏普比率', 'Sharpe Ratio')
    win_rate = get_title('胜率', 'Win Rate')
    
    trade_stats = get_title('交易统计:', 'Trading Statistics:')
    total_trades = get_title('总交易次数', 'Total Trades')
    total_fees = get_title('总手续费', 'Total Fees')
    fee_ratio = get_title('手续费占比', 'Fee Ratio')
    
    strategy_evaluation = get_title('策略评价:', 'Strategy Evaluation:')
    excellent_performance = get_title('表现优秀', 'Excellent Performance')
    average_performance = get_title('表现一般', 'Average Performance')
    outperform_buy_hold = get_title('，超越买入持有策略', ', outperforming buy-and-hold strategy')
    underperform_buy_hold = get_title('，未能超越买入持有策略', ', failing to outperform buy-and-hold strategy')
    
    report = f"""
{title}
{'=' * len(title)}

{basic_info}
- {initial_capital}: ${result.initial_capital:,.2f}
- {final_capital}: ${result.final_capital:,.2f}
- {total_profit}: ${result.final_capital - result.initial_capital:,.2f}

{return_analysis}
- {strategy_return}: {result.total_return:.2%}
- {buy_hold_return}: {result.buy_hold_return:.2%}
- {excess_return}: {result.excess_return:.2%}
- {annual_return}: {result.total_return * 4:.2%} {three_months_note}

{risk_analysis}
- {max_drawdown}: {result.max_drawdown:.2%}
- {sharpe_ratio}: {result.sharpe_ratio:.3f}
- {win_rate}: {result.win_rate:.2%}

{trade_stats}
- {total_trades}: {result.total_trades}
- {total_fees}: ${result.total_fees:.2f}
- {fee_ratio}: {result.total_fees/result.initial_capital:.2%}

{strategy_evaluation}
{excellent_performance if result.excess_return > 0 else average_performance}
{outperform_buy_hold if result.excess_return > 0 else underperform_buy_hold}
"""
    return report


if __name__ == "__main__":
    print("可视化模块已加载")
    visualizer = GridTradingVisualizer()
    print("可视化器初始化完成")