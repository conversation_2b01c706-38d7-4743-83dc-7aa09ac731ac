#!/usr/bin/env python

"""
  script to download klines.
  set the absoluate path destination folder for STORE_DIRECTORY, and run

  e.g. STORE_DIRECTORY=/data/ ./download-kline.py

"""
import sys
from datetime import *
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from enums import *
from utility import download_file, get_all_symbols, get_parser, get_start_end_date_objects, convert_to_date_object, \
  get_path, get_destination_dir
import os
import hashlib

def verify_checksum(file_path, checksum_path):
    """验证文件的校验和是否匹配
    
    Args:
        file_path: 下载的数据文件路径
        checksum_path: 校验和文件路径
    
    Returns:
        bool: 校验结果是否匹配
    """
    # 读取校验和文件
    try:
        with open(checksum_path, 'r') as f:
            expected_checksum = f.read().strip().split(' ')[0]
    except FileNotFoundError:
        print(f"Checksum file not found: {checksum_path}")
        return False
        
    # 计算下载文件的 SHA256
    sha256_hash = hashlib.sha256()
    try:
        with open(file_path, 'rb') as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        actual_checksum = sha256_hash.hexdigest()
    except FileNotFoundError:
        print(f"Downloaded file not found: {file_path}")
        return False
        
    return actual_checksum == expected_checksum

def download_single_file(args):
    """单个文件下载函数"""
    path, file_name, date_range, folder, checksum = args
    download_file(path, file_name, date_range, folder)
    file_path = get_destination_dir(os.path.join(path, file_name), folder)
    
    if checksum == 1:
        checksum_path = path
        checksum_file_name = file_name + ".CHECKSUM"
        checksum_file_path = get_destination_dir(os.path.join(path, checksum_file_name), folder)
        download_file(checksum_path, checksum_file_name, date_range, folder)

        # 验证校验和
        retry_count = 0
        while retry_count < 3:  # 最多重试3次
            if verify_checksum(file_path, checksum_file_path):
                print(f"Checksum verified for {file_name}")
                break
            else:
                print(f"Checksum verification failed for {file_name}, retrying download... ({retry_count + 1}/3)")
                # delete the file
                if os.path.exists(file_path):
                    os.remove(file_path)
                download_file(path, file_name, date_range, folder)
                retry_count += 1
        
        if retry_count == 3:
            print(f"Failed to download {file_name} with correct checksum after 3 attempts")

def download_monthly_klines(trading_type, symbols, num_symbols, intervals, years, months, start_date, end_date, folder, checksum):
    current = 0
    date_range = None
    download_tasks = []

    if start_date and end_date:
        date_range = start_date + " " + end_date

    if not start_date:
        start_date = START_DATE
    else:
        start_date = convert_to_date_object(start_date)

    if not end_date:
        end_date = END_DATE
    else:
        end_date = convert_to_date_object(end_date)

    print("Found {} symbols".format(num_symbols))

    if trading_type == 'um':
        intervals.remove('1s')

    # 准备所有下载任务
    for symbol in symbols:
        print("[{}/{}] - preparing monthly {} klines ".format(current+1, num_symbols, symbol))
        for interval in intervals:
            for year in years:
                for month in months:
                    current_date = convert_to_date_object('{}-{}-01'.format(year, month))
                    if current_date >= start_date and current_date <= end_date:
                        path = get_path(trading_type, "klines", "monthly", symbol, interval)
                        file_name = "{}-{}-{}-{}.zip".format(symbol.upper(), interval, year, '{:02d}'.format(month))
                        download_tasks.append((path, file_name, date_range, folder, checksum))
        current += 1

    # 使用线程池并发下载
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(download_single_file, task) for task in download_tasks]
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"下载出错: {str(e)}")

def download_daily_klines(trading_type, symbols, num_symbols, intervals, dates, start_date, end_date, folder, checksum):
    current = 0
    date_range = None
    download_tasks = []

    if start_date and end_date:
        date_range = start_date + " " + end_date

    if not start_date:
        start_date = START_DATE
    else:
        start_date = convert_to_date_object(start_date)

    if not end_date:
        end_date = END_DATE
    else:
        end_date = convert_to_date_object(end_date)

    intervals = list(set(intervals) & set(DAILY_INTERVALS))
    print("Found {} symbols".format(num_symbols))

    if trading_type == 'um':
        intervals.remove('1s')

    # 准备所有下载任务
    for symbol in symbols:
        print("[{}/{}] - preparing daily {} klines ".format(current+1, num_symbols, symbol))
        for interval in intervals:
            for date in dates:
                current_date = convert_to_date_object(date)
                if current_date >= start_date and current_date <= end_date:
                    path = get_path(trading_type, "klines", "daily", symbol, interval)
                    file_name = "{}-{}-{}.zip".format(symbol.upper(), interval, date)
                    download_tasks.append((path, file_name, date_range, folder, checksum))
        current += 1

    # 使用线程池并发下载
    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = [executor.submit(download_single_file, task) for task in download_tasks]
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"下载出错: {str(e)}")

if __name__ == "__main__":
    parser = get_parser('klines')
    args = parser.parse_args(sys.argv[1:])

    if not args.symbols:
      print("fetching all symbols from exchange")
      symbols = get_all_symbols(args.type)
      #symbols = SYMBOLS_SPOT

      num_symbols = len(symbols)
    else:
      symbols = args.symbols
      num_symbols = len(symbols)

    if args.dates:
      dates = args.dates
    else:
      dates = pd.date_range(end = datetime.today(), periods = MAX_DAYS).to_pydatetime().tolist()
      dates = [date.strftime("%Y-%m-%d") for date in dates]
      download_monthly_klines(args.type, symbols, num_symbols, args.intervals, args.years, args.months, args.startDate, args.endDate, args.folder, args.checksum)
    # download_daily_klines(args.type, symbols, num_symbols, args.intervals, dates, args.startDate, args.endDate, args.folder, args.checksum)

