from datetime import *

YEARS = ['2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024']
YEARS = ['2025']
INTERVALS = ["1s", "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h"]
# INTERVALS = ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h"]
DAILY_INTERVALS = ["1s", "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h"]
TRADING_TYPE = ["spot", "um", "cm"]
MONTHS = list(range(1,6))
MAX_DAYS = 10
BASE_URL = 'https://data.binance.vision/'
START_DATE = date(int(YEARS[0]), MONTHS[0], 1)
END_DATE = datetime.date(datetime.now())

usdt = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'NEOUSDT', 'LTCUSDT', 'QTUMUSDT', 'ADAUSDT', 'XRPUSDT', 'EOSUSDT', 'TUSDUSDT', 'IOTAUSDT', 'XLMUSDT', 'ONTUSDT', 'TRXUSDT', 'ETCUSDT', 'ICXUSDT', 'NULSUSDT', 'VETUSDT', 'USDCUSDT', 'LINKUSDT', 'ONGUSDT', 'HOTUSDT', 'ZILUSDT', 'ZRXUSDT', 'FETUSDT', 'BATUSDT', 'ZECUSDT', 'IOSTUSDT', 'CELRUSDT', 'DASHUSDT', 'THETAUSDT', 'ENJUSDT', 'ATOMUSDT', 'TFUELUSDT', 'ONEUSDT', 'FTMUSDT', 'ALGOUSDT', 'DOGEUSDT', 'DUSKUSDT', 'ANKRUSDT', 'WINUSDT', 'COSUSDT', 'MTLUSDT', 'DENTUSDT', 'WANUSDT', 'FUNUSDT', 'CVCUSDT', 'CHZUSDT', 'BANDUSDT', 'XTZUSDT', 'RVNUSDT', 'HBARUSDT', 'NKNUSDT', 'STXUSDT', 'KAVAUSDT', 'ARPAUSDT', 'IOTXUSDT', 'RLCUSDT', 'CTXCUSDT', 'BCHUSDT', 'TROYUSDT', 'VITEUSDT', 'FTTUSDT', 'EURUSDT', 'OGNUSDT', 'WRXUSDT', 'LSKUSDT', 'BNTUSDT', 'LTOUSDT', 'MBLUSDT', 'COTIUSDT', 'STPTUSDT', 'DATAUSDT', 'SOLUSDT', 'CTSIUSDT', 'HIVEUSDT', 'CHRUSDT', 'ARDRUSDT', 'MDTUSDT', 'STMXUSDT', 'KNCUSDT', 'LRCUSDT', 'COMPUSDT', 'SCUSDT', 'ZENUSDT', 'SNXUSDT', 'VTHOUSDT', 'DGBUSDT', 'SXPUSDT', 'MKRUSDT', 'DCRUSDT', 'STORJUSDT', 'MANAUSDT', 'YFIUSDT', 'BALUSDT', 'BLZUSDT', 'KMDUSDT', 'JSTUSDT', 'CRVUSDT', 'SANDUSDT', 'NMRUSDT', 'DOTUSDT', 'LUNAUSDT', 'RSRUSDT', 'PAXGUSDT', 'TRBUSDT', 'SUSHIUSDT', 'KSMUSDT', 'EGLDUSDT', 'DIAUSDT', 'RUNEUSDT', 'FIOUSDT', 'UMAUSDT', 'BELUSDT', 'WINGUSDT', 'UNIUSDT', 'OXTUSDT', 'SUNUSDT', 'AVAXUSDT', 'FLMUSDT', 'UTKUSDT', 'XVSUSDT', 'ALPHAUSDT', 'AAVEUSDT', 'NEARUSDT', 'FILUSDT', 'INJUSDT', 'AUDIOUSDT', 'CTKUSDT', 'AKROUSDT', 'AXSUSDT', 'HARDUSDT', 'STRAXUSDT', 'ROSEUSDT', 'AVAUSDT', 'SKLUSDT', 'GRTUSDT', 'JUVUSDT', 'PSGUSDT', '1INCHUSDT', 'OGUSDT', 'ATMUSDT', 'ASRUSDT', 'CELOUSDT', 'RIFUSDT', 'TRUUSDT', 'CKBUSDT', 'TWTUSDT', 'FIROUSDT', 'LITUSDT', 'SFPUSDT', 'DODOUSDT', 'CAKEUSDT', 'ACMUSDT', 'BADGERUSDT', 'FISUSDT', 'OMUSDT', 'PONDUSDT', 'DEGOUSDT', 'ALICEUSDT', 'LINAUSDT', 'PERPUSDT', 'SUPERUSDT', 'CFXUSDT', 'TKOUSDT', 'PUNDIXUSDT', 'TLMUSDT', 'BARUSDT', 'FORTHUSDT', 'BAKEUSDT', 'BURGERUSDT', 'SLPUSDT', 'SHIBUSDT', 'ICPUSDT', 'ARUSDT', 'MASKUSDT', 'LPTUSDT', 'XVGUSDT', 'ATAUSDT', 'GTCUSDT', 'ERNUSDT', 'PHAUSDT', 'MLNUSDT', 'DEXEUSDT', 'C98USDT', 'CLVUSDT', 'QNTUSDT', 'FLOWUSDT', 'MINAUSDT', 'RAYUSDT', 'FARMUSDT', 'ALPACAUSDT', 'QUICKUSDT', 'MBOXUSDT', 'REQUSDT', 'GHSTUSDT', 'WAXPUSDT', 'GNOUSDT', 'XECUSDT', 'ELFUSDT', 'DYDXUSDT', 'IDEXUSDT', 'VIDTUSDT', 'USDPUSDT', 'GALAUSDT', 'ILVUSDT', 'YGGUSDT', 'SYSUSDT', 'DFUSDT', 'FIDAUSDT', 'AGLDUSDT', 'RADUSDT', 'BETAUSDT', 'RAREUSDT', 'LAZIOUSDT', 'CHESSUSDT', 'ADXUSDT', 'AUCTIONUSDT', 'DARUSDT', 'BNXUSDT', 'MOVRUSDT', 'CITYUSDT', 'ENSUSDT', 'QIUSDT', 'PORTOUSDT', 'POWRUSDT', 'JASMYUSDT', 'AMPUSDT', 'PYRUSDT', 'ALCXUSDT', 'SANTOSUSDT', 'BICOUSDT', 'FLUXUSDT', 'FXSUSDT', 'VOXELUSDT', 'HIGHUSDT', 'CVXUSDT', 'PEOPLEUSDT', 'SPELLUSDT', 'JOEUSDT', 'ACHUSDT', 'IMXUSDT', 'GLMRUSDT', 'LOKAUSDT', 'SCRTUSDT', 'API3USDT', 'BTTCUSDT', 'ACAUSDT', 'XNOUSDT', 'WOOUSDT', 'ALPINEUSDT', 'TUSDT', 'ASTRUSDT', 'GMTUSDT', 'KDAUSDT', 'APEUSDT', 'BSWUSDT', 'BIFIUSDT', 'STEEMUSDT', 'NEXOUSDT', 'REIUSDT', 'LDOUSDT', 'OPUSDT', 'LEVERUSDT', 'STGUSDT', 'LUNCUSDT', 'GMXUSDT', 'POLYXUSDT', 'APTUSDT', 'OSMOUSDT', 'HFTUSDT', 'PHBUSDT', 'HOOKUSDT', 'MAGICUSDT', 'HIFIUSDT', 'RPLUSDT', 'PROSUSDT', 'GNSUSDT', 'SYNUSDT', 'VIBUSDT', 'SSVUSDT', 'LQTYUSDT', 'AMBUSDT', 'USTCUSDT', 'GASUSDT', 'GLMUSDT', 'PROMUSDT', 'QKCUSDT', 'UFTUSDT', 'IDUSDT', 'ARBUSDT', 'RDNTUSDT', 'WBTCUSDT', 'EDUUSDT', 'SUIUSDT', 'AERGOUSDT', 'PEPEUSDT', 'FLOKIUSDT', 'ASTUSDT', 'SNTUSDT', 'COMBOUSDT', 'MAVUSDT', 'PENDLEUSDT', 'ARKMUSDT', 'WBETHUSDT', 'WLDUSDT', 'FDUSDUSDT', 'SEIUSDT', 'CYBERUSDT', 'ARKUSDT', 'CREAMUSDT', 'IQUSDT', 'NTRNUSDT', 'TIAUSDT', 'MEMEUSDT', 'ORDIUSDT', 'BEAMXUSDT', 'PIVXUSDT', 'VICUSDT', 'BLURUSDT', 'VANRYUSDT', 'AEURUSDT', 'JTOUSDT', '1000SATSUSDT', 'BONKUSDT', 'ACEUSDT', 'NFPUSDT', 'AIUSDT', 'XAIUSDT', 'MANTAUSDT', 'ALTUSDT', 'JUPUSDT', 'PYTHUSDT', 'RONINUSDT', 'DYMUSDT', 'PIXELUSDT', 'STRKUSDT', 'PORTALUSDT', 'PDAUSDT', 'AXLUSDT', 'WIFUSDT', 'METISUSDT', 'AEVOUSDT', 'BOMEUSDT', 'ETHFIUSDT', 'ENAUSDT', 'WUSDT', 'TNSRUSDT', 'SAGAUSDT', 'TAOUSDT', 'OMNIUSDT', 'REZUSDT', 'BBUSDT', 'NOTUSDT', 'IOUSDT', 'ZKUSDT', 'LISTAUSDT', 'ZROUSDT', 'GUSDT', 'BANANAUSDT', 'RENDERUSDT', 'TONUSDT', 'DOGSUSDT', 'EURIUSDT', 'SLFUSDT', 'POLUSDT', 'NEIROUSDT', 'TURBOUSDT', '1MBABYDOGEUSDT', 'CATIUSDT', 'HMSTRUSDT', 'EIGENUSDT', 'SCRUSDT', 'BNSOLUSDT', 'LUMIAUSDT', 'KAIAUSDT', 'COWUSDT', 'CETUSUSDT', 'PNUTUSDT', 'ACTUSDT', 'USUALUSDT', 'THEUSDT', 'ACXUSDT', 'ORCAUSDT', 'MOVEUSDT', 'MEUSDT', 'VELODROMEUSDT']


btc = ['ETHBTC', 'LTCBTC', 'BNBBTC', 'NEOBTC', 'BCCBTC', 'GASBTC', 'HSRBTC', 'MCOBTC', 'WTCBTC', 'LRCBTC', 'QTUMBTC', 'YOYOBTC', 'OMGBTC', 'ZRXBTC', 'STRATBTC', 'SNGLSBTC', 'BQXBTC', 'KNCBTC', 'FUNBTC', 'SNMBTC', 'IOTABTC', 'LINKBTC', 'XVGBTC', 'SALTBTC', 'MDABTC', 'MTLBTC', 'SUBBTC', 'EOSBTC', 'SNTBTC', 'ETCBTC', 'MTHBTC', 'ENGBTC', 'DNTBTC', 'ZECBTC', 'BNTBTC', 'ASTBTC', 'DASHBTC', 'OAXBTC', 'ICNBTC', 'BTGBTC', 'EVXBTC', 'REQBTC', 'VIBBTC', 'TRXBTC', 'POWRBTC', 'ARKBTC', 'XRPBTC', 'MODBTC', 'ENJBTC', 'STORJBTC', 'VENBTC', 'KMDBTC', 'RCNBTC', 'NULSBTC', 'RDNBTC', 'XMRBTC', 'DLTBTC', 'AMBBTC', 'BATBTC', 'BCPTBTC', 'ARNBTC', 'GVTBTC', 'CDTBTC', 'GXSBTC', 'POEBTC', 'QSPBTC', 'BTSBTC', 'XZCBTC', 'LSKBTC', 'TNTBTC', 'FUELBTC', 'MANABTC', 'BCDBTC', 'DGDBTC', 'ADXBTC', 'ADABTC', 'PPTBTC', 'CMTBTC', 'XLMBTC', 'CNDBTC', 'LENDBTC', 'WABIBTC', 'TNBBTC', 'WAVESBTC', 'GTOBTC', 'ICXBTC', 'OSTBTC', 'ELFBTC', 'AIONBTC', 'NEBLBTC', 'BRDBTC', 'EDOBTC', 'WINGSBTC', 'NAVBTC', 'LUNBTC', 'TRIGBTC', 'APPCBTC', 'VIBEBTC', 'RLCBTC', 'INSBTC', 'PIVXBTC', 'IOSTBTC', 'CHATBTC', 'STEEMBTC', 'NANOBTC', 'VIABTC', 'BLZBTC', 'AEBTC', 'RPXBTC', 'NCASHBTC', 'POABTC', 'ZILBTC', 'ONTBTC', 'STORMBTC', 'XEMBTC', 'WANBTC', 'WPRBTC', 'QLCBTC', 'SYSBTC', 'GRSBTC', 'CLOAKBTC', 'GNTBTC', 'LOOMBTC', 'BCNBTC', 'REPBTC', 'TUSDBTC', 'ZENBTC', 'SKYBTC', 'THETABTC', 'IOTXBTC', 'QKCBTC', 'AGIBTC', 'NXSBTC', 'DATABTC', 'SCBTC', 'NPXSBTC', 'KEYBTC', 'NASBTC', 'ARDRBTC', 'HOTBTC', 'VETBTC', 'DOCKBTC', 'POLYBTC', 'PHXBTC', 'HCBTC', 'GOBTC', 'PAXBTC', 'RVNBTC', 'DCRBTC', 'MITHBTC', 'BCHABCBTC', 'BCHSVBTC', 'RENBTC', 'BTTBTC', 'ONGBTC', 'FETBTC', 'CELRBTC', 'MATICBTC', 'ATOMBTC', 'PHBBTC', 'TFUELBTC', 'ONEBTC', 'FTMBTC', 'BTCBBTC', 'ALGOBTC', 'ERDBTC', 'DOGEBTC', 'DUSKBTC', 'ANKRBTC', 'WINBTC', 'COSBTC', 'COCOSBTC', 'TOMOBTC', 'PERLBTC', 'CHZBTC', 'BANDBTC', 'BEAMBTC', 'XTZBTC', 'HBARBTC', 'NKNBTC', 'STXBTC', 'KAVABTC', 'ARPABTC', 'CTXCBTC', 'BCHBTC', 'TROYBTC', 'VITEBTC', 'OGNBTC', 'DREPBTC', 'TCTBTC', 'WRXBTC', 'LTOBTC', 'MBLBTC', 'COTIBTC', 'STPTBTC', 'SOLBTC', 'CTSIBTC', 'HIVEBTC', 'CHRBTC', 'MDTBTC', 'STMXBTC', 'PNTBTC', 'DGBBTC', 'COMPBTC', 'SXPBTC', 'SNXBTC', 'IRISBTC', 'MKRBTC', 'DAIBTC', 'RUNEBTC', 'FIOBTC', 'AVABTC', 'BALBTC', 'YFIBTC', 'JSTBTC', 'SRMBTC', 'ANTBTC', 'CRVBTC', 'SANDBTC', 'OCEANBTC', 'NMRBTC', 'DOTBTC', 'LUNABTC', 'IDEXBTC', 'RSRBTC', 'PAXGBTC', 'WNXMBTC', 'TRBBTC', 'BZRXBTC', 'WBTCBTC', 'SUSHIBTC', 'YFIIBTC', 'KSMBTC', 'EGLDBTC', 'DIABTC', 'UMABTC', 'BELBTC', 'WINGBTC', 'UNIBTC', 'NBSBTC', 'OXTBTC', 'SUNBTC', 'AVAXBTC', 'FLMBTC', 'SCRTBTC', 'ORNBTC', 'UTKBTC', 'XVSBTC', 'ALPHABTC', 'VIDTBTC', 'AAVEBTC', 'NEARBTC', 'FILBTC', 'INJBTC', 'AERGOBTC', 'AUDIOBTC', 'CTKBTC', 'BOTBTC', 'AKROBTC', 'AXSBTC', 'HARDBTC', 'RENBTCBTC', 'STRAXBTC', 'FORBTC', 'UNFIBTC', 'ROSEBTC', 'SKLBTC', 'SUSDBTC', 'GLMBTC', 'GRTBTC', 'JUVBTC', 'PSGBTC', '1INCHBTC', 'REEFBTC', 'OGBTC', 'ATMBTC', 'ASRBTC', 'CELOBTC', 'RIFBTC', 'BTCSTBTC', 'TRUBTC', 'CKBBTC', 'TWTBTC', 'FIROBTC', 'LITBTC', 'SFPBTC', 'FXSBTC', 'DODOBTC', 'FRONTBTC', 'EASYBTC', 'CAKEBTC', 'ACMBTC', 'AUCTIONBTC', 'PHABTC', 'TVKBTC', 'BADGERBTC', 'FISBTC', 'OMBTC', 'PONDBTC', 'DEGOBTC', 'ALICEBTC', 'LINABTC', 'PERPBTC', 'RAMPBTC', 'SUPERBTC', 'CFXBTC', 'EPSBTC', 'AUTOBTC', 'TKOBTC', 'TLMBTC', 'MIRBTC', 'BARBTC', 'FORTHBTC', 'EZBTC', 'ICPBTC', 'ARBTC', 'POLSBTC', 'MDXBTC', 'LPTBTC', 'AGIXBTC', 'NUBTC', 'ATABTC', 'GTCBTC', 'TORNBTC', 'BAKEBTC', 'KEEPBTC', 'KLAYBTC', 'BONDBTC', 'MLNBTC', 'QUICKBTC', 'C98BTC', 'CLVBTC', 'QNTBTC', 'FLOWBTC', 'MINABTC', 'FARMBTC', 'ALPACABTC', 'MBOXBTC', 'VGXBTC', 'WAXPBTC', 'TRIBEBTC', 'GNOBTC', 'PROMBTC', 'DYDXBTC', 'GALABTC', 'ILVBTC', 'YGGBTC', 'FIDABTC', 'AGLDBTC', 'RADBTC', 'BETABTC', 'RAREBTC', 'SSVBTC', 'LAZIOBTC', 'CHESSBTC', 'DARBTC', 'RGTBTC', 'MOVRBTC', 'CITYBTC', 'ENSBTC', 'QIBTC', 'PORTOBTC', 'JASMYBTC', 'AMPBTC', 'PLABTC', 'PYRBTC', 'RNDRBTC', 'ALCXBTC', 'SANTOSBTC', 'MCBTC', 'ANYBTC', 'BICOBTC', 'FLUXBTC', 'VOXELBTC', 'HIGHBTC', 'CVXBTC', 'PEOPLEBTC', 'SPELLBTC', 'USTBTC', 'JOEBTC', 'ACHBTC', 'IMXBTC', 'GLMRBTC', 'LOKABTC', 'API3BTC', 'ACABTC', 'ANCBTC', 'XNOBTC', 'WOOBTC', 'ALPINEBTC', 'GMTBTC', 'KDABTC', 'APEBTC', 'MULTIBTC', 'ASTRBTC', 'MOBBTC', 'NEXOBTC', 'GALBTC', 'LDOBTC']

eth = ['QTUMETH', 'EOSETH', 'SNTETH', 'BNTETH', 'BNBETH', 'OAXETH', 'DNTETH', 'MCOETH', 'ICNETH', 'WTCETH', 'LRCETH', 'OMGETH', 'ZRXETH', 'STRATETH', 'SNGLSETH', 'BQXETH', 'KNCETH', 'FUNETH', 'SNMETH', 'NEOETH', 'IOTAETH', 'LINKETH', 'XVGETH', 'SALTETH', 'MDAETH', 'MTLETH', 'SUBETH', 'ETCETH', 'MTHETH', 'ENGETH', 'ZECETH', 'ASTETH', 'DASHETH', 'BTGETH', 'EVXETH', 'REQETH', 'VIBETH', 'HSRETH', 'TRXETH', 'POWRETH', 'ARKETH', 'YOYOETH', 'XRPETH', 'MODETH', 'ENJETH', 'STORJETH', 'VENETH', 'KMDETH', 'RCNETH', 'NULSETH', 'RDNETH', 'XMRETH', 'DLTETH', 'AMBETH', 'BCCETH', 'BATETH', 'BCPTETH', 'ARNETH', 'GVTETH', 'CDTETH', 'GXSETH', 'POEETH', 'QSPETH', 'BTSETH', 'XZCETH', 'LSKETH', 'TNTETH', 'FUELETH', 'MANAETH', 'BCDETH', 'DGDETH', 'ADXETH', 'ADAETH', 'PPTETH', 'CMTETH', 'XLMETH', 'CNDETH', 'LENDETH', 'WABIETH', 'LTCETH', 'TNBETH', 'WAVESETH', 'GTOETH', 'ICXETH', 'OSTETH', 'ELFETH', 'AIONETH', 'BRDETH', 'EDOETH', 'WINGSETH', 'NAVETH', 'LUNETH', 'TRIGETH', 'APPCETH', 'VIBEETH', 'RLCETH', 'INSETH', 'IOSTETH', 'CHATETH', 'STEEMETH', 'NANOETH', 'VIAETH', 'BLZETH', 'AEETH', 'RPXETH', 'NCASHETH', 'POAETH', 'ZILETH', 'ONTETH', 'STORMETH', 'XEMETH', 'WANETH', 'WPRETH', 'QLCETH', 'SYSETH', 'GRSETH', 'CLOAKETH', 'GNTETH', 'LOOMETH', 'BCNETH', 'TUSDETH', 'ZENETH', 'SKYETH', 'THETAETH', 'IOTXETH', 'QKCETH', 'AGIETH', 'NXSETH', 'DATAETH', 'SCETH', 'NPXSETH', 'KEYETH', 'NASETH', 'MFTETH', 'DENTETH', 'ARDRETH', 'HOTETH', 'VETETH', 'DOCKETH', 'PHXETH', 'HCETH', 'PAXETH', 'STMXETH', 'WBTCETH', 'SCRTETH', 'AAVEETH', 'EASYETH', 'RENBTCETH', 'SLPETH', 'CVPETH', 'STRAXETH', 'FRONTETH', 'HEGICETH', 'SUSDETH', 'COVERETH', 'GLMETH', 'GHSTETH', 'DFETH', 'GRTETH', 'DEXEETH', 'FIROETH', 'BETHETH', 'PROSETH', 'UFTETH', 'PUNDIXETH', 'EZETH', 'VGXETH', 'AXSETH', 'FTMETH', 'SOLETH', 'SSVETH', 'SANDETH', 'DOTETH', 'MATICETH', 'AVAXETH', 'CHRETH', 'GALAETH', 'LUNAETH', 'ATOMETH', 'EGLDETH', 'ICPETH', 'NEARETH', 'VOXELETH', 'ONEETH', 'DYDXETH', 'FARMETH', 'JASMYETH', 'OOKIETH', 'ROSEETH', 'UNIETH', 'XTZETH', 'CRVETH', 'XNOETH', 'KAVAETH', 'CELRETH', 'BETAETH', 'KSMETH', 'GMTETH', 'RUNEETH', 'APEETH', 'BSWETH', 'ASTRETH', 'REIETH', 'DARETH', 'ALGOETH']

bnb = ['VENBNB', 'YOYOBNB', 'POWRBNB', 'NULSBNB', 'RCNBNB', 'RDNBNB', 'DLTBNB', 'WTCBNB', 'AMBBNB', 'BCCBNB', 'BATBNB', 'BCPTBNB', 'NEOBNB', 'QSPBNB', 'BTSBNB', 'XZCBNB', 'LSKBNB', 'IOTABNB', 'ADXBNB', 'CMTBNB', 'XLMBNB', 'CNDBNB', 'WABIBNB', 'LTCBNB', 'WAVESBNB', 'GTOBNB', 'ICXBNB', 'OSTBNB', 'AIONBNB', 'NEBLBNB', 'BRDBNB', 'MCOBNB', 'NAVBNB', 'TRIGBNB', 'APPCBNB', 'RLCBNB', 'PIVXBNB', 'STEEMBNB', 'NANOBNB', 'VIABNB', 'BLZBNB', 'AEBNB', 'RPXBNB', 'NCASHBNB', 'POABNB', 'ZILBNB', 'ONTBNB', 'STORMBNB', 'QTUMBNB', 'XEMBNB', 'WANBNB', 'SYSBNB', 'QLCBNB', 'ADABNB', 'GNTBNB', 'LOOMBNB', 'BCNBNB', 'REPBNB', 'TUSDBNB', 'ZENBNB', 'SKYBNB', 'EOSBNB', 'THETABNB', 'XRPBNB', 'AGIBNB', 'NXSBNB', 'ENJBNB', 'TRXBNB', 'ETCBNB', 'NASBNB', 'MFTBNB', 'ARDRBNB', 'VETBNB', 'POLYBNB', 'PHXBNB', 'GOBNB', 'PAXBNB', 'DCRBNB', 'USDCBNB', 'MITHBNB', 'RENBNB', 'BTTBNB', 'ONGBNB', 'HOTBNB', 'ZRXBNB', 'FETBNB', 'XMRBNB', 'ZECBNB', 'CELRBNB', 'DASHBNB', 'OMGBNB', 'MATICBNB', 'ATOMBNB', 'PHBBNB', 'TFUELBNB', 'ONEBNB', 'FTMBNB', 'ALGOBNB', 'ERDBNB', 'DOGEBNB', 'DUSKBNB', 'ANKRBNB', 'WINBNB', 'COSBNB', 'COCOSBNB', 'TOMOBNB', 'PERLBNB', 'CHZBNB', 'BANDBNB', 'BEAMBNB', 'XTZBNB', 'HBARBNB', 'NKNBNB', 'STXBNB', 'KAVABNB', 'ARPABNB', 'CTXCBNB', 'BCHBNB', 'TROYBNB', 'VITEBNB', 'OGNBNB', 'DREPBNB', 'TCTBNB', 'WRXBNB', 'LTOBNB', 'STRATBNB', 'MBLBNB', 'COTIBNB', 'STPTBNB', 'SOLBNB', 'CTSIBNB', 'HIVEBNB', 'CHRBNB', 'MDTBNB', 'IQBNB', 'COMPBNB', 'SXPBNB', 'SNXBNB', 'VTHOBNB', 'IRISBNB', 'MKRBNB', 'DAIBNB', 'RUNEBNB', 'FIOBNB', 'AVABNB', 'BALBNB', 'YFIBNB', 'JSTBNB', 'SRMBNB', 'ANTBNB', 'CRVBNB', 'SANDBNB', 'OCEANBNB', 'DOTBNB', 'LUNABNB', 'RSRBNB', 'PAXGBNB', 'WNXMBNB', 'TRBBNB', 'BZRXBNB', 'SUSHIBNB', 'YFIIBNB', 'KSMBNB', 'EGLDBNB', 'DIABNB', 'BELBNB', 'WINGBNB', 'SWRVBNB', 'CREAMBNB', 'UNIBNB', 'AVAXBNB', 'BAKEBNB', 'BURGERBNB', 'FLMBNB', 'CAKEBNB', 'SPARTABNB', 'XVSBNB', 'ALPHABNB', 'AAVEBNB', 'NEARBNB', 'FILBNB', 'INJBNB', 'CTKBNB', 'KP3RBNB', 'AXSBNB', 'HARDBNB', 'UNFIBNB', 'PROMBNB', 'BIFIBNB', 'ICPBNB', 'ARBNB', 'POLSBNB', 'MDXBNB', 'MASKBNB', 'LPTBNB', 'NUBNB', 'ATABNB', 'GTCBNB', 'TORNBNB', 'KEEPBNB', 'ERNBNB', 'KLAYBNB', 'BONDBNB', 'MLNBNB', 'QUICKBNB', 'C98BNB', 'CLVBNB', 'QNTBNB', 'FLOWBNB', 'MINABNB', 'RAYBNB', 'FARMBNB', 'ALPACABNB', 'MBOXBNB', 'WAXPBNB', 'TRIBEBNB', 'GNOBNB', 'DYDXBNB', 'GALABNB', 'ILVBNB', 'YGGBNB', 'FIDABNB', 'AGLDBNB', 'RADBNB', 'BETABNB', 'RAREBNB', 'CHESSBNB', 'DARBNB', 'RGTBNB', 'MOVRBNB', 'CITYBNB', 'ENSBNB', 'QIBNB', 'JASMYBNB', 'AMPBNB', 'PLABNB', 'VOXELBNB', 'MANABNB', 'GXSBNB', 'LINKBNB', 'ROSEBNB', 'ALICEBNB', 'LRCBNB', 'OOKIBNB', 'FORBNB', 'LINABNB', 'LOKABNB', 'HIGHBNB', 'MCBNB', 'WOOBNB', 'PEOPLEBNB', 'SLPBNB', 'SPELLBNB', 'IDEXBNB', 'API3BNB', 'TLMBNB', 'GLMRBNB', 'GMTBNB', 'ANCBNB', 'BSWBNB', 'APEBNB', 'IMXBNB', 'FUNBNB', 'REIBNB', 'GALBNB', 'KNCBNB']

busd = ['BNBBUSD', 'BTCBUSD', 'XRPBUSD', 'ETHBUSD', 'BCHABCBUSD', 'LTCBUSD', 'LINKBUSD', 'ETCBUSD', 'TRXBUSD', 'EOSBUSD', 'XLMBUSD', 'ADABUSD', 'BCHBUSD', 'QTUMBUSD', 'VETBUSD', 'EURBUSD', 'BULLBUSD', 'BEARBUSD', 'ETHBULLBUSD', 'ETHBEARBUSD', 'ICXBUSD', 'BTSBUSD', 'BNTBUSD', 'ATOMBUSD', 'DASHBUSD', 'NEOBUSD', 'WAVESBUSD', 'XTZBUSD', 'EOSBULLBUSD', 'EOSBEARBUSD', 'XRPBULLBUSD', 'XRPBEARBUSD', 'BATBUSD', 'ENJBUSD', 'NANOBUSD', 'ONTBUSD', 'RVNBUSD', 'STRATBUSD', 'AIONBUSD', 'ALGOBUSD', 'BTTBUSD', 'TOMOBUSD', 'XMRBUSD', 'ZECBUSD', 'BNBBULLBUSD', 'BNBBEARBUSD', 'DATABUSD', 'SOLBUSD', 'CTSIBUSD', 'ERDBUSD', 'HBARBUSD', 'MATICBUSD', 'WRXBUSD', 'ZILBUSD', 'KNCBUSD', 'REPBUSD', 'LRCBUSD', 'IQBUSD', 'GBPBUSD', 'DGBBUSD', 'COMPBUSD', 'BKRWBUSD', 'SXPBUSD', 'SNXBUSD', 'VTHOBUSD', 'DCRBUSD', 'STORJBUSD', 'IRISBUSD', 'MKRBUSD', 'DAIBUSD', 'RUNEBUSD', 'MANABUSD', 'DOGEBUSD', 'LENDBUSD', 'ZRXBUSD', 'AUDBUSD', 'FIOBUSD', 'AVABUSD', 'IOTABUSD', 'BALBUSD', 'YFIBUSD', 'BLZBUSD', 'KMDBUSD', 'JSTBUSD', 'SRMBUSD', 'ANTBUSD', 'CRVBUSD', 'SANDBUSD', 'OCEANBUSD', 'NMRBUSD', 'DOTBUSD', 'LUNABUSD', 'IDEXBUSD', 'RSRBUSD', 'PAXGBUSD', 'WNXMBUSD', 'TRBBUSD', 'BZRXBUSD', 'SUSHIBUSD', 'YFIIBUSD', 'KSMBUSD', 'EGLDBUSD', 'DIABUSD', 'BELBUSD', 'SWRVBUSD', 'WINGBUSD', 'CREAMBUSD', 'UNIBUSD', 'AVAXBUSD', 'FLMBUSD', 'CAKEBUSD', 'XVSBUSD', 'ALPHABUSD', 'VIDTBUSD', 'AAVEBUSD', 'NEARBUSD', 'FILBUSD', 'INJBUSD', 'AERGOBUSD', 'ONEBUSD', 'AUDIOBUSD', 'CTKBUSD', 'BOTBUSD', 'KP3RBUSD', 'AXSBUSD', 'HARDBUSD', 'DNTBUSD', 'CVPBUSD', 'STRAXBUSD', 'FORBUSD', 'UNFIBUSD', 'FRONTBUSD', 'BCHABUSD', 'ROSEBUSD', 'SYSBUSD', 'HEGICBUSD', 'PROMBUSD', 'SKLBUSD', 'COVERBUSD', 'GHSTBUSD', 'DFBUSD', 'JUVBUSD', 'PSGBUSD', 'BTCSTBUSD', 'TRUBUSD', 'DEXEBUSD', 'USDCBUSD', 'TUSDBUSD', 'PAXBUSD', 'CKBBUSD', 'TWTBUSD', 'LITBUSD', 'SFPBUSD', 'FXSBUSD', 'DODOBUSD', 'BAKEBUSD', 'UFTBUSD', '1INCHBUSD', 'BANDBUSD', 'GRTBUSD', 'IOSTBUSD', 'OMGBUSD', 'REEFBUSD', 'ACMBUSD', 'AUCTIONBUSD', 'PHABUSD', 'TVKBUSD', 'BADGERBUSD', 'FISBUSD', 'OMBUSD', 'PONDBUSD', 'DEGOBUSD', 'ALICEBUSD', 'CHZBUSD', 'BIFIBUSD', 'LINABUSD', 'PERPBUSD', 'RAMPBUSD', 'SUPERBUSD', 'CFXBUSD', 'XVGBUSD', 'EPSBUSD', 'AUTOBUSD', 'TKOBUSD', 'TLMBUSD', 'BTGBUSD', 'HOTBUSD', 'MIRBUSD', 'BARBUSD', 'FORTHBUSD', 'BURGERBUSD', 'SLPBUSD', 'SHIBBUSD', 'ICPBUSD', 'ARBUSD', 'POLSBUSD', 'MDXBUSD', 'MASKBUSD', 'LPTBUSD', 'NUBUSD', 'RLCBUSD', 'CELRBUSD', 'ATMBUSD', 'ZENBUSD', 'FTMBUSD', 'THETABUSD', 'WINBUSD', 'KAVABUSD', 'XEMBUSD', 'ATABUSD', 'GTCBUSD', 'TORNBUSD', 'COTIBUSD', 'KEEPBUSD', 'SCBUSD', 'CHRBUSD', 'STMXBUSD', 'DOCKBUSD', 'ERNBUSD', 'KLAYBUSD', 'UTKBUSD', 'IOTXBUSD', 'BONDBUSD', 'MLNBUSD', 'LTOBUSD', 'ADXBUSD', 'QUICKBUSD', 'C98BUSD', 'CLVBUSD', 'QNTBUSD', 'FLOWBUSD', 'XECBUSD', 'MINABUSD', 'RAYBUSD', 'FARMBUSD', 'ALPACABUSD', 'ORNBUSD', 'MBOXBUSD', 'WAXPBUSD', 'TRIBEBUSD', 'GNOBUSD', 'MTLBUSD', 'OGNBUSD', 'POLYBUSD', 'DYDXBUSD', 'ELFBUSD', 'USDPBUSD', 'GALABUSD', 'SUNBUSD', 'ILVBUSD', 'RENBUSD', 'YGGBUSD', 'STXBUSD', 'FETBUSD', 'ARPABUSD', 'LSKBUSD', 'FIDABUSD', 'DENTBUSD', 'AGLDBUSD', 'RADBUSD', 'HIVEBUSD', 'STPTBUSD', 'BETABUSD', 'RAREBUSD', 'TROYBUSD', 'CHESSBUSD', 'SCRTBUSD', 'CELOBUSD', 'DARBUSD', 'BNXBUSD', 'RGTBUSD', 'LAZIOBUSD', 'OXTBUSD', 'MOVRBUSD', 'CITYBUSD', 'ENSBUSD', 'ANKRBUSD', 'QIBUSD', 'POWRBUSD', 'JASMYBUSD', 'AMPBUSD', 'PLABUSD', 'PYRBUSD', 'RNDRBUSD', 'ALCXBUSD', 'MCBUSD', 'COCOSBUSD', 'ANYBUSD', 'BICOBUSD', 'FLUXBUSD', 'REQBUSD', 'VOXELBUSD', 'COSBUSD', 'CTXCBUSD', 'HIGHBUSD', 'CVXBUSD', 'PEOPLEBUSD', 'OOKIBUSD', 'MDTBUSD', 'NULSBUSD', 'SPELLBUSD', 'USTBUSD', 'JOEBUSD', 'DUSKBUSD', 'ACHBUSD', 'IMXBUSD', 'GLMRBUSD', 'UMABUSD', 'LOKABUSD', 'API3BUSD', 'ACABUSD', 'ANCBUSD', 'XNOBUSD', 'WOOBUSD', 'TFUELBUSD', 'TBUSD', 'ASTRBUSD', 'GMTBUSD', 'KDABUSD', 'APEBUSD', 'ALPINEBUSD', 'BSWBUSD', 'SANTOSBUSD', 'MULTIBUSD', 'PORTOBUSD', 'BTTCBUSD', 'MBLBUSD', 'MOBBUSD', 'NEXOBUSD', 'GALBUSD', 'LDOBUSD']

tusd = ['BTCTUSD', 'ETHTUSD', 'BNBTUSD', 'XRPTUSD', 'EOSTUSD', 'XLMTUSD', 'ADATUSD', 'TRXTUSD', 'NEOTUSD', 'PAXTUSD', 'USDCTUSD', 'LINKTUSD', 'WAVESTUSD', 'BCHABCTUSD', 'BCHSVTUSD', 'LTCTUSD', 'USDSTUSD', 'BTTTUSD', 'ZECTUSD', 'ATOMTUSD', 'ETCTUSD', 'BATTUSD', 'PHBTUSD', 'TFUELTUSD', 'ONETUSD', 'FTMTUSD', 'BCPTTUSD', 'ALGOTUSD', 'GTOTUSD', 'ANKRTUSD', 'TUSDBTUSD', 'BCHTUSD']

usdc = ['BNBUSDC', 'BTCUSDC', 'ETHUSDC', 'XRPUSDC', 'EOSUSDC', 'XLMUSDC', 'LINKUSDC', 'WAVESUSDC', 'BCHABCUSDC', 'BCHSVUSDC', 'LTCUSDC', 'TRXUSDC', 'USDSUSDC', 'BTTUSDC', 'ZECUSDC', 'ADAUSDC', 'NEOUSDC', 'ATOMUSDC', 'ETCUSDC', 'BATUSDC', 'PHBUSDC', 'TFUELUSDC', 'ONEUSDC', 'FTMUSDC', 'BCPTUSDC', 'ALGOUSDC', 'GTOUSDC', 'ERDUSDC', 'DOGEUSDC', 'DUSKUSDC', 'BGBPUSDC', 'ANKRUSDC', 'ONTUSDC', 'WINUSDC', 'NPXSUSDC', 'TOMOUSDC', 'PERLUSDC', 'BCHUSDC', 'SOLUSDC', 'AUDUSDC', 'BTTCUSDC']

other = ['BNBPAX', 'BTCPAX', 'ETHPAX', 'XRPPAX', 'EOSPAX', 'XLMPAX', 'TRXXRP', 'XZCXRP', 'USDCPAX', 'LINKPAX', 'WAVESPAX', 'BCHABCPAX', 'BCHSVPAX', 'LTCPAX', 'TRXPAX', 'BNBUSDS', 'BTCUSDS', 'USDSPAX', 'BTTPAX', 'ZECPAX', 'ADAPAX', 'NEOPAX', 'ATOMPAX', 'ETCPAX', 'BATPAX', 'PHBPAX', 'TFUELPAX', 'ONEPAX', 'FTMPAX', 'BCPTPAX', 'ALGOPAX', 'USDSBUSDS', 'GTOPAX', 'ERDPAX', 'DOGEPAX', 'DUSKPAX', 'ANKRPAX', 'ONTPAX', 'BTTTRX', 'WINTRX', 'BUSDNGN', 'BNBNGN', 'BTCNGN', 'BCHPAX', 'BTCRUB', 'ETHRUB', 'XRPRUB', 'BNBRUB', 'BUSDRUB', 'BTCTRY', 'BNBTRY', 'BUSDTRY', 'ETHTRY', 'XRPTRY', 'USDTTRY', 'USDTRUB', 'BTCEUR', 'ETHEUR', 'BNBEUR', 'XRPEUR', 'BTCZAR', 'ETHZAR', 'BNBZAR', 'USDTZAR', 'BUSDZAR', 'BTCBKRW', 'ETHBKRW', 'BNBBKRW', 'BTCIDRT', 'BNBIDRT', 'USDTIDRT', 'BUSDIDRT', 'BTCGBP', 'ETHGBP', 'XRPGBP', 'BNBGBP', 'BTCUAH', 'USDTUAH', 'BTCBIDR', 'ETHBIDR', 'BNBBIDR', 'BUSDBIDR', 'USDTBIDR', 'XRPBKRW', 'ADABKRW', 'BTCAUD', 'ETHAUD', 'USDTBKRW', 'BUSDBKRW', 'XRPAUD', 'BNBAUD', 'BTCDAI', 'ETHDAI', 'BNBDAI', 'USDTDAI', 'BUSDDAI', 'ETHNGN', 'DOTBIDR', 'LINKAUD', 'SXPAUD', 'SRMBIDR', 'ONEBIDR', 'LINKTRY', 'USDTNGN', 'LENDBKRW', 'SXPEUR', 'SXPBIDR', 'LINKBKRW', 'TRXNGN', 'SXPTRY', 'BTCBRL', 'USDTBRL', 'AAVEBKRW', 'DOTBKRW', 'SXPGBP', 'LINKEUR', 'ETHBRL', 'DOTEUR', 'BNBBRL', 'LTCEUR', 'ADAEUR', 'LTCNGN', 'AVAXTRY', 'BUSDBRL', 'XRPBRL', 'XRPNGN', 'BCHEUR', 'YFIEUR', 'ZILBIDR', 'LINKBRL', 'LINKNGN', 'LTCRUB', 'TRXTRY', 'XLMEUR', 'BUSDBVND', 'USDTBVND', 'CHZTRY', 'XLMTRY', 'LINKGBP', 'GRTEUR', 'EOSEUR', 'LTCBRL', 'DOGEEUR', 'DOGETRY', 'DOGEAUD', 'DOGEBRL', 'DOTNGN', 'BTCVAI', 'BUSDVAI', 'DOGEGBP', 'DOTTRY', 'DOTGBP', 'ADATRY', 'ADABRL', 'ADAGBP', 'DOTBRL', 'ADAAUD', 'HOTTRY', 'EGLDEUR', 'AVAXEUR', 'BTTTRY', 'CHZBRL', 'UNIEUR', 'CHZEUR', 'CHZGBP', 'ADARUB', 'ENJBRL', 'ENJEUR', 'MATICEUR', 'NEOTRY', 'ENJGBP', 'EOSTRY', 'LTCGBP', 'LUNAEUR', 'RVNTRY', 'THETAEUR', 'TKOBIDR', 'BTTBRL', 'BTTEUR', 'HOTEUR', 'WINEUR', 'BNBUAH', 'ONTTRY', 'VETEUR', 'VETGBP', 'WINBRL', 'CAKEGBP', 'DOGERUB', 'HOTBRL', 'WRXEUR', 'TRXAUD', 'TRXEUR', 'VETTRY', 'BTCGYEN', 'USDTGYEN', 'SHIBEUR', 'SHIBRUB', 'ETCEUR', 'ETCBRL', 'DOGEBIDR', 'ETHUAH', 'MATICBRL', 'SOLEUR', 'SHIBBRL', 'ICPEUR', 'MATICGBP', 'SHIBTRY', 'MATICBIDR', 'MATICRUB', 'MATICTRY', 'ETCGBP', 'SOLGBP', 'SOLTRY', 'RUNEGBP', 'SOLBRL', 'ADABIDR', 'RUNEEUR', 'MATICAUD', 'DOTRUB', 'SOLRUB', 'RUNEAUD', 'BUSDUAH', 'GRTTRY', 'CAKEBRL', 'ICPRUB', 'DOTAUD', 'AAVEBRL', 'EOSAUD', 'AXSBRL', 'AXSAUD', 'TLMTRY', 'TRURUB', 'FISBRL', 'ARPATRY', 'C98BRL', 'SOLAUD', 'XRPBIDR', 'SOLBIDR', 'AXSBIDR', 'BTCUSDP', 'ETHUSDP', 'BNBUSDP', 'FTMBIDR', 'ALGOBIDR', 'CAKEAUD', 'KSMAUD', 'WAVESRUB', 'ARPARUB', 'LTCUAH', 'AVAXBIDR', 'ALICEBIDR', 'UNIAUD', 'SHIBAUD', 'AVAXBRL', 'AVAXAUD', 'LUNAAUD', 'LAZIOTRY', 'LAZIOEUR', 'FTMAUD', 'FTMBRL', 'FTMRUB', 'NUAUD', 'NURUB', 'REEFTRY', 'REEFBIDR', 'SHIBDOGE', 'MANATRY', 'ALGORUB', 'SHIBUAH', 'LUNABIDR', 'SANDTRY', 'MANABRL', 'PORTOTRY', 'PORTOEUR', 'SLPTRY', 'FISTRY', 'LRCTRY', 'FISBIDR', 'SANTOSBRL', 'SANTOSTRY', 'BELTRY', 'DENTTRY', 'ENJTRY', 'NEORUB', 'SANDAUD', 'SLPBIDR', 'ALICETRY', 'GALABRL', 'GALATRY', 'LUNATRY', 'SANDBRL', 'MANABIDR', 'SANDBIDR', 'FTMTRY', 'MINATRY', 'XTZTRY', 'COCOSTRY', 'LUNABRL', 'LUNAUST', 'ATOMTRY', 'ETHUST', 'GALAAUD', 'ATOMBIDR', 'ICPTRY', 'ATOMBRL', 'BNBUST', 'NEARRUB', 'ROSETRY', 'BTTCTRY', 'BDOTDOT', 'COSTRY', 'ONETRY', 'SPELLTRY', 'AXSTRY', 'DARTRY', 'NEARTRY', 'ALPINEEUR', 'ALPINETRY', 'INJTRY', 'API3TRY', 'MBOXTRY', 'NBTBIDR', 'ATOMEUR', 'GALAEUR', 'UMATRY', 'LUNAGBP', 'NEAREUR', 'TWTTRY', 'WAVESEUR', 'APEEUR', 'APEGBP', 'APETRY', 'GMTBRL', 'JASMYTRY', 'APEAUD', 'GMTEUR', 'AVAXGBP', 'FILTRY', 'FTMEUR', 'GMTGBP', 'ZILTRY', 'GMTTRY', 'WAVESTRY', 'BTCUST', 'BSWTRY', 'ZILEUR', 'APEBRL', 'AUDIOTRY', 'GMTAUD', 'JASMYEUR', 'SHIBGBP', 'GALEUR', 'GALTRY', 'ENSTRY', 'DAREUR']

SYMBOLS_SPOT = usdt + btc + eth + bnb + busd + usdc + tusd + other
SYMBOLS_SPOT = usdt

um_usdt = ['BTCUSDT', 'ETHUSDT', 'BCHUSDT', 'XRPUSDT', 'EOSUSDT', 'LTCUSDT', 'TRXUSDT', 'ETCUSDT', 'LINKUSDT', 'XLMUSDT', 'ADAUSDT', 'XMRUSDT', 'DASHUSDT', 'ZECUSDT', 'XTZUSDT', 'BNBUSDT', 'ATOMUSDT', 'ONTUSDT', 'IOTAUSDT', 'BATUSDT', 'VETUSDT', 'NEOUSDT', 'QTUMUSDT', 'IOSTUSDT', 'THETAUSDT', 'ALGOUSDT', 'ZILUSDT', 'KNCUSDT', 'ZRXUSDT', 'COMPUSDT', 'OMGUSDT', 'DOGEUSDT', 'SXPUSDT', 'KAVAUSDT', 'BANDUSDT', 'RLCUSDT', 'WAVESUSDT', 'MKRUSDT', 'SNXUSDT', 'DOTUSDT', 'DEFIUSDT', 'YFIUSDT', 'BALUSDT', 'CRVUSDT', 'TRBUSDT', 'RUNEUSDT', 'SUSHIUSDT', 'EGLDUSDT', 'SOLUSDT', 'ICXUSDT', 'STORJUSDT', 'BLZUSDT', 'UNIUSDT', 'AVAXUSDT', 'FTMUSDT', 'ENJUSDT', 'FLMUSDT', 'RENUSDT', 'KSMUSDT', 'NEARUSDT', 'AAVEUSDT', 'FILUSDT', 'RSRUSDT', 'LRCUSDT', 'OCEANUSDT', 'CVCUSDT', 'BELUSDT', 'CTKUSDT', 'AXSUSDT', 'ALPHAUSDT', 'ZENUSDT', 'SKLUSDT', 'GRTUSDT', '1INCHUSDT', 'CHZUSDT', 'SANDUSDT', 'ANKRUSDT', 'UNFIUSDT', 'REEFUSDT', 'RVNUSDT', 'SFPUSDT', 'XEMUSDT', 'COTIUSDT', 'CHRUSDT', 'MANAUSDT', 'ALICEUSDT', 'HBARUSDT', 'ONEUSDT', 'LINAUSDT', 'STMXUSDT', 'DENTUSDT', 'CELRUSDT', 'HOTUSDT', 'MTLUSDT', 'OGNUSDT', 'NKNUSDT', 'SCUSDT', 'DGBUSDT', '1000SHIBUSDT', 'BAKEUSDT', 'GTCUSDT', 'BTCDOMUSDT', 'IOTXUSDT', 'RAYUSDT', 'C98USDT', 'MASKUSDT', 'ATAUSDT', 'DYDXUSDT', '1000XECUSDT', 'GALAUSDT', 'CELOUSDT', 'ARUSDT', 'KLAYUSDT', 'ARPAUSDT', 'CTSIUSDT', 'LPTUSDT', 'ENSUSDT', 'PEOPLEUSDT', 'ROSEUSDT', 'DUSKUSDT', 'FLOWUSDT', 'IMXUSDT', 'API3USDT', 'GMTUSDT', 'APEUSDT', 'WOOUSDT', 'FTTUSDT', 'JASMYUSDT', 'DARUSDT', 'OPUSDT', 'INJUSDT', 'STGUSDT', 'SPELLUSDT', '1000LUNCUSDT', 'LUNA2USDT', 'LDOUSDT', 'CVXUSDT', 'ICPUSDT', 'APTUSDT', 'QNTUSDT', 'FETUSDT', 'FXSUSDT', 'HOOKUSDT', 'MAGICUSDT', 'TUSDT', 'HIGHUSDT', 'MINAUSDT', 'ASTRUSDT', 'AGIXUSDT', 'PHBUSDT', 'GMXUSDT', 'CFXUSDT', 'STXUSDT', 'BNXUSDT', 'ACHUSDT', 'SSVUSDT', 'CKBUSDT', 'PERPUSDT', 'TRUUSDT', 'LQTYUSDT', 'USDCUSDT', 'IDUSDT', 'ARBUSDT', 'JOEUSDT', 'TLMUSDT', 'AMBUSDT', 'LEVERUSDT', 'RDNTUSDT', 'HFTUSDT', 'XVSUSDT', 'BLURUSDT', 'EDUUSDT', 'IDEXUSDT', 'SUIUSDT', '1000PEPEUSDT', '1000FLOKIUSDT', 'UMAUSDT', 'RADUSDT', 'KEYUSDT', 'COMBOUSDT', 'NMRUSDT', 'MAVUSDT', 'MDTUSDT', 'XVGUSDT', 'WLDUSDT', 'PENDLEUSDT', 'ARKMUSDT', 'AGLDUSDT', 'YGGUSDT', 'DODOXUSDT', 'BNTUSDT', 'OXTUSDT', 'SEIUSDT', 'CYBERUSDT', 'HIFIUSDT', 'ARKUSDT', 'GLMRUSDT', 'BICOUSDT', 'STRAXUSDT', 'LOOMUSDT', 'BIGTIMEUSDT', 'BONDUSDT', 'ORBSUSDT', 'STPTUSDT', 'WAXPUSDT', 'BSVUSDT', 'RIFUSDT', 'POLYXUSDT', 'GASUSDT', 'POWRUSDT', 'SLPUSDT', 'TIAUSDT', 'SNTUSDT', 'CAKEUSDT', 'MEMEUSDT', 'TWTUSDT', 'TOKENUSDT', 'ORDIUSDT', 'STEEMUSDT', 'BADGERUSDT', 'ILVUSDT', 'NTRNUSDT', 'KASUSDT', 'BEAMXUSDT', '1000BONKUSDT', 'PYTHUSDT', 'SUPERUSDT', 'USTCUSDT', 'ONGUSDT', 'ETHWUSDT', 'JTOUSDT', '1000SATSUSDT', 'AUCTIONUSDT', '1000RATSUSDT', 'ACEUSDT', 'MOVRUSDT', 'NFPUSDT', 'AIUSDT', 'XAIUSDT', 'WIFUSDT', 'MANTAUSDT', 'ONDOUSDT', 'LSKUSDT', 'ALTUSDT', 'JUPUSDT', 'ZETAUSDT', 'RONINUSDT', 'DYMUSDT', 'OMUSDT', 'PIXELUSDT', 'STRKUSDT', 'MAVIAUSDT', 'GLMUSDT', 'PORTALUSDT', 'TONUSDT', 'AXLUSDT', 'MYROUSDT', 'METISUSDT', 'AEVOUSDT', 'VANRYUSDT', 'BOMEUSDT', 'ETHFIUSDT', 'ENAUSDT', 'WUSDT', 'TNSRUSDT', 'SAGAUSDT', 'TAOUSDT', 'OMNIUSDT', 'REZUSDT', 'BBUSDT', 'NOTUSDT', 'TURBOUSDT', 'IOUSDT', 'ZKUSDT', 'MEWUSDT', 'LISTAUSDT', 'ZROUSDT', 'RENDERUSDT', 'BANANAUSDT', 'RAREUSDT', 'GUSDT', 'SYNUSDT', 'SYSUSDT', 'VOXELUSDT', 'BRETTUSDT', 'ALPACAUSDT', 'POPCATUSDT', 'SUNUSDT', 'VIDTUSDT', 'NULSUSDT', 'DOGSUSDT', 'MBOXUSDT', 'CHESSUSDT', 'FLUXUSDT', 'BSWUSDT', 'QUICKUSDT', 'NEIROETHUSDT', 'RPLUSDT', 'AERGOUSDT', 'POLUSDT', 'UXLINKUSDT', '1MBABYDOGEUSDT', 'NEIROUSDT', 'KDAUSDT', 'FIDAUSDT', 'FIOUSDT', 'CATIUSDT', 'GHSTUSDT', 'LOKAUSDT', 'HMSTRUSDT', 'REIUSDT', 'COSUSDT', 'EIGENUSDT', 'DIAUSDT', '1000CATUSDT', 'SCRUSDT', 'GOATUSDT', 'MOODENGUSDT', 'SAFEUSDT', 'SANTOSUSDT', 'TROYUSDT', 'PONKEUSDT', 'COWUSDT', 'CETUSUSDT', '1000000MOGUSDT', 'GRASSUSDT', 'DRIFTUSDT', 'SWELLUSDT', 'ACTUSDT', 'PNUTUSDT', 'HIPPOUSDT', '1000XUSDT', 'DEGENUSDT', 'BANUSDT', 'AKTUSDT', 'SLERFUSDT', 'SCRTUSDT', '1000CHEEMSUSDT', '1000WHYUSDT', 'TRUMPUSDT'] 

um_busd = ['ADABUSD', 'AVAXBUSD', 'BNBBUSD', 'BTCBUSD', 'DOGEBUSD', 'ETHBUSD', 'SOLBUSD', 'XRPBUSD']

SYMBOLS_UM = um_usdt# + um_busd

SYMBOLS_CM = ['BTCUSD_PERP', 'ETHUSD_PERP', 'LINKUSD_PERP', 'BNBUSD_PERP', 'TRXUSD_PERP', 'DOTUSD_PERP', 'ADAUSD_PERP', 'EOSUSD_PERP', 'LTCUSD_PERP', 'BCHUSD_PERP', 'XRPUSD_PERP', 'ETCUSD_PERP', 'FILUSD_PERP', 'EGLDUSD_PERP', 'DOGEUSD_PERP', 'UNIUSD_PERP', 'THETAUSD_PERP', 'XLMUSD_PERP', 'SOLUSD_PERP', 'FTMUSD_PERP', 'SANDUSD_PERP', 'MANAUSD_PERP', 'AVAXUSD_PERP', 'GALAUSD_PERP', 'MATICUSD_PERP', 'NEARUSD_PERP', 'ATOMUSD_PERP', 'AAVEUSD_PERP', 'CRVUSD_PERP', 'AXSUSD_PERP', 'ROSEUSD_PERP', 'XTZUSD_PERP', 'ICXUSD_PERP', 'ALGOUSD_PERP', 'RUNEUSD_PERP', 'APEUSD_PERP', 'VETUSD_PERP', 'ZILUSD_PERP', 'KNCUSD_PERP', 'XMRUSD_PERP', 'GMTUSD_PERP']
