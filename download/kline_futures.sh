#!/bin/bash

cd kline

header="z,s,i,t,o,h,l,c,v,T,q,n,V,Q"

#echo ${header} > "${type}_klines.csv"

for syml in BTC ETH BCH XRP EOS LTC TRX ETC LINK XLM ADA XMR DASH ZEC XTZ BNB ATOM ONT IOTA BAT VET NEO QTUM IOST THETA ALGO ZIL KNC ZRX COMP OMG DOGE SXP KAVA BAND RLC WAVES MKR SNX DOT DEFI YFI BAL CRV TRB RUNE SUSHI EGLD SOL ICX STORJ BLZ UNI AVAX FTM ENJ FLM REN KSM NEAR AAVE FIL RSR LRC OCEAN CVC BEL CTK AXS ALPHA ZEN SKL GRT 1INCH CHZ SAND ANKR LIT UNFI REEF RVN SFP XEM COTI CHR MANA ALICE HBAR ONE LINA STMX DENT CELR HOT MTL OGN NKN SC DGB 1000SHIB BAKE GTC BTCDOM IOTX RAY C98 MASK ATA DYDX 1000XEC GALA CELO AR KLAY ARPA CTSI LPT ENS PEOPLE ROSE DUSK FLOW IMX API3 GMT APE WOO FTT JASMY DAR OP INJ STG SPELL 1000LUNC LUNA2 LDO CVX ICP APT QNT FET FXS HOOK MAGIC T HIGH MINA ASTR AGIX PHB GMX CFX STX BNX ACH SSV CKB PERP TRU LQTY USDC ID ARB JOE TLM AMB LEVER RDNT HFT XVS BLUR EDU IDEX SUI 1000PEPE 1000FLOKI UMA RAD KEY COMBO NMR MAV MDT XVG WLD PENDLE ARKM AGLD YGG DODOX BNT OXT SEI CYBER HIFI ARK GLMR BICO STRAX LOOM BIGTIME BOND ORBS STPT WAXP BSV RIF POLYX GAS POWR SLP TIA SNT CAKE MEME TWT TOKEN ORDI STEEM BADGER ILV NTRN KAS BEAMX 1000BONK PYTH SUPER USTC ONG ETHW JTO 1000SATS AUCTION 1000RATS ACE MOVR NFP AI XAI WIF MANTA ONDO LSK ALT JUP ZETA RONIN DYM OM PIXEL STRK MAVIA GLM PORTAL TON AXL MYRO METIS AEVO VANRY BOME ETHFI ENA W TNSR SAGA TAO OMNI REZ BB NOT TURBO IO ZK MEW LISTA ZRO RENDER BANANA RARE G SYN SYS VOXEL BRETT ALPACA POPCAT SUN VIDT NULS DOGS MBOX CHESS FLUX BSW QUICK NEIROETH RPL AERGO POL UXLINK 1MBABYDOGE NEIRO KDA FIDA FIO CATI GHST LOKA HMSTR REI COS EIGEN DIA 1000CAT SCR GOAT MOODENG SAFE SANTOS TROY PONKE COW CETUS 1000000MOG GRASS DRIFT SWELL ACT PNUT HIPPO 1000X DEGEN BAN AKT SLERF SCRT 1000CHEEMS 1000WHY TRUMP;
do
	# symbol='BTCUSDT'
	symbol="${syml}USDT"

	echo "`date`: futures_$symbol"
	mkdir -p "${syml}"
	cd "${syml}"

	# rm -r "${syml}"*
	#echo ${header} > "${syml}.csv"
	# month
	# for date_ in 2019-01 2019-02 2019-03 2019-04 2019-05 2019-06 2019-07 2019-08 2019-09 2019-10 2019-11 2019-12 2020-01 2020-02 2020-03 2020-04 2020-05 2020-06 2020-07 2020-08 2020-09 2020-10 2020-11 2020-12 2021-01 2021-02 2021-03 2021-04 2021-05 2021-06 2021-07 2021-08 2021-09 2021-10 2021-11 2021-12 2022-01 2022-02 2022-03 2022-04 2022-05 2022-06 2022-07 2022-08 2022-09 2022-10 2022-11 2022-12 2023-01 2023-02 2023-03 2023-04 2023-05 2023-06 2023-07 2023-08 2023-09 2023-10 2023-11 2023-12 2024-01 2024-02 2024-03 2024-04 2024-05 2024-06 2024-07 2024-08 2024-09 2024-10 2024-11;
	for date_ in 2024-12;
	do
		# echo "z,s,e,i,t,o,h,l,c,v,T,q,n,V,Q" >"${date_}-klines.csv"
		prefix='/Volumes/alpha/data/futures/um/monthly'

		echo ${header} > "${syml}_${date_}.csv"

		for inter in 1m 3m 5m 15m 30m 1h 2h 4h 6h;
		do
			kline_f="${prefix}/klines/${symbol}/${inter}/${symbol}-${inter}-${date_}.zip"
			if [ ! -f "$kline_f" ]; then
				echo "$kline_f does not exist!"
				if [[ "$inter" == "1m" ]]; then
					rm -r "${syml}_${date_}.csv"
				fi
				break
			fi
			cp "$kline_f" .;
			unzip -q "${symbol}-${inter}-${date_}.zip";
			
			grep -v 'open' "${symbol}-${inter}-${date_}.csv" | gawk -F ',' -v var=",${syml},$inter," '{print strftime("%F %T",int($1/1000))var""substr($0,0,length($0)-2)}' >>"${syml}_${date_}.csv";

			rm "${symbol}-${inter}-${date_}.zip" ;
			rm "${symbol}-${inter}-${date_}.csv" ;
		done
	done
	cd ..


	# for date_ in 2023-07-01 2023-07-02 2023-07-03 2023-07-04 2023-07-05 2023-07-06 2023-07-07 2023-07-08 2023-07-09 2023-07-10 2023-07-11 2023-07-12 2023-07-13 2023-07-14 2023-07-15 2023-07-16 2023-07-17 2023-07-18 2023-07-19 2023-07-20 2023-07-21 2023-07-22 2023-07-23 2023-07-24 2023-07-25 2023-07-26 2023-07-27 2023-07-28 2023-07-29 2023-07-30 2023-07-31;
	# do
	# continue
	# 	# echo "z,s,e,i,t,o,h,l,c,v,T,q,n,V,Q" >"${date_}-klines.csv"
	# 	prefix='/Volumes/gamma/history/data/spot/daily'
	# 	if [[ "$type" == "futures" ]]; then
	# 		prefix='/Volumes/gamma/history/data/futures/um/daily'
	# 	fi
	# 	for inter in 1m 3m 5m 15m 30m 1h 2h 4h 6h 8h;
	# 	do
	# 		kline_f="${prefix}/klines/${symbol}/${inter}/${symbol}-${inter}-${date_}.zip"
	# 		if [ ! -f "$kline_f" ]; then
	# 			echo "$kline_f does not exist!"
	# 			break
	# 		fi

	# 		cp "$kline_f" .;
	# 	    unzip -q "${symbol}-${inter}-${date_}.zip";
			
	# 	    # gawk -F ',' -v var=",${symbol},kline,$inter," '{print strftime("%F %T",int($1/1000))"."substr($1,11,3)var""substr($0,0,length($0)-2)}' "${symbol}-${inter}-${date_}.csv" >>"${symbol}_${date_}-klines.csv" ;
	# 	    grep -v 'open' "${symbol}-${inter}-${date_}.csv" | gawk -F ',' -v var=",${syml},$inter," '{print strftime("%F %T",int($1/1000))var""substr($0,0,length($0)-2)}' >>"${syml}.csv";
	# 		#grep -v 'open' "${symbol}-${inter}-${date_}.csv" | gawk -F ',' -v var=",${syml},$inter," '{print strftime("%F %T",int($1/1000))var""substr($0,0,length($0)-2)}' >>"${type}.csv";
			
	# 	    rm "${symbol}-${inter}-${date_}.zip"
	# 	    rm "${symbol}-${inter}-${date_}.csv"
	# 	done
	# done

done
cd ..


