#!/bin/bash

cd kline

header="z,s,i,t,o,h,l,c,v,T,q,n,V,Q"

#echo ${header} > "${type}_klines.csv"

# for syml in BTC ETH BNB NEO LTC QTUM ADA XRP EOS TUSD IOTA XLM ONT TRX ETC ICX NULS VET USDC LINK ONG HOT ZIL ZRX FET BAT ZEC IOST CELR DASH THETA ENJ ATOM TFUEL ONE FTM ALGO DOGE DUSK ANKR WIN COS MTL DENT WAN FUN CVC CHZ BAND XTZ RVN HBAR NKN STX KAVA ARPA IOTX RLC CTXC BCH TROY VITE FTT EUR OGN WRX LSK BNT LTO MBL COTI STPT DATA SOL CTSI HIVE CHR ARDR MDT STMX KNC LRC COMP SC ZEN SNX VTHO DGB SXP MKR DCR STORJ MANA YFI BAL BLZ KMD JST CRV SAND NMR DOT LUNA RSR PAXG TRB SUSHI KSM EGLD DIA RUNE FIO UMA BEL WING UNI OXT SUN AVAX FLM UTK XVS ALPHA AAVE NEAR FIL INJ AUDIO CTK AKRO AXS HARD STRAX ROSE AVA SKL GRT JUV PSG 1INCH OG ATM ASR CELO RIF TRU CKB TWT FIRO LIT SFP DODO CAKE ACM BADGER FIS OM POND DEGO ALICE LINA PERP SUPER CFX TKO PUNDIX TLM BAR FORTH BAKE BURGER SLP SHIB ICP AR MASK LPT XVG ATA GTC ERN PHA MLN DEXE C98 CLV QNT FLOW MINA RAY FARM ALPACA QUICK MBOX REQ GHST WAXP GNO XEC ELF DYDX IDEX VIDT USDP GALA ILV YGG SYS DF FIDA AGLD RAD BETA RARE LAZIO CHESS ADX AUCTION DAR BNX MOVR CITY ENS QI PORTO POWR JASMY AMP PYR ALCX SANTOS BICO FLUX FXS VOXEL HIGH CVX PEOPLE SPELL JOE ACH IMX GLMR LOKA SCRT API3 BTTC ACA XNO WOO ALPINE T ASTR GMT KDA APE BSW BIFI STEEM NEXO REI LDO OP LEVER STG LUNC GMX POLYX APT OSMO HFT PHB HOOK MAGIC HIFI RPL PROS GNS SYN VIB SSV LQTY AMB USTC GAS GLM PROM QKC UFT ID ARB RDNT WBTC EDU SUI AERGO PEPE FLOKI AST SNT COMBO MAV PENDLE ARKM WBETH WLD FDUSD SEI CYBER ARK CREAM IQ NTRN TIA MEME ORDI BEAMX PIVX VIC BLUR VANRY AEUR JTO 1000SATS BONK ACE NFP AI XAI MANTA ALT JUP PYTH RONIN DYM PIXEL STRK PORTAL PDA AXL WIF METIS AEVO BOME ETHFI ENA W TNSR SAGA TAO OMNI REZ BB NOT IO ZK LISTA ZRO G BANANA RENDER TON DOGS EURI SLF POL NEIRO TURBO 1MBABYDOGE CATI HMSTR EIGEN SCR BNSOL LUMIA KAIA COW CETUS PNUT ACT USUAL THE ACX ORCA MOVE ME VELODROME;
for syml in BNB;
do
	# symbol='BTCUSDT'
	symbol="${syml}USDT"

	echo "`date`: spot_$symbol"
	mkdir -p "${syml}"
	cd "${syml}"

	# rm -r "${syml}"*
	#echo ${header} > "${syml}.csv"
	# month
	# for date_ in 2019-01 2019-02 2019-03 2019-04 2019-05 2019-06 2019-07 2019-08 2019-09 2019-10 2019-11 2019-12 2020-01 2020-02 2020-03 2020-04 2020-05 2020-06 2020-07 2020-08 2020-09 2020-10 2020-11 2020-12 2021-01 2021-02 2021-03 2021-04 2021-05 2021-06 2021-07 2021-08 2021-09 2021-10 2021-11 2021-12 2022-01 2022-02 2022-03 2022-04 2022-05 2022-06 2022-07 2022-08 2022-09 2022-10 2022-11 2022-12 2023-01 2023-02 2023-03 2023-04 2023-05 2023-06 2023-07 2023-08 2023-09 2023-10 2023-11 2023-12 2024-01 2024-02 2024-03 2024-04 2024-05 2024-06 2024-07 2024-08 2024-09 2024-10 2024-11;
	for date_ in 2020-06
	do
		# echo "z,s,e,i,t,o,h,l,c,v,T,q,n,V,Q" >"${date_}-klines.csv"
		prefix='/Volumes/alpha/data/spot/monthly'

		echo ${header} > "${syml}_${date_}.csv"

		for inter in 1m 3m 5m 15m 30m 1h 2h 4h 6h;
		do
			kline_f="${prefix}/klines/${symbol}/${inter}/${symbol}-${inter}-${date_}.zip"
			if [ ! -f "$kline_f" ]; then
				echo "$kline_f does not exist!"
				if [[ "$inter" == "1m" ]]; then
					rm -r "${syml}_${date_}.csv"
				fi
				break
			fi
			cp "$kline_f" .;
			unzip -q "${symbol}-${inter}-${date_}.zip";
			
			grep -v 'open' "${symbol}-${inter}-${date_}.csv" | gawk -F ',' -v var=",${syml},$inter," '{print strftime("%F %T",int($1/1000))var""substr($0,0,length($0)-2)}' >>"${syml}_${date_}.csv";

			rm "${symbol}-${inter}-${date_}.zip" ;
			rm "${symbol}-${inter}-${date_}.csv" ;
		done
	done
	cd ..


	# for date_ in 2023-07-01 2023-07-02 2023-07-03 2023-07-04 2023-07-05 2023-07-06 2023-07-07 2023-07-08 2023-07-09 2023-07-10 2023-07-11 2023-07-12 2023-07-13 2023-07-14 2023-07-15 2023-07-16 2023-07-17 2023-07-18 2023-07-19 2023-07-20 2023-07-21 2023-07-22 2023-07-23 2023-07-24 2023-07-25 2023-07-26 2023-07-27 2023-07-28 2023-07-29 2023-07-30 2023-07-31;
	# do
	# continue
	# 	# echo "z,s,e,i,t,o,h,l,c,v,T,q,n,V,Q" >"${date_}-klines.csv"
	# 	prefix='/Volumes/gamma/history/data/spot/daily'
	# 	if [[ "$type" == "futures" ]]; then
	# 		prefix='/Volumes/gamma/history/data/futures/um/daily'
	# 	fi
	# 	for inter in 1m 3m 5m 15m 30m 1h 2h 4h 6h 8h;
	# 	do
	# 		kline_f="${prefix}/klines/${symbol}/${inter}/${symbol}-${inter}-${date_}.zip"
	# 		if [ ! -f "$kline_f" ]; then
	# 			echo "$kline_f does not exist!"
	# 			break
	# 		fi

	# 		cp "$kline_f" .;
	# 	    unzip -q "${symbol}-${inter}-${date_}.zip";
			
	# 	    # gawk -F ',' -v var=",${symbol},kline,$inter," '{print strftime("%F %T",int($1/1000))"."substr($1,11,3)var""substr($0,0,length($0)-2)}' "${symbol}-${inter}-${date_}.csv" >>"${symbol}_${date_}-klines.csv" ;
	# 	    grep -v 'open' "${symbol}-${inter}-${date_}.csv" | gawk -F ',' -v var=",${syml},$inter," '{print strftime("%F %T",int($1/1000))var""substr($0,0,length($0)-2)}' >>"${syml}.csv";
	# 		#grep -v 'open' "${symbol}-${inter}-${date_}.csv" | gawk -F ',' -v var=",${syml},$inter," '{print strftime("%F %T",int($1/1000))var""substr($0,0,length($0)-2)}' >>"${type}.csv";
			
	# 	    rm "${symbol}-${inter}-${date_}.zip"
	# 	    rm "${symbol}-${inter}-${date_}.csv"
	# 	done
	# done

done
cd ..


