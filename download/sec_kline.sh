#!/bin/bash

cd kline_1s

header="z,s,i,t,o,h,l,c,v,T,q,n,V,Q"

# 定义单个币种的处理函数
process_symbol() {
	local syml=$1
	local symbol="${syml}USDT"
	echo "`date`: spot_$symbol"

	mkdir -p "${syml}"
	cd "${syml}"

	rm -r "${syml}"* 2>/dev/null

	# 处理每个月份的数据
	for date_ in 2019-01 2019-02 2019-03 2019-04 2019-05 2019-06 2019-07 2019-08 2019-09 2019-10 2019-11 2019-12 2020-01 2020-02 2020-03 2020-04 2020-05 2020-06 2020-07 2020-08 2020-09 2020-10 2020-11 2020-12 2021-01 2021-02 2021-03 2021-04 2021-05 2021-06 2021-07 2021-08 2021-09 2021-10 2021-11 2021-12 2022-01 2022-02 2022-03 2022-04 2022-05 2022-06 2022-07 2022-08 2022-09 2022-10 2022-11 2022-12 2023-01 2023-02 2023-03 2023-04 2023-05 2023-06 2023-07 2023-08 2023-09 2023-10 2023-11 2023-12 2024-01 2024-02 2024-03 2024-04 2024-05 2024-06 2024-07 2024-08 2024-09 2024-10 2024-11
	do
		prefix='/Volumes/alpha/data/spot/monthly'
		kline_f="${prefix}/klines/${symbol}/1s/${symbol}-1s-${date_}.zip"
		
		if [ ! -f "$kline_f" ]; then
			echo "$kline_f does not exist!"
			continue
		fi
		
		echo ${header} > "${syml}_${date_}.csv"

		cp "$kline_f" .
		unzip -q "${symbol}-1s-${date_}.zip"
			
		gawk -F ',' -v var=",${syml},1s," '{print strftime("%F %T",int($1/1000))var""substr($0,0,length($0)-2)}' "${symbol}-1s-${date_}.csv" >>"${syml}_${date_}.csv"
		gzip "${syml}_${date_}.csv"
		
		rm "${symbol}-1s-${date_}.zip"
		rm "${symbol}-1s-${date_}.csv"
	done

	cd ..
}

export -f process_symbol
export header

# 创建临时文件存储币种列表
symbols_list="BTC ETH BNB NEO LTC QTUM ADA XRP EOS TUSD IOTA XLM ONT TRX ETC ICX NULS VET USDC LINK ONG HOT ZIL ZRX FET BAT ZEC IOST CELR DASH THETA ENJ ATOM TFUEL ONE FTM ALGO DOGE DUSK ANKR WIN COS MTL DENT WAN FUN CVC CHZ BAND XTZ RVN HBAR NKN STX KAVA ARPA IOTX RLC CTXC BCH TROY VITE FTT EUR OGN WRX LSK BNT LTO MBL COTI STPT DATA SOL CTSI HIVE CHR ARDR MDT STMX KNC LRC COMP SC ZEN SNX VTHO DGB SXP MKR DCR STORJ MANA YFI BAL BLZ KMD JST CRV SAND NMR DOT LUNA RSR PAXG TRB SUSHI KSM EGLD DIA RUNE FIO UMA BEL WING UNI OXT SUN AVAX FLM UTK XVS ALPHA AAVE NEAR FIL INJ AUDIO CTK AKRO AXS HARD STRAX ROSE AVA SKL GRT JUV PSG 1INCH OG ATM ASR CELO RIF TRU CKB TWT FIRO LIT SFP DODO CAKE ACM BADGER FIS OM POND DEGO ALICE LINA PERP SUPER CFX TKO PUNDIX TLM BAR FORTH BAKE BURGER SLP SHIB ICP AR MASK LPT XVG ATA GTC ERN PHA MLN DEXE C98 CLV QNT FLOW MINA RAY FARM ALPACA QUICK MBOX REQ GHST WAXP GNO XEC ELF DYDX IDEX VIDT USDP GALA ILV YGG SYS DF FIDA AGLD RAD BETA RARE LAZIO CHESS ADX AUCTION DAR BNX MOVR CITY ENS QI PORTO POWR JASMY AMP PYR ALCX SANTOS BICO FLUX FXS VOXEL HIGH CVX PEOPLE SPELL JOE ACH IMX GLMR LOKA SCRT API3 BTTC ACA XNO WOO ALPINE T ASTR GMT KDA APE BSW BIFI STEEM NEXO REI LDO OP LEVER STG LUNC GMX POLYX APT OSMO HFT PHB HOOK MAGIC HIFI RPL PROS GNS SYN VIB SSV LQTY AMB USTC GAS GLM PROM QKC UFT ID ARB RDNT WBTC EDU SUI AERGO PEPE FLOKI AST SNT COMBO MAV PENDLE ARKM WBETH WLD FDUSD SEI CYBER ARK CREAM IQ NTRN TIA MEME ORDI BEAMX PIVX VIC BLUR VANRY AEUR JTO 1000SATS BONK ACE NFP AI XAI MANTA ALT JUP PYTH RONIN DYM PIXEL STRK PORTAL PDA AXL WIF METIS AEVO BOME ETHFI ENA W TNSR SAGA TAO OMNI REZ BB NOT IO ZK LISTA ZRO G BANANA RENDER TON DOGS EURI SLF POL NEIRO TURBO 1MBABYDOGE CATI HMSTR EIGEN SCR BNSOL LUMIA KAIA COW CETUS PNUT ACT USUAL THE ACX ORCA MOVE ME VELODROME"

# 使用GNU Parallel并行处理，设置8个并发任务
echo $symbols_list | tr ' ' '\n' | parallel -j 8 process_symbol

cd ..

