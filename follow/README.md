# Granville八大法则双均线策略

基于Granville八大法则的DOGE加密货币交易策略，使用Python backtesting库实现。

## 策略说明

### Granville八大法则

Granville八大法则是由美国投资专家Joseph Granville提出的移动平均线买卖法则，其中四条用于判断买进时机，四条用于判断卖出时机。

#### 买入法则

1. **买1（黄金交叉）**: 均线整体上行，股价由下至上上穿均线，此为黄金交叉，形成第一个买点。
2. **买2（均线支撑）**: 股价出现下跌迹象，但尚未跌破均线，此时均线变成支撑线，形成第二个买点。
3. **买3（回调买入）**: 股价仍处于均线上方，但呈现急剧下跌趋势。当跌破均线时，出现第三个买点。
4. **买4（严重偏离）**: 股价和均线都处于下降通道，且股价处于均线下方，严重远离均线，出现第四个买点。

#### 卖出法则

1. **卖1（死亡交叉）**: 均线由上升状态变为缓慢下降的状态，股价也开始下降。当股价跌破均线时，此为死亡交叉，形成第一个卖点。
2. **卖2（均线阻力）**: 股价仍处于均线之下，但股价开始呈现上涨趋势，当股价无限接近均线但尚未突破时，此时均线变成阻力线，形成第二个卖点。
3. **卖3（假突破）**: 股价终于突破均线，处于均线上方。但持续时间不长，股价开始下跌，直至再一次跌破均线，此为第三个卖点。
4. **卖4（严重偏离）**: 股价和均线都在上涨，股价上涨的速度远快于均线上涨的速度。当股价严重偏离均线时，出现第四个卖点。

## 文件结构

```
follow/
├── data_loader.py              # DOGE数据加载和预处理模块
├── granville_signals.py       # Granville八大法则信号识别模块
├── dual_ma_granville_strategy.py  # 双均线+Granville策略实现
├── main.py                     # 主程序，运行回测
├── requirements.txt            # 依赖包列表
└── README.md                   # 项目说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 运行完整回测

```bash
cd follow
python main.py
```

### 2. 单独测试数据加载

```bash
python data_loader.py
```

### 3. 单独测试信号生成

```bash
python granville_signals.py
```

### 4. 单独运行策略回测

```bash
python dual_ma_granville_strategy.py
```

## 回测设置

- **回测数据**: 2025年1月份DOGE/USDT 5分钟K线数据
- **初始资金**: 10,000 USDT
- **手续费**: 0.2%
- **均线周期**: 20期
- **数据来源**: `/Users/<USER>/workspace/data/doge/DOGE_2025-01.parquet`

## 策略实现

### 1. 简化版Granville策略 (SimpleGranvilleStrategy)

仅使用Granville八大法则进行买卖决策：
- 当触发任一买入信号时买入
- 当触发任一卖出信号时卖出

### 2. 双均线+Granville组合策略 (DualMAGranvilleStrategy)

结合双均线交叉和Granville法则：
- 买入条件：双均线金叉 AND Granville买入信号
- 卖出条件：双均线死叉 AND Granville卖出信号

## 输出结果

程序运行后会生成以下文件：

1. **granville_strategy_analysis.png**: 策略分析图，显示价格走势、均线和买卖信号点
2. **simple_granville_backtest.png**: 简化版Granville策略回测结果图
3. **dual_ma_granville_backtest.png**: 双均线+Granville组合策略回测结果图
4. **strategy_comparison.csv**: 两种策略的比较表格
5. **granville_signals_detail.csv**: 详细的信号数据，包含所有买卖信号的时间和价格

## 评估指标

- **总收益率**: 策略在回测期间的总收益百分比
- **最大回撤**: 策略的最大回撤百分比
- **胜率**: 盈利交易占总交易次数的比例
- **夏普比率**: 风险调整后的收益率指标
- **交易次数**: 回测期间的总交易次数

## 技术特点

1. **模块化设计**: 数据加载、信号生成和策略执行分离，便于维护和扩展
2. **参数可配置**: 支持调整均线周期、偏离阈值等参数
3. **详细信号分析**: 提供Granville八大法则的详细信号统计
4. **可视化输出**: 生成策略分析图和回测结果图
5. **多策略比较**: 同时测试两种不同的策略实现

## 注意事项

1. 本策略仅用于学习和研究目的，不构成投资建议
2. 回测结果不代表未来表现，实际交易需要考虑更多因素
3. 加密货币市场波动较大，请谨慎使用杠杆和风险管理
4. 建议在实盘交易前进行充分的纸上交易测试

## 扩展功能

可以考虑的改进方向：

1. 添加更多技术指标（RSI、MACD等）
2. 实现动态仓位管理
3. 加入止损止盈机制
4. 支持多时间周期分析
5. 实现实时交易接口

## 联系信息

如有问题或建议，请参考代码注释或相关文档。 