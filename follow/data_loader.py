"""
DOGE数据加载和预处理模块
用于处理DOGE原始1分钟K线数据并转换为5分钟K线数据
"""
import pandas as pd
import numpy as np
from datetime import datetime
import os

class DogeDataLoader:
    """DOGE数据加载器"""
    
    def __init__(self, data_dir: str = "/Users/<USER>/workspace/data/doge"):
        """
        初始化数据加载器
        
        Args:
            data_dir: DOGE数据目录路径
        """
        self.data_dir = data_dir
    
    def load_monthly_data(self, year: int, month: int, time_frame: str) -> pd.DataFrame:

        filename = f"DOGE_{year}-{month:02d}.parquet"
        filepath = os.path.join(self.data_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"数据文件不存在: {filepath}")
        
        df = pd.read_parquet(filepath)
        df = df[df['i'] == time_frame]
        return self._process_raw_data(df)
    
    def _process_raw_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理原始数据，转换为标准格式
        
        Args:
            df: 原始DataFrame
            
        Returns:
            DataFrame: 处理后的数据
        """
        # 转换时间戳
        df['datetime'] = pd.to_datetime(df['t'], unit='ms')
        
        # 重命名列为标准OHLCV格式
        df = df.rename(columns={
            'o': 'open',
            'h': 'high', 
            'l': 'low',
            'c': 'close',
            'v': 'volume'
        })
        
        # 设置时间索引
        df = df.set_index('datetime')
        
        # 选择需要的列
        df = df[['open', 'high', 'low', 'close', 'volume']]
        
        # 确保数据类型正确
        for col in ['open', 'high', 'low', 'close']:
            df[col] = df[col].astype(float)
        df['volume'] = df['volume'].astype(int)
        
        return df
    
    
    def load_data(self, time_frame: str, month: int) -> pd.DataFrame:
        df = self.load_monthly_data(2025, month, time_frame)
        
        print(f"成功加载2025年1月DOGE数据，共{len(df)}根{time_frame}K线")
        print(f"数据时间范围：{df.index.min()} 至 {df.index.max()}")
        
        return df
    
    def prepare_backtest_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        为回测准备数据，添加必要的技术指标列
        
        Args:
            df: 原始OHLCV数据
            
        Returns:
            DataFrame: 准备好的回测数据
        """
        # 确保数据按时间排序
        df = df.sort_index()
        
        # 添加均线列（这些会在策略中计算，这里先预留）
        df['ma_short'] = np.nan
        df['ma_long'] = np.nan
        
        return df

if __name__ == "__main__":
    # 测试数据加载
    loader = DogeDataLoader()
    df = loader.load_data('5m', 1)
    print(df.head())
    print(df.tail())
    print(df.info()) 