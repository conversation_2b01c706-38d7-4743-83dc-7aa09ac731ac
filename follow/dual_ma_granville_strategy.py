"""
基于Granville八大法则的双均线策略
使用backtesting库实现回测
"""
import pandas as pd
import numpy as np
from backtesting import Backtest, Strategy
from backtesting.lib import crossover
from granville_signals import GranvilleSignals

class DualMAGranvilleStrategy(Strategy):
    """基于Granville八大法则的双均线策略"""
    
    # 策略参数
    short_ma_period = 10  # 短期均线周期
    long_ma_period = 20   # 长期均线周期
    granville_ma_period = 20  # Granville法则使用的均线周期
    
    def init(self):
        """初始化策略"""
        # 计算双均线
        self.short_ma = self.I(lambda x: pd.Series(x).rolling(self.short_ma_period).mean(), 
                              self.data.Close, name='Short_MA')
        self.long_ma = self.I(lambda x: pd.Series(x).rolling(self.long_ma_period).mean(), 
                             self.data.Close, name='Long_MA')
        
        # 初始化Granville信号识别器
        self.granville = GranvilleSignals(ma_period=self.granville_ma_period)
        
        # 计算Granville均线
        self.granville_ma = self.I(lambda x: pd.Series(x).rolling(self.granville_ma_period).mean(), 
                                  self.data.Close, name='Granville_MA')
        
        # 生成Granville信号
        self._generate_granville_signals()
    
    def _generate_granville_signals(self):
        """生成Granville八大法则信号"""
        # 将数据转换为DataFrame格式
        df = pd.DataFrame({
            'close': self.data.Close,
            'open': self.data.Open,
            'high': self.data.High,
            'low': self.data.Low,
            'volume': self.data.Volume
        }, index=self.data.index)
        
        # 生成Granville信号
        df_with_signals = self.granville.generate_trading_signals(df)
        
        # 存储信号数据用于后续访问
        self.granville_signals = df_with_signals
    
    def next(self):
        """策略逻辑执行"""
        # 如果没有足够的数据，跳过
        if len(self.data) < max(self.short_ma_period, self.long_ma_period, self.granville_ma_period):
            return
        
        # 双均线交叉信号
        ma_golden_cross = crossover(self.short_ma, self.long_ma)  # 金叉
        ma_death_cross = crossover(self.long_ma, self.short_ma)  # 死叉（长均线上穿短均线）
        
        # Granville买卖信号
        current_idx = len(self.data) - 1
        if hasattr(self, 'granville_signals') and current_idx < len(self.granville_signals):
            granville_buy_signal = self.granville_signals.iloc[current_idx]['buy_signal']
            granville_sell_signal = self.granville_signals.iloc[current_idx]['sell_signal']
        else:
            granville_buy_signal = False
            granville_sell_signal = False
        
        # 综合买入条件：双均线金叉 + Granville买入信号
        buy_condition = ma_golden_cross and granville_buy_signal
        
        # 综合卖出条件：双均线死叉 + Granville卖出信号
        sell_condition = ma_death_cross and granville_sell_signal
        
        # 执行交易
        if buy_condition and not self.position:
            # 买入信号且当前无持仓
            self.buy(size=0.95)  # 使用95%的资金买入
            
        elif sell_condition and self.position:
            # 卖出信号且当前有持仓
            self.position.close()  # 全部卖出

class SimpleGranvilleStrategy(Strategy):
    """简化版Granville策略（仅使用Granville信号）"""
    
    # 策略参数
    ma_period = 20  # 均线周期
    
    def init(self):
        """初始化策略"""
        # 初始化Granville信号识别器
        self.granville = GranvilleSignals(ma_period=self.ma_period)
        
        # 计算均线
        self.ma = self.I(lambda x: pd.Series(x).rolling(self.ma_period).mean(), 
                        self.data.Close, name='MA')
        
        # 生成Granville信号
        self._generate_granville_signals()
    
    def _generate_granville_signals(self):
        """生成Granville八大法则信号"""
        # 将数据转换为DataFrame格式
        df = pd.DataFrame({
            'close': self.data.Close,
            'open': self.data.Open,
            'high': self.data.High,
            'low': self.data.Low,
            'volume': self.data.Volume
        }, index=self.data.index)
        
        # 生成Granville信号
        df_with_signals = self.granville.generate_trading_signals(df)
        
        # 存储信号数据用于后续访问
        self.granville_signals = df_with_signals
    
    def next(self):
        """策略逻辑执行"""
        # 如果没有足够的数据，跳过
        if len(self.data) < self.ma_period:
            return
        
        # Granville买卖信号
        current_idx = len(self.data) - 1
        if hasattr(self, 'granville_signals') and current_idx < len(self.granville_signals):
            granville_buy_signal = self.granville_signals.iloc[current_idx]['buy_signal']
            granville_sell_signal = self.granville_signals.iloc[current_idx]['sell_signal']
        else:
            granville_buy_signal = False
            granville_sell_signal = False
        
        # 执行交易
        if granville_buy_signal and not self.position:
            # 买入信号且当前无持仓
            self.buy(size=0.95)  # 使用95%的资金买入
            
        elif granville_sell_signal and self.position:
            # 卖出信号且当前有持仓
            self.position.close()  # 全部卖出

def run_backtest(data: pd.DataFrame, strategy_class=SimpleGranvilleStrategy, **kwargs):
    """
    运行回测
    
    Args:
        data: OHLCV数据
        strategy_class: 策略类
        **kwargs: 传递给Backtest的其他参数
        
    Returns:
        回测结果
    """
    # 确保数据格式正确
    data = data.copy()
    data.columns = [col.title() for col in data.columns]  # 转换为大写首字母
    
    # 默认回测参数
    default_params = {
        'cash': 10000,  # 初始资金
        'commission': 0.002,  # 手续费0.2%
        'exclusive_orders': True,  # 排他性订单
    }
    default_params.update(kwargs)
    
    # 创建回测实例
    bt = Backtest(data, strategy_class, **default_params)
    
    # 运行回测
    results = bt.run()
    
    return bt, results

if __name__ == "__main__":
    # 测试策略
    from data_loader import DogeDataLoader
    
    # 加载数据
    loader = DogeDataLoader()
    df = loader.load_january_2025_5min()
    
    print("开始回测...")
    print(f"数据范围：{df.index.min()} 至 {df.index.max()}")
    print(f"数据条数：{len(df)}")
    
    # 运行简化版Granville策略回测
    bt, results = run_backtest(df, SimpleGranvilleStrategy)
    
    print("\n=== 回测结果 ===")
    print(results)
    
    # 运行双均线+Granville策略回测
    print("\n运行双均线+Granville策略回测...")
    bt2, results2 = run_backtest(df, DualMAGranvilleStrategy)
    
    print("\n=== 双均线+Granville策略回测结果 ===")
    print(results2) 