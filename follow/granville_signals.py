"""
Granville八大法则信号识别模块
实现价格与移动平均线相互关系的买卖信号识别
"""
import pandas as pd
import numpy as np
from typing import Tuple, Dict

class GranvilleSignals:
    """Granville八大法则信号识别器"""
    
    def __init__(self, ma_period: int = 20):
        """
        初始化Granville信号识别器
        
        Args:
            ma_period: 移动平均线周期，默认20
        """
        self.ma_period = ma_period
    
    def calculate_ma(self, prices: pd.Series) -> pd.Series:
        """
        计算移动平均线
        
        Args:
            prices: 价格序列
            
        Returns:
            Series: 移动平均线序列
        """
        return prices.rolling(window=self.ma_period).mean()
    
    def identify_buy_signals(self, prices: pd.Series, ma: pd.Series) -> Dict[str, pd.Series]:
        """
        识别Granville四个买入信号
        
        Args:
            prices: 价格序列
            ma: 移动平均线序列
            
        Returns:
            Dict: 包含四个买入信号的字典
        """
        signals = {}
        
        # 买1：均线整体上行，股价由下至上上穿均线（黄金交叉）
        ma_rising = ma > ma.shift(1)
        price_cross_above = (prices > ma) & (prices.shift(1) <= ma.shift(1))
        signals['buy1'] = ma_rising & price_cross_above
        
        # 买2：股价出现下跌迹象但尚未跌破均线（均线支撑）
        price_declining = prices < prices.shift(1)
        price_above_ma = prices > ma
        price_near_ma = (prices - ma) / ma < 0.02  # 接近均线（2%以内）
        signals['buy2'] = price_declining & price_above_ma & price_near_ma
        
        # 买3：股价仍处于均线上方，但呈现急剧下跌趋势，当跌破均线时
        price_above_ma_prev = prices.shift(1) > ma.shift(1)
        rapid_decline = (prices - prices.shift(2)) / prices.shift(2) < -0.03  # 快速下跌3%
        price_cross_below_then_above = (prices.shift(1) < ma.shift(1)) & (prices > ma)
        signals['buy3'] = price_above_ma_prev & rapid_decline & price_cross_below_then_above
        
        # 买4：股价和均线都处于下降通道，且股价严重远离均线
        ma_declining = ma < ma.shift(1)
        price_below_ma = prices < ma
        severely_below = (ma - prices) / ma > 0.05  # 严重偏离5%以上
        signals['buy4'] = ma_declining & price_below_ma & severely_below
        
        return signals
    
    def identify_sell_signals(self, prices: pd.Series, ma: pd.Series) -> Dict[str, pd.Series]:
        """
        识别Granville四个卖出信号
        
        Args:
            prices: 价格序列
            ma: 移动平均线序列
            
        Returns:
            Dict: 包含四个卖出信号的字典
        """
        signals = {}
        
        # 卖1：均线由上升状态变为缓慢下降，股价跌破均线（死亡交叉）
        ma_turning_down = (ma.shift(1) > ma.shift(2)) & (ma < ma.shift(1))
        price_cross_below = (prices < ma) & (prices.shift(1) >= ma.shift(1))
        signals['sell1'] = ma_turning_down & price_cross_below
        
        # 卖2：股价仍处于均线之下，但开始上涨接近均线（均线阻力）
        price_below_ma = prices < ma
        price_rising = prices > prices.shift(1)
        price_near_ma = (ma - prices) / ma < 0.02  # 接近均线（2%以内）
        signals['sell2'] = price_below_ma & price_rising & price_near_ma
        
        # 卖3：股价突破均线处于上方，但持续时间不长，再次跌破均线
        price_above_ma_prev = prices.shift(1) > ma.shift(1)
        short_breakout = (prices.shift(1) > ma.shift(1)) & (prices.shift(2) <= ma.shift(2))
        price_cross_below_again = (prices < ma) & (prices.shift(1) >= ma.shift(1))
        signals['sell3'] = price_above_ma_prev & short_breakout & price_cross_below_again
        
        # 卖4：股价和均线都在上涨，股价严重偏离均线
        ma_rising = ma > ma.shift(1)
        price_above_ma = prices > ma
        severely_above = (prices - ma) / ma > 0.05  # 严重偏离5%以上
        signals['sell4'] = ma_rising & price_above_ma & severely_above
        
        return signals
    
    def generate_trading_signals(self, df: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        生成完整的交易信号
        
        Args:
            df: 包含价格数据的DataFrame
            price_col: 价格列名，默认'close'
            
        Returns:
            DataFrame: 包含所有信号的DataFrame
        """
        # 复制数据以避免修改原始数据
        result = df.copy()
        
        # 计算移动平均线
        prices = df[price_col]
        ma = self.calculate_ma(prices)
        result['ma'] = ma
        
        # 识别买入信号
        buy_signals = self.identify_buy_signals(prices, ma)
        for signal_name, signal_series in buy_signals.items():
            result[signal_name] = signal_series
        
        # 识别卖出信号
        sell_signals = self.identify_sell_signals(prices, ma)
        for signal_name, signal_series in sell_signals.items():
            result[signal_name] = signal_series
        
        # 综合买卖信号
        result['buy_signal'] = (
            result['buy1'] | result['buy2'] | result['buy3'] | result['buy4']
        )
        result['sell_signal'] = (
            result['sell1'] | result['sell2'] | result['sell3'] | result['sell4']
        )
        
        # 创建交易信号列：1=买入，-1=卖出，0=无信号
        result['signal'] = 0
        result.loc[result['buy_signal'], 'signal'] = 1
        result.loc[result['sell_signal'], 'signal'] = -1
        
        return result
    
    def get_signal_summary(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        获取信号统计摘要
        
        Args:
            df: 包含信号的DataFrame
            
        Returns:
            Dict: 信号统计摘要
        """
        summary = {}
        
        # 买入信号统计
        for i in range(1, 5):
            signal_name = f'buy{i}'
            if signal_name in df.columns:
                summary[f'买入信号{i}次数'] = df[signal_name].sum()
        
        # 卖出信号统计
        for i in range(1, 5):
            signal_name = f'sell{i}'
            if signal_name in df.columns:
                summary[f'卖出信号{i}次数'] = df[signal_name].sum()
        
        # 总信号统计
        if 'buy_signal' in df.columns:
            summary['总买入信号次数'] = df['buy_signal'].sum()
        if 'sell_signal' in df.columns:
            summary['总卖出信号次数'] = df['sell_signal'].sum()
        
        return summary

if __name__ == "__main__":
    # 测试Granville信号
    from data_loader import DogeDataLoader
    
    # 加载数据
    loader = DogeDataLoader()
    df = loader.load_january_2025_5min()
    
    # 生成Granville信号
    granville = GranvilleSignals(ma_period=20)
    df_with_signals = granville.generate_trading_signals(df)
    
    # 打印信号统计
    summary = granville.get_signal_summary(df_with_signals)
    print("Granville八大法则信号统计：")
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    # 显示有信号的日期
    signal_dates = df_with_signals[df_with_signals['signal'] != 0].index
    print(f"\n共有{len(signal_dates)}个交易信号")
    print("前10个信号时间：")
    for date in signal_dates[:10]:
        signal_type = "买入" if df_with_signals.loc[date, 'signal'] == 1 else "卖出"
        print(f"{date}: {signal_type}") 