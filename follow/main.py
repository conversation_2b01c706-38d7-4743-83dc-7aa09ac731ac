"""
Granville八大法则双均线策略主程序
运行回测并生成详细的分析报告
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_loader import DogeDataLoader
from granville_signals import GranvilleSignals
from dual_ma_granville_strategy import SimpleGranvilleStrategy, DualMAGranvilleStrategy, run_backtest

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_signals(df_with_signals):
    """分析Granville信号"""
    granville = GranvilleSignals()
    summary = granville.get_signal_summary(df_with_signals)
    
    print("=== Granville八大法则信号分析 ===")
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    # 分析信号分布
    signal_dates = df_with_signals[df_with_signals['signal'] != 0].copy()
    if len(signal_dates) > 0:
        print(f"\n信号分布：")
        print(f"总交易信号数量: {len(signal_dates)}")
        print(f"买入信号数量: {len(signal_dates[signal_dates['signal'] == 1])}")
        print(f"卖出信号数量: {len(signal_dates[signal_dates['signal'] == -1])}")
        
        print(f"\n前10个交易信号:")
        for i, (date, row) in enumerate(signal_dates.head(10).iterrows()):
            signal_type = "买入" if row['signal'] == 1 else "卖出"
            price = row['close']
            print(f"{i+1}. {date.strftime('%Y-%m-%d %H:%M')} - {signal_type} (价格: {price:.6f})")
    
    return summary

def plot_strategy_results(df, df_with_signals, title="策略分析图"):
    """绘制策略分析图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 上图：价格、均线和信号
    ax1.plot(df.index, df['close'], label='DOGE价格', linewidth=1, alpha=0.8)
    ax1.plot(df_with_signals.index, df_with_signals['ma'], label='20日均线', linewidth=2, alpha=0.7)
    
    # 标记买卖信号
    buy_signals = df_with_signals[df_with_signals['signal'] == 1]
    sell_signals = df_with_signals[df_with_signals['signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], 
                   color='red', marker='^', s=100, label='买入信号', zorder=5)
    
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], 
                   color='green', marker='v', s=100, label='卖出信号', zorder=5)
    
    ax1.set_title(f'{title} - 价格走势与交易信号')
    ax1.set_ylabel('价格 (USDT)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 下图：成交量
    ax2.bar(df.index, df['volume'], alpha=0.6, color='lightblue', label='成交量')
    ax2.set_title('成交量')
    ax2.set_ylabel('成交量')
    ax2.set_xlabel('时间')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 格式化日期轴
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    return fig

def plot_backtest_results(bt, title="回测结果"):
    """绘制回测结果图"""
    try:
        fig = bt.plot(title=title)
        return fig
    except Exception as e:
        print(f"绘制回测图表时出错: {e}")
        return None

def generate_detailed_report(results, df, strategy_name="策略"):
    """生成详细的回测报告"""
    print(f"\n=== {strategy_name}详细回测报告 ===")
    
    # 基本统计信息
    print(f"回测期间: {df.index.min().strftime('%Y-%m-%d')} 至 {df.index.max().strftime('%Y-%m-%d')}")
    print(f"回测天数: {(df.index.max() - df.index.min()).days}天")
    print(f"数据点数: {len(df)}个5分钟K线")
    
    print(f"\n--- 收益指标 ---")
    print(f"总收益率: {results['Return [%]']:.2f}%")
    print(f"年化收益率: {results.get('Annual Return [%]', 'N/A')}")
    print(f"买入持有收益率: {results['Buy & Hold Return [%]']:.2f}%")
    print(f"最大回撤: {results['Max. Drawdown [%]']:.2f}%")
    
    print(f"\n--- 交易统计 ---")
    print(f"总交易次数: {results['# Trades']}")
    print(f"胜率: {results['Win Rate [%]']:.2f}%")
    print(f"最好交易: {results['Best Trade [%]']:.2f}%")
    print(f"最差交易: {results['Worst Trade [%]']:.2f}%")
    print(f"平均交易收益: {results['Avg. Trade [%]']:.2f}%")
    
    print(f"\n--- 风险指标 ---")
    print(f"夏普比率: {results.get('Sharpe Ratio', 'N/A')}")
    print(f"Sortino比率: {results.get('Sortino Ratio', 'N/A')}")
    print(f"Calmar比率: {results.get('Calmar Ratio', 'N/A')}")
    
    return results

def main():
    """主函数"""
    print("=== Granville八大法则双均线策略回测系统 ===")
    print("开始加载DOGE 2025年1月数据...")
    
    try:
        # 1. 加载数据
        loader = DogeDataLoader()
        time_frame = '5m'
        month = 1
        df = loader.load_data(time_frame, month)
        
        print(f"数据加载成功!")
        print(f"数据时间范围: {df.index.min()} 至 {df.index.max()}")
        print(f"数据条数: {len(df)}根5分钟K线")
        print(f"价格范围: {df['close'].min():.6f} - {df['close'].max():.6f} USDT")
        
        # 2. 生成Granville信号
        print("\n生成Granville八大法则信号...")
        granville = GranvilleSignals(ma_period=20)
        df_with_signals = granville.generate_trading_signals(df)
        
        # 3. 分析信号
        signal_summary = analyze_signals(df_with_signals)
        
        # 4. 运行策略回测
        print("\n=== 运行简化版Granville策略回测 ===")
        bt1, results1 = run_backtest(df, SimpleGranvilleStrategy, cash=10000, commission=0.002)
        generate_detailed_report(results1, df, "简化版Granville策略")
        
        print("\n=== 运行双均线+Granville组合策略回测 ===")
        bt2, results2 = run_backtest(df, DualMAGranvilleStrategy, cash=10000, commission=0.002)
        generate_detailed_report(results2, df, "双均线+Granville组合策略")
        
        # 5. 生成可视化图表
        print("\n生成可视化图表...")
        
        # 策略分析图
        fig1 = plot_strategy_results(df, df_with_signals, "Granville八大法则策略分析")
        fig1.savefig('granville_strategy_analysis.png', dpi=300, bbox_inches='tight')
        plt.close(fig1)  # 关闭图形而不显示
        
        # 回测结果图
        try:
            print("\n显示简化版Granville策略回测图...")
            fig2 = plot_backtest_results(bt1, "简化版Granville策略回测结果")
            if fig2:
                fig2.savefig('simple_granville_backtest.png', dpi=300, bbox_inches='tight')
            
            print("\n显示双均线+Granville组合策略回测图...")
            fig3 = plot_backtest_results(bt2, "双均线+Granville组合策略回测结果")
            if fig3:
                fig3.savefig('dual_ma_granville_backtest.png', dpi=300, bbox_inches='tight')
        except Exception as e:
            print(f"生成回测图表时出错: {e}")
        
        # 6. 策略比较
        print("\n=== 策略比较 ===")
        comparison = pd.DataFrame({
            '简化版Granville策略': [
                f"{results1['Return [%]']:.2f}%",
                f"{results1['Max. Drawdown [%]']:.2f}%",
                f"{results1['Win Rate [%]']:.2f}%",
                results1['# Trades'],
                f"{results1['Sharpe Ratio']:.2f}" if pd.notna(results1.get('Sharpe Ratio')) else 'N/A'
            ],
            '双均线+Granville组合策略': [
                f"{results2['Return [%]']:.2f}%",
                f"{results2['Max. Drawdown [%]']:.2f}%",
                f"{results2['Win Rate [%]']:.2f}%",
                results2['# Trades'],
                f"{results2['Sharpe Ratio']:.2f}" if pd.notna(results2.get('Sharpe Ratio')) else 'N/A'
            ]
        }, index=['总收益率', '最大回撤', '胜率', '交易次数', '夏普比率'])
        
        print(comparison)
        
        # 保存结果到文件
        print("\n保存结果到文件...")
        comparison.to_csv('strategy_comparison.csv', encoding='utf-8-sig')
        
        # 保存详细信号数据
        df_with_signals.to_csv('granville_signals_detail.csv', encoding='utf-8-sig')
        
        print("\n=== 回测完成 ===")
        print("结果文件已保存到follow目录:")
        print("- granville_strategy_analysis.png: 策略分析图")
        print("- simple_granville_backtest.png: 简化版策略回测图")
        print("- dual_ma_granville_backtest.png: 组合策略回测图")
        print("- strategy_comparison.csv: 策略比较表")
        print("- granville_signals_detail.csv: 详细信号数据")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 