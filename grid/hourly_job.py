# -*- coding: utf-8 -*
import time, json
import redis
import pandas as pd
import numpy as np
import math
from util import *

import sys
sys.path.append("/root/workspace/trade/public")
from constant import *

from FuturesApi import FuturesApi

from binance.client import Client
from binance.um_futures import UMFutures
from binance.cm_futures import CMFutures

r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
fapi = FuturesApi(api_key, api_secret)

um_futures_client = UMFutures(key=api_key, secret=api_secret)


# 获取U本位合约所有持仓
def get_usdt_positions():
    try:
        positions = fapi.get_positionRisk()
        # 只显示持仓量不为0的仓位
        active_positions = [p for p in positions if float(p['positionAmt']) != 0]
        infos = ['持仓通知：']
        infos.append("------------------------")
        for position in active_positions:
            infos.append(f"Symbol: {position['symbol']}")
            infos.append(f"Position Amount: {position['positionAmt']}")
            infos.append(f"Entry Price: {position['entryPrice']}")
            infos.append(f"Unrealized PNL: {position['unRealizedProfit']}")
            infos.append(f"Leverage: {position['leverage']}")
            infos.append("------------------------")
        local_time = time.localtime(time.time())
        tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        infos.append(f"通知时间：{tstr}")
    except Exception as e:
        print(f"Error: {e}")
    if len(infos) > 3:
        weixin_info('\n'.join(infos), account_token)


    
def account_info():
    info = fapi.get_account()
    try:
        balance = float(info['totalWalletBalance'])
    except Exception as e:
        print(e)
        time.sleep(30)
        info = fapi.get_account()
    balance = float(info['totalWalletBalance'])
    profit = round(float(info['totalUnrealizedProfit']), 2)
    balance = int(balance + profit)

    notional = 1e-8
    ntl_ = 0
    num = 0
    positions = info['positions']
    positions = [p for p in positions if float(p['positionAmt']) != 0]
    # r.set('trph_positions', json.dumps(positions))
    r.set(group + '_positions', json.dumps(positions))
    
    group_positions = {}
    syml_info = []
    for p in positions:
        symbol = p['symbol'][0:-4]
        ntl = float(p['notional'])
        group_positions[p['symbol']] = ntl
        notional += abs(ntl)
        ntl_ += ntl
        if ntl != 0:
            num += 1
    lever = round(notional / balance, 2)
    

    local_time = time.localtime(time.time())
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)

    balance_base = 10000
    diff = balance - balance_base
    ratio = round(diff / balance_base * 100, 2)

    text = ['投资账号通知：']
    text.append(f'投资总额：{balance_base}')
    text.append(f'当前余额：{balance}')
    text.append(f'变化金额：{diff}')
    text.append(f'变化幅度：{ratio}%')
    text.append(f'持仓数量：{num}')
    text.append(f'持仓总额：{int(notional)}')
    text.append(f'杠杆比例：{lever}')
    text.append(f'通知时间：{tstr}')
    weixin_info('\n'.join(text), account_token)



def main():
    get_usdt_positions()
    account_info()
    #set_lever()


if __name__ == '__main__':
    main()