# -*- coding: utf-8 -*
import json, logging
import redis
import time
import pandas as pd
import numpy as np
from kafka import KafkaProducer, KafkaConsumer
from util import *
import traceback

import sys
sys.path.append("../public")
from FuturesApi import FuturesApi

logger = logging.getLogger("SmallGoal-TradeGrid")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.trade')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class TradeGrid():
    def __init__(self):
        self.fapi = FuturesApi(api_key, api_secret)
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.position = None
        self.symbol = None
        self.trade_price = None
        self.trade_volume = None
    
    def set_exchange_info(self):
        exchange_info = self.fapi.get_exchangeInfo()
        symbols = exchange_info['symbols']
        for info in symbols:
            symbol = info['symbol']
            self.redis.set(symbol + '_info', json.dumps(info))
    
    def get_current_pa(self, symbol):
        p = self.redis.get(group + '_' + symbol + '_p')
        if p:
            return json.loads(p)['pa']
        else:
            return 0.0

    def adjust_price_volume(self, symbol, volume, price):
        info = json.loads(self.redis.get(symbol + '_info'))
        filters = info['filters']
        notional = 5
        tick_size = 0.0
        for flt in filters:
            if flt['filterType'] == 'MARKET_LOT_SIZE':
                step_size = float(flt['stepSize'])
                min_qty = float(flt['minQty'])
            if flt['filterType'] == 'MIN_NOTIONAL':
                notional = float(flt['notional'])
            if flt['filterType'] == 'PRICE_FILTER':
                tick_size = float(flt['tickSize'])

        price = int(price / tick_size) * tick_size

        volume = int(volume / step_size) * step_size
        if abs(volume) < min_qty or price * abs(volume) < notional * 1.2:
            return price, 0
        return price, volume

    def gererate_orderId(self, strategy, symbol):
        t = int(time.time()*1000) / 1000
        local_time = time.localtime(time.time())
        tstr = time.strftime("%y%m%d_%H-%M-%S", local_time) + '_' + str(t).split('.')[1]
        return f'{strategy}_{symbol[:-4]}_{tstr}'


    def make_deal(self, symbol, orders):
        self.fapi.delete_allOpenOrders(symbol)  
        for order in orders:
            volume = order['volume']
            side = order['side']
            price = order['price']
            price, volume = self.adjust_price_volume(symbol, volume, price)
            orderId = self.gererate_orderId(group, symbol)
            # print(newClientOrderId)
            if volume == 0 or price == 0 or volume * price < 50:
                weixin_info(f'{symbol} volume: {volume}, price: {price} {json.dumps(order)} is invalid!', warning_token)
                continue
            if side == 'buy_limit':
                msg = self.fapi.buy_limit(symbol, volume, price, orderId)
            elif side == 'sell_limit':
                msg = self.fapi.sell_limit(symbol, volume, price, orderId)
            elif side == 'buy_market':
                msg = self.fapi.buy_market(symbol, volume, orderId)
            elif side == 'sell_market':
                msg = self.fapi.sell_market(symbol, volume, orderId)

            logger.info(json.dumps(msg))
            
            if 'code' in msg:
                weixin_info(f'{symbol} {json.dumps(order)} {json.dumps(msg)}', warning_token)
                if msg['code'] == -4400:
                    status = self.fapi.get_apiTradingStatus(symbol)
                    weixin_info(f'{symbol} {json.dumps(status)}', warning_token)
                    return
            
     
    def run(self):
        group_id = group
        logger.info('group_id:' + group_id)
        consumer = KafkaConsumer(bootstrap_servers= ['localhost:9092'],
                                group_id= group_id,
                                auto_offset_reset='latest',
                                key_deserializer= bytes.decode, 
                                value_deserializer= bytes.decode)
        consumer.subscribe(topics= ['trade'])
        # key: grid
        for msg in consumer:
            topic = msg.topic
            key = msg.key
            if key != 'grid':
                continue
            tgap = round(time.time() - msg.timestamp/1000, 1)
            if tgap > 60 * 5:
                info = 'TradeGrid {key} delay {diff} seconds: {msg}'.format(key=key, diff=tgap, msg=msg)
                logger.info(info)
                weixin_info(info, warning_token)
                continue
            value = json.loads(msg.value)
            
            if key == 'grid':
                logger.info(json.dumps(value))
                for x in value:
                    symbol = x['symbol']
                    orders = x['orders']
                    self.make_deal(symbol, orders)
                
            consumer.commit_async()


def main():
    msg = '### 开始交易 ###\nBelieve in Grid, Believe in Money!'
    while True:
        try:
            weixin_info(msg, weixin_token)
            trade = TradeGrid()
            trade.set_exchange_info()
            trade.run()
        except Exception as e:
            text = str(e) +  type(e).__name__ + ' restart! please check it!'
            weixin_info(text, weixin_token)
            traceback.print_exc()
    
if __name__ == "__main__":
    main()


