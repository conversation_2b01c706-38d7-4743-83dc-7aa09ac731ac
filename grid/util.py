# -*- coding: utf-8 -*

import requests, json
import time
import base64
from hashlib import md5

group = 'grid'

api_key = 'cTGOn7m5s9Tf9XrJ42z2F1qT0nmemGqCFvYg8SXH2Fss897gfUbVCbGpZEbq1iJU'
api_secret = 'hUVyptPS8awjlOzWZPr8mPlymifix05IuVUPEmQGn5HPmthf0YNGZ3sGE1o3ZEYc'

api_key = 'ynR0rmsqOzWYPIwGm5F0tK575YYlGfQTYyff5FUay3q8W6qjjOToee4Sb3gE3be7'
api_secret = '7Og5pRcumpB1Fqbs95LbsA8YOPHzQsKTmxTesMW2iQ3EbbKnaKG8gbLAmcZn0udg'

api_key = 'oAE9qbXCUR8VZ1MDTrfIaVFpb4wkGPuCxuVoOTCntxtYsQ3fcCPhceYmv3BK399O'
api_secret = 'n2ZjasCsO4kaVyCOEXGDeaJwolM1lCP2p6AtJ1Bor1FuOd3TuohlCBqq4h9IoKQ3'


api_key = 'rnUSzbanaTtXzOh9iUyVXrWHIzfKS7GjB02nza6OQVnt9CZAuemQNFyA3r6Ts4TB'
api_secret = 'oLn5vNBJgTXcfsaTUiGT1OdrqypmeRwagDKY8YjJUEN6nkKTu7PuaZhjLLhvF1vI'

redis_pw = 'peng1234'
recv_window = 3000

dingding_token = "1686c8a5b15855a8600291046926743655958a8e9b300da041bb6f9ccfaa7122"
test_token = '670e4e47ac2b4bd1c096d1701d69cdbf8be4a3155c547c3da0322f3661bdfd88'
ding_token = '4ebae08e14d04f45c71e3a678108ef6b44fff9eee0f3abc356aea55501ce17f4'

weixin_token = '6f01ebb2-e61c-44b9-8183-1d4472b311b0' 
warning_token='34c0705a-9d81-41fc-a56f-f19115174ee8'  #报警通知
order_merge_token = 'fa7c8118-48b9-4534-b81f-a755ec2f04f6'  # 订单汇总
lever_token = '29411a35-116f-4082-a75b-c99c96420c2b'
profit_token = '751db621-3a80-49d2-8ff1-0cc4618eb92b'  # 盈亏通知
balance_token = 'ce5cef3d-2bf6-4f74-bc55-11fb9d425eea' # 余额通知


order_token = 'a8329a7e-75e3-4b15-a481-839fb38e6978'  # 订单详情
# dingding_token = test_token
account_token = 'f14d4ed3-8dd5-4d50-a2c6-0a6d07d533dd' # 账号通知


def _msg(text):
    json_text = {
        "msgtype": "text",
        "at": {
            "atMobiles": ["11111"],
            "isAtAll": False
        },
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_msg(text):
    json_text = {
        "msgtype": "text",
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_img(base64_data, md5_data):
    json_text = {
        "msgtype": "image",
        "image": {
            "base64": str(base64_data,'utf-8'),
            "md5": md5_data
        }
    }
    return json_text

def dingding_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = "https://oapi.dingtalk.com/robot/send?access_token=%s" % token
    json_text = _msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_img(path, token):
    hash = md5()
    img = open(path, 'rb')
    data = img.read()
    hash.update(data)
    base64_data = base64.b64encode(data)
    md5_data = hash.hexdigest()
    img.close()
    
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_img(base64_data, md5_data)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def ts2date(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%Y-%m-%d", local_time)
    return z

def ts2idx(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%H%M", local_time)
    return int(z)

def check_klines(klines):
        for i in range(len(klines)-1):
            if klines[i]['t'] - klines[i+1]['t'] != 60 * 1000:
                print(klines[i], klines[i+1])
                return False
        return True


