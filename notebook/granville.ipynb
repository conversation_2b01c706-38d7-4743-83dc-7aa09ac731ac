from backtesting import Backtest, Strategy
from backtesting.lib import crossover
from backtesting.test import SMA, GOOG
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

def load_data(i, head=100000, tail=100000):
    path = '/Users/<USER>/workspace/data/doge/DOGE_2025-01.parquet'
    df = pd.read_parquet(path)
    df = df[df['i'] == i]
    df = df.head(head).tail(tail)
    df['datetime'] = pd.to_datetime(df['z'])
    df = df.set_index('datetime')
    df = df[['o', 'h', 'l', 'c', 'v']]
    # 重命名列
    df = df.rename(columns={
        'o': 'Open',
        'h': 'High',
        'l': 'Low',
        'c': 'Close',
        'v': 'Volume'
    })
    return df

def EMA(data, period):
    return pd.Series(data).ewm(span=period, adjust=False).mean()

class GranvilleStrategy(Strategy):
    n1 = 10
    n2 = 30
    def init(self):
        self.sma_fast = self.I(EMA, self.data.Close, self.n1)
        self.sma_slow = self.I(EMA, self.data.Close, self.n2)

    def next(self):
        if crossover(self.sma_fast, self.sma_slow):
            self.position.close()
            self.buy()
        elif crossover(self.sma_slow, self.sma_fast):
            self.position.close()
            # self.sell()

i = '15m'
head = 200
df = load_data(i, head)

i = '30m'
head = 200000
tail = 200000
df = load_data(i, head, tail)

bt = Backtest(df, GranvilleStrategy, cash=10_000, commission=.002)
stats = bt.run()
bt.plot()

stats['_trades']

%%time
stats = bt.optimize(n1=range(5, 20, 2),
                    n2=range(10, 40, 2),
                    maximize='Equity Final [$]',
                    constraint=lambda param: param.n1 < param.n2)
stats