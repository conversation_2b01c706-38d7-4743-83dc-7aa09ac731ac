{"cells": [{"cell_type": "code", "execution_count": 14, "id": "47705bfb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100.0\n"]}, {"data": {"text/plain": ["1618.4772622801602"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["start = 0.1\n", "end = 0.3\n", "lever = 10\n", "num = 100\n", "\n", "record_u = 1000 * lever / num\n", "a = 1.0106\n", "# 20%\n", "print(record_u)\n", "record_u * ((a - a**51)/(1-a) - 50)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "82700d24", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.21065166922796327"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["1.0106 ** 20 * 0.1706"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}