import pandas as pd
import numpy as np
import glob
import math
import time
from concurrent.futures import ThreadPoolExecutor
import os
import sys
sys.path.insert(0, '../public')
from jupyter import *
from pandarallel import pandarallel
pandarallel.initialize()

df = pd.read_csv('/Users/<USER>/Downloads/DOGE/DOGE_2025-05.csv')
df = df[df['i']=='1m']
df = df[['t','o','h','l','c','v']]
# rename columns
df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
df['datetime'] = pd.to_datetime(df['datetime'], unit='ms')
df.set_index('datetime', inplace=True)
df.to_csv('/Users/<USER>/workspace/bitquant/trade/grid_bt/data/DOGEUSDT_1m.csv')

def ts2date(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%Y-%m-%d", local_time)
    return z

def ts2idx(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%H%M", local_time)
    return int(z)

def df_to_hytstyle(df):
    names = {'s':'symbol', 'o':'open', 'h':'high','l':'low','c':'close', 'v':'volume'}
    df.rename(columns=names, inplace = True)
    df['date'] = df['T'].apply(lambda x: ts2date(x))
    df['idx'] = df['T'].apply(lambda x: ts2idx(x))
    # df['symbol'] = df['symbol'].apply(lambda x: x[0:-4])
    df['date'] = pd.to_datetime(df['date'])
    return df
    
def get_date(df, day_date):
    df = df[df['date'] == day_date]
    df = df[['date','idx','symbol','open','high','low','close','volume','q','n','V','Q']]
    df = df.set_index(['date','idx','symbol'])
    df.sort_index(inplace=True)
    return df

df = pd.read_csv('/Users/<USER>/Downloads/eth/ETH_2024-12.csv.gz')

def process_file(file):
    try:
        df = pd.read_csv(file)
        # 创建一个布尔掩码标记连续3行及以上q为0的位置
        mask = df['q'].rolling(window=3).sum() == 0
        # 将连续3行及以上q为0的行过滤掉
        df = df[~mask]
        if len(df) <= 100:
            return
        output_file = file.replace('.csv', '.parquet')
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        df.to_parquet(output_file, index=False)
    except Exception as e:
        print(f"处理文件 {file} 时出错: {str(e)}")
   

files = glob.glob('/Volumes/symbol/futures/kline/*/*2024-12.csv')
# files = glob.glob('/Volumes/symbol/spot/kline/*/*.csv')
len(files)

# 使用线程池并发处理文件
with ThreadPoolExecutor(max_workers=8) as executor:
    executor.map(process_file, files) 

# 删除所有csv文件
for file in files:
    try:
        os.remove(file)
    except Exception as e:
        print(f"删除文件 {file} 时出错: {str(e)}")



files = glob.glob('/Volumes/symbol/spot/kline/*/*.parquet')
# files = glob.glob('/Volumes/symbol/futures/kline/*/*.parquet')
files = [file for file in files if file.split('_')[1] >= '2023-12']
files = sorted(files)
dfs = [pd.read_parquet(f) for f in files]
df = pd.concat(dfs)
df = df[df['i'] == '1m']
df = df_to_hytstyle(df)
df.to_parquet('/Users/<USER>/Downloads/2024_spot.parquet')

dts = []
## 2024-01-01 到 2024-11-30 之间的日期
for dt in pd.date_range('2024-01-01', '2024-11-30'):
    dts.append(dt.strftime('%Y-%m-%d'))
dts = sorted(dts)
for dt in dts:
    data = get_date(df, dt)
    data.to_parquet(f'/Users/<USER>/Downloads/2024_spot/{dt}.parquet')



symbol = 'sol'
symbol = symbol.upper()
files = glob.glob(f'/Volumes/symbol/futures/kline/{symbol}/{symbol}_2024*.parquet')
files = sorted(files)
dfs = [pd.read_parquet(file) for file in files]
df = pd.concat(dfs)
df.to_parquet(f'/Users/<USER>/Public/symbols/{symbol}_kline1m_2024.parquet')

files = glob.glob('/Users/<USER>/Downloads/btc/*csv.gz')
files = sorted(files)
dfs = []
for file in files:
    df = pd.read_csv(file, compression='gzip')
    kline_1s = aggtrade2kline1s(df)
    dfs.append(kline_1s)
    kline_1s.to_parquet(f'/Users/<USER>/Downloads/btc/{file.split("/")[-1].split(".")[0]}_kline1s.parquet')

kline_1s = pd.concat(dfs)
kline_1s.to_parquet('/Users/<USER>/Downloads/btc/btc_kline1s_2024.parquet')


files = glob.glob('/Users/<USER>/Downloads/xrp/*.gz')
files = sorted(files)
dfs = [pd.read_csv(file, compression='gzip') for file in files]
df = pd.concat(dfs)
df.to_parquet('/Users/<USER>/Downloads/xrp/xrp_aggtrade_2024.parquet')

kline_1s = aggtrade2kline1s(df)
kline_1s.to_parquet('/Users/<USER>/Downloads/xrp/xrp_kline1s_2024.parquet')

kline_1s = pd.read_parquet('/Users/<USER>/Downloads/doge/doge_kline1s_2024-11.parquet')
kline_1s



# 测试代码
# kline_1s = pd.read_parquet('/Users/<USER>/Downloads/doge/doge_kline1s_2024-11.parquet')
# kline_1m = kline1s2kline1m(kline_1s)
# kline_1m.to_parquet('/Users/<USER>/Downloads/doge/doge_kline1m_2024-11.parquet')

kline_1s = pd.read_parquet('/Users/<USER>/Downloads/xrp/xrp_kline1s_2024.parquet')
kline_1m = kline1s2kline1m(kline_1s)
kline_1m.to_parquet('/Users/<USER>/Downloads/xrp/xrp_kline1m_2024.parquet')


df = pd.read_csv('/Users/<USER>/Downloads/doge/results.csv')

mask = (df['position'] != 0) & (df['position'].notna()) & (df['returns'] != 0)
df[mask]

symbols = ['BTC','ETH','BCH','XRP','EOS','LTC','TRX','ETC','LINK','XLM','ADA','XMR','DASH','ZEC','XTZ','BNB','ATOM','ONT','IOTA','BAT','VET','NEO','QTUM','IOST','THETA','ALGO','ZIL','KNC','ZRX','COMP','OMG','DOGE','SXP','KAVA','BAND','RLC','WAVES','MKR','SNX','DOT','DEFI','YFI','BAL','CRV','TRB','RUNE','SUSHI','EGLD','SOL','ICX','STORJ','BLZ','UNI','AVAX','FTM','ENJ','FLM','REN','KSM','NEAR','AAVE','FIL','RSR','LRC','OCEAN','BEL','CTK','AXS','ALPHA','ZEN','SKL','GRT','1INCH','CHZ','SAND','ANKR','LIT','UNFI','REEF','RVN','SFP','XEM','COTI','CHR','MANA','ALICE','HBAR','ONE','LINA','STMX','DENT','CELR','HOT','MTL','OGN','NKN','SC','DGB','1000SHIB','BAKE','GTC','BTCDOM','IOTX','RAY','C98','MASK','ATA','DYDX','1000XEC','GALA','CELO','AR','KLAY','ARPA','CTSI','LPT','ENS','PEOPLE','ROSE','DUSK','FLOW','IMX','API3','GMT','APE','WOO','JASMY','DAR','OP','INJ','STG','SPELL','1000LUNC','LUNA2','LDO','CVX','ICP','APT','QNT','FET','FXS','HOOK','MAGIC','T','HIGH','MINA','ASTR','AGIX','PHB','GMX','CFX','STX','BNX','ACH','SSV','CKB','PERP','TRU','LQTY','USDC','ID','ARB','JOE','TLM','AMB','LEVER','RDNT','HFT','XVS','BLUR','EDU','IDEX','SUI','1000PEPE','1000FLOKI','UMA','RAD','KEY','COMBO','NMR','MAV','MDT','XVG','WLD','PENDLE','ARKM','AGLD','YGG','DODOX','BNT','OXT','SEI','CYBER','HIFI','ARK','GLMR','BICO','STRAX','LOOM','BIGTIME','BOND','ORBS','STPT','WAXP','BSV','RIF','POLYX','GAS','POWR','SLP','TIA','SNT','CAKE','MEME','TWT','TOKEN','ORDI','STEEM','BADGER','ILV','NTRN','KAS','BEAMX','1000BONK','PYTH','SUPER','USTC','ONG','ETHW','JTO','1000SATS','AUCTION','1000RATS','ACE','MOVR','NFP','AI','XAI','WIF','MANTA','ONDO','LSK','ALT','JUP','ZETA','RONIN','DYM','OM','PIXEL','STRK','MAVIA','GLM','PORTAL','TON','AXL','MYRO','METIS','AEVO','VANRY','BOME','ETHFI','ENA','W','TNSR','SAGA','TAO','OMNI','REZ','BB','NOT','TURBO','IO','ZK','MEW','LISTA','ZRO','RENDER','BANANA','RARE','G','SYN','SYS','VOXEL','BRETT','ALPACA','POPCAT','SUN','VIDT','NULS','DOGS','MBOX','CHESS','FLUX','BSW','QUICK','NEIROETH','RPL','AERGO','POL','UXLINK','1MBABYDOGE','NEIRO','KDA','FIDA','FIO','CATI','GHST','LOKA','HMSTR','REI','COS','EIGEN','DIA','1000CAT','SCR','GOAT','MOODENG','SAFE','SANTOS','TROY','PONKE','COW','CETUS','1000000MOG','GRASS','DRIFT','SWELL','ACT','PNUT','HIPPO','1000X','DEGEN','BAN','AKT','SLERF','SCRT','1000CHEEMS','1000WHY']
invalid = ['1000000MOG','1000CAT','1000CHEEMS','1000WHY','1000X','1MBABYDOGE','ACT','AERGO','AGIX','AKT','ALPACA','BAN','BANANA','BB','BRETT','BSW','CATI','CETUS','CHESS','COS','COW','CTK','CVX','DEGEN','DGB','DIA','DOGS','DRIFT','EIGEN','ENA','FIDA','FIO','FLUX','FTT','G','GHST','GLMR','GOAT','GRASS','HIPPO','HMSTR','IDEX','IO','KDA','LISTA','LOKA','MBOX','MDT','MEW','MOODENG','NEIRO','NEIROETH','NOT','NULS','OCEAN','OMNI','PNUT','POL','PONKE','POPCAT','QUICK','RAD','RARE','RAY','REI','RENDER','REZ','RPL','SAFE','SAGA','SANTOS','SC','SCR','SCRT','SLERF','SLP','SNT','STPT','STRAX','SUN','SWELL','SYN','SYS','TAO','TNSR','TROY','TURBO','UXLINK','VIDT','VOXEL','W','WAVES','ZK','ZRO']
symbols = [i for i in symbols if i not in invalid]
symbols = sorted(symbols)
top_symbols = ['BTC','ETH','XRP','SOL','DOGE','BNB','LTC','1000SHIB']

def get_klines(symbol):
    base_path = '/Volumes/symbol/futures/kline'
    dfs = []
    files = glob.glob(f'{base_path}/{symbol}/{symbol}_2024*.parquet')
    files = sorted(files)
    if len(files) == 0:
        return pd.DataFrame()
    dfs = [pd.read_parquet(file) for file in files]
    df = pd.concat(dfs)
    df = df[df['i'] == '1m']
    return df

dfs = []
files = [f'/Users/<USER>/Downloads/klines/{symbol}_kline1m_2024.parquet' for symbol in symbols]
dfs = [pd.read_parquet(file) for file in files]
df = pd.concat(dfs)
df.to_parquet('/Users/<USER>/Downloads/klines/all_kline1m_2024.parquet')

df = pd.read_parquet('/Users/<USER>/Downloads/klines/all_kline1m_2024.parquet')

df['co'] = df['c'] / df['o'] - 1
df['co'] = df['co'].apply(lambda x: min(max(x, -0.05), 0.05))
df['sma_v'] = df['v'].rolling(30).mean()
df['sma_v'] = df['sma_v'].shift(1)
df['max_v'] = df['v'].rolling(30).max()
df['max_v'] = df['max_v'].shift(1)
df['vt'] = df['v'] / df['sma_v']
df['vt_max'] = df['v'] / df['max_v']

df['cc_5'] = df['c'].shift(-5) / df['c'] - 1
df['cc_10'] = df['c'].shift(-10) / df['c'] - 1
df['cc_30'] = df['c'].shift(-30) / df['c'] - 1

df['hc_30'] = (df['h'].shift(-30).rolling(30).max() / df['c'] - 1)
df['lc_30'] = (df['l'].shift(-30).rolling(30).min() / df['c'] - 1)

data = df[df['s'].isin(top_symbols)].groupby('z').agg({'co': 'mean', 'vt': 'mean', 'vt_max': 'mean', 'cc_5': 'mean', 'cc_10': 'mean', 'cc_30': 'mean', 'hc_30': 'mean', 'lc_30': 'mean'})
data = data.reset_index()
data['cc_5'] = data['cc_5'].round(4)
data['cc_10'] = data['cc_10'].round(4)
data['hc_30'] = data['hc_30'].round(4)
data['lc_30'] = data['lc_30'].round(4)



mask = (data['co'] > 0.008) & (data['vt_max'] > 1.5)
print(data[mask]['cc_5'].sum(), data[mask]['cc_30'].sum(), len(data[mask]))

path = '/Users/<USER>/Downloads/DOGE/DOGE_2025-05.csv'
df = pd.read_csv(path)
df.to_parquet(path.replace('.csv', '.parquet'))

df = pd.read_parquet('/Users/<USER>/workspace/bitquant/trade/trend/data/doge_2025_processed.parquet')
df