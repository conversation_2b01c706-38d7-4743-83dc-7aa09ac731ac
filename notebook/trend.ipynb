{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import time\n", "import talib\n", "from backtesting.test import SMA, GOOG\n", "from backtesting import Strategy, Backtest\n", "from backtesting.lib import crossover, resample_apply\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "import sys\n", "sys.path.insert(0, '../public')\n", "from jupyter import *\n", "warnings.simplefilter(action='ignore', category=FutureWarning)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["path = '/Users/<USER>/Downloads/doge/doge_kline1m_2024.parquet'\n", "df = pd.read_parquet(path)\n", "df = df[df['i'] == '1m']\n", "df = df[['z', 't', 'o', 'h', 'l', 'c', 'q', 'Q', 'v', 'V', 'n']]"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["df['sma_v'] = df['v'].rolling(30).mean()\n", "df['sma_v'] = df['sma_v'].shift(1)\n", "df['max_v'] = df['v'].rolling(30).max()\n", "df['max_v'] = df['max_v'].shift(1)\n", "\n", "df['sma_n'] = df['n'].rolling(30).mean()\n", "df['sma_n'] = df['sma_n'].shift(1)\n", "df['max_n'] = df['n'].rolling(30).max()\n", "df['max_n'] = df['max_n'].shift(1)\n", "\n", "df['v_ratio'] = df['V'] / df['v']\n", "\n", "df['vt'] = df['v'] / df['sma_v']\n", "df['vt_max'] = df['v'] / df['max_v']\n", "df['nt'] = df['n'] / df['sma_n']\n", "df['nt_max'] = df['n'] / df['max_n']\n", "\n", "df['co'] = (df['c'] / df['o'] - 1) * 100\n", "df['ho'] = (df['h'] / df['o'] - 1) * 100\n", "df['lo'] = (df['l'] / df['o'] - 1) * 100\n", "\n", "df['v_ratio'] = df['v_ratio'].round(2)\n", "df['vt'] = df['vt'].round(2)\n", "df['co'] = df['co'].round(2)\n", "df['ho'] = df['ho'].round(2)\n", "df['lo'] = df['lo'].round(2)\n", "df['nt'] = df['nt'].round(2)\n", "df['nt_max'] = df['nt_max'].round(2)\n", "\n", "df['oo_pre_5'] = (df['o'] / df['o'].shift(5) - 1) * 100\n", "df['oo_pre_5'] = df['oo_pre_5'].round(2)\n", "df['oo_pre_10'] = (df['o'] / df['o'].shift(10) - 1) * 100\n", "df['oo_pre_10'] = df['oo_pre_10'].round(2)\n", "\n", "\n", "df['ho_10'] = (df['h'].shift(-10).rolling(10).max() / df['c'] - 1) * 100\n", "df['ho_10'] = df['ho_10'].round(2)\n", "df['lo_10'] = (df['l'].shift(-10).rolling(10).min() / df['c'] - 1) * 100\n", "df['lo_10'] = df['lo_10'].round(2)\n", "df['co_10'] = (df['c'].shift(-10) / df['c'] - 1) * 100\n", "df['co_10'] = df['co_10'].round(2)\n", "\n", "df['v_5'] = df['v'].shift(-5).rolling(5).mean() / df['v']\n", "df['v_5'] = df['v_5'].round(2)\n", "\n", "df['ho_30'] = (df['h'].shift(-30).rolling(30).max() / df['c'] - 1) * 100\n", "df['ho_30'] = df['ho_30'].round(2)\n", "df['lo_30'] = (df['l'].shift(-30).rolling(30).min() / df['c'] - 1) * 100\n", "df['lo_30'] = df['lo_30'].round(2)\n", "df['co_30'] = (df['c'].shift(-30) / df['c'] - 1) * 100\n", "df['co_30'] = df['co_30'].round(2)\n"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ho_10：0.46%，lo_10：-0.4%，co_10：-0.03%\n", "ho_30：0.65%，lo_30：-0.59%，co_30：-0.05%\n", "173\n", "61\n", "-0.044739884393063575\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>z</th>\n", "      <th>v_ratio</th>\n", "      <th>vt</th>\n", "      <th>co</th>\n", "      <th>v_5</th>\n", "      <th>nt</th>\n", "      <th>ho_10</th>\n", "      <th>lo_10</th>\n", "      <th>co_10</th>\n", "      <th>ho_30</th>\n", "      <th>lo_30</th>\n", "      <th>co_30</th>\n", "      <th>final</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-02 15:34:00</td>\n", "      <td>0.65</td>\n", "      <td>47.03</td>\n", "      <td>0.94</td>\n", "      <td>0.21</td>\n", "      <td>36.08</td>\n", "      <td>0.38</td>\n", "      <td>-0.25</td>\n", "      <td>0.17</td>\n", "      <td>0.38</td>\n", "      <td>-0.37</td>\n", "      <td>-0.14</td>\n", "      <td>0.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-04 13:06:00</td>\n", "      <td>0.74</td>\n", "      <td>12.52</td>\n", "      <td>0.23</td>\n", "      <td>0.14</td>\n", "      <td>8.06</td>\n", "      <td>0.22</td>\n", "      <td>-0.05</td>\n", "      <td>0.01</td>\n", "      <td>0.38</td>\n", "      <td>-0.10</td>\n", "      <td>0.38</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-05 16:23:00</td>\n", "      <td>0.59</td>\n", "      <td>23.51</td>\n", "      <td>0.26</td>\n", "      <td>0.10</td>\n", "      <td>8.80</td>\n", "      <td>0.01</td>\n", "      <td>-0.41</td>\n", "      <td>-0.31</td>\n", "      <td>0.01</td>\n", "      <td>-0.59</td>\n", "      <td>-0.53</td>\n", "      <td>-0.31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-06 04:54:00</td>\n", "      <td>0.59</td>\n", "      <td>57.54</td>\n", "      <td>0.44</td>\n", "      <td>0.15</td>\n", "      <td>40.77</td>\n", "      <td>0.28</td>\n", "      <td>-0.59</td>\n", "      <td>-0.35</td>\n", "      <td>0.28</td>\n", "      <td>-0.82</td>\n", "      <td>-0.33</td>\n", "      <td>-0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-07 01:43:00</td>\n", "      <td>0.88</td>\n", "      <td>14.17</td>\n", "      <td>0.18</td>\n", "      <td>0.11</td>\n", "      <td>3.21</td>\n", "      <td>0.21</td>\n", "      <td>-0.27</td>\n", "      <td>-0.18</td>\n", "      <td>0.21</td>\n", "      <td>-0.44</td>\n", "      <td>-0.42</td>\n", "      <td>-0.18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-01-08 14:21:00</td>\n", "      <td>0.60</td>\n", "      <td>21.77</td>\n", "      <td>0.94</td>\n", "      <td>0.47</td>\n", "      <td>17.38</td>\n", "      <td>0.47</td>\n", "      <td>-0.14</td>\n", "      <td>0.32</td>\n", "      <td>0.47</td>\n", "      <td>-0.17</td>\n", "      <td>-0.14</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-01-08 23:41:00</td>\n", "      <td>0.51</td>\n", "      <td>24.21</td>\n", "      <td>0.44</td>\n", "      <td>0.27</td>\n", "      <td>16.88</td>\n", "      <td>0.13</td>\n", "      <td>-0.53</td>\n", "      <td>-0.33</td>\n", "      <td>0.14</td>\n", "      <td>-0.53</td>\n", "      <td>-0.13</td>\n", "      <td>-0.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-01-09 08:00:00</td>\n", "      <td>0.52</td>\n", "      <td>28.22</td>\n", "      <td>0.04</td>\n", "      <td>0.09</td>\n", "      <td>14.35</td>\n", "      <td>0.18</td>\n", "      <td>-0.29</td>\n", "      <td>0.15</td>\n", "      <td>0.28</td>\n", "      <td>-0.70</td>\n", "      <td>-0.45</td>\n", "      <td>0.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-01-10 01:11:00</td>\n", "      <td>0.61</td>\n", "      <td>19.52</td>\n", "      <td>0.35</td>\n", "      <td>1.56</td>\n", "      <td>10.73</td>\n", "      <td>2.54</td>\n", "      <td>-0.39</td>\n", "      <td>0.53</td>\n", "      <td>8.47</td>\n", "      <td>-0.39</td>\n", "      <td>0.85</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-01-10 06:15:00</td>\n", "      <td>0.55</td>\n", "      <td>54.77</td>\n", "      <td>3.27</td>\n", "      <td>0.22</td>\n", "      <td>46.09</td>\n", "      <td>2.57</td>\n", "      <td>-1.46</td>\n", "      <td>-0.97</td>\n", "      <td>2.57</td>\n", "      <td>-2.65</td>\n", "      <td>-2.38</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-01-11 17:48:00</td>\n", "      <td>0.87</td>\n", "      <td>13.04</td>\n", "      <td>0.06</td>\n", "      <td>0.15</td>\n", "      <td>3.91</td>\n", "      <td>-0.01</td>\n", "      <td>-0.52</td>\n", "      <td>-0.32</td>\n", "      <td>-0.01</td>\n", "      <td>-0.71</td>\n", "      <td>-0.09</td>\n", "      <td>-0.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-01-14 05:50:00</td>\n", "      <td>0.99</td>\n", "      <td>15.59</td>\n", "      <td>0.07</td>\n", "      <td>0.24</td>\n", "      <td>1.24</td>\n", "      <td>0.22</td>\n", "      <td>-0.05</td>\n", "      <td>-0.02</td>\n", "      <td>0.22</td>\n", "      <td>-0.16</td>\n", "      <td>-0.01</td>\n", "      <td>-0.02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-01-15 19:00:00</td>\n", "      <td>0.49</td>\n", "      <td>14.93</td>\n", "      <td>0.21</td>\n", "      <td>0.08</td>\n", "      <td>6.48</td>\n", "      <td>0.01</td>\n", "      <td>-0.32</td>\n", "      <td>-0.15</td>\n", "      <td>0.01</td>\n", "      <td>-0.45</td>\n", "      <td>-0.16</td>\n", "      <td>-0.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-01-16 09:02:00</td>\n", "      <td>0.68</td>\n", "      <td>18.45</td>\n", "      <td>0.20</td>\n", "      <td>0.10</td>\n", "      <td>8.59</td>\n", "      <td>0.00</td>\n", "      <td>-0.33</td>\n", "      <td>-0.21</td>\n", "      <td>0.00</td>\n", "      <td>-0.61</td>\n", "      <td>-0.60</td>\n", "      <td>-0.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-01-16 17:01:00</td>\n", "      <td>0.70</td>\n", "      <td>28.65</td>\n", "      <td>1.05</td>\n", "      <td>0.32</td>\n", "      <td>19.76</td>\n", "      <td>0.58</td>\n", "      <td>-0.28</td>\n", "      <td>-0.21</td>\n", "      <td>0.58</td>\n", "      <td>-0.40</td>\n", "      <td>-0.24</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-01-17 13:50:00</td>\n", "      <td>0.74</td>\n", "      <td>22.78</td>\n", "      <td>0.09</td>\n", "      <td>0.43</td>\n", "      <td>12.10</td>\n", "      <td>0.07</td>\n", "      <td>-0.44</td>\n", "      <td>-0.23</td>\n", "      <td>0.07</td>\n", "      <td>-0.44</td>\n", "      <td>-0.10</td>\n", "      <td>-0.23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-01-20 14:05:00</td>\n", "      <td>0.76</td>\n", "      <td>22.38</td>\n", "      <td>0.37</td>\n", "      <td>0.12</td>\n", "      <td>14.33</td>\n", "      <td>0.20</td>\n", "      <td>-0.01</td>\n", "      <td>0.14</td>\n", "      <td>0.22</td>\n", "      <td>-0.01</td>\n", "      <td>0.11</td>\n", "      <td>0.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-01-21 21:14:00</td>\n", "      <td>0.74</td>\n", "      <td>15.13</td>\n", "      <td>0.64</td>\n", "      <td>0.27</td>\n", "      <td>10.30</td>\n", "      <td>0.16</td>\n", "      <td>-0.80</td>\n", "      <td>-0.65</td>\n", "      <td>0.58</td>\n", "      <td>-0.80</td>\n", "      <td>0.56</td>\n", "      <td>-0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-01-22 19:54:00</td>\n", "      <td>0.63</td>\n", "      <td>44.26</td>\n", "      <td>1.14</td>\n", "      <td>0.13</td>\n", "      <td>31.11</td>\n", "      <td>0.20</td>\n", "      <td>-0.30</td>\n", "      <td>-0.04</td>\n", "      <td>0.47</td>\n", "      <td>-0.38</td>\n", "      <td>0.19</td>\n", "      <td>-0.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-01-26 14:16:00</td>\n", "      <td>0.56</td>\n", "      <td>32.08</td>\n", "      <td>0.26</td>\n", "      <td>0.07</td>\n", "      <td>9.29</td>\n", "      <td>0.00</td>\n", "      <td>-0.14</td>\n", "      <td>-0.09</td>\n", "      <td>0.00</td>\n", "      <td>-0.42</td>\n", "      <td>-0.41</td>\n", "      <td>-0.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-01-27 09:07:00</td>\n", "      <td>0.90</td>\n", "      <td>11.06</td>\n", "      <td>0.21</td>\n", "      <td>0.08</td>\n", "      <td>3.30</td>\n", "      <td>0.05</td>\n", "      <td>-0.14</td>\n", "      <td>0.05</td>\n", "      <td>0.27</td>\n", "      <td>-0.14</td>\n", "      <td>0.25</td>\n", "      <td>0.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-01-27 16:16:00</td>\n", "      <td>0.79</td>\n", "      <td>24.24</td>\n", "      <td>0.23</td>\n", "      <td>0.07</td>\n", "      <td>9.12</td>\n", "      <td>0.01</td>\n", "      <td>-0.24</td>\n", "      <td>-0.10</td>\n", "      <td>0.06</td>\n", "      <td>-0.31</td>\n", "      <td>0.04</td>\n", "      <td>-0.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-01-28 04:01:00</td>\n", "      <td>0.65</td>\n", "      <td>17.86</td>\n", "      <td>0.15</td>\n", "      <td>0.22</td>\n", "      <td>7.66</td>\n", "      <td>0.23</td>\n", "      <td>-0.06</td>\n", "      <td>0.19</td>\n", "      <td>0.61</td>\n", "      <td>-0.06</td>\n", "      <td>0.41</td>\n", "      <td>0.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-01-28 12:03:00</td>\n", "      <td>0.79</td>\n", "      <td>49.49</td>\n", "      <td>0.41</td>\n", "      <td>0.20</td>\n", "      <td>17.71</td>\n", "      <td>0.20</td>\n", "      <td>-0.16</td>\n", "      <td>0.11</td>\n", "      <td>0.62</td>\n", "      <td>-0.16</td>\n", "      <td>0.57</td>\n", "      <td>0.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-01-29 11:29:00</td>\n", "      <td>0.76</td>\n", "      <td>17.77</td>\n", "      <td>0.09</td>\n", "      <td>0.16</td>\n", "      <td>2.83</td>\n", "      <td>0.00</td>\n", "      <td>-0.20</td>\n", "      <td>-0.10</td>\n", "      <td>0.00</td>\n", "      <td>-0.43</td>\n", "      <td>-0.40</td>\n", "      <td>-0.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2024-01-29 17:32:00</td>\n", "      <td>0.65</td>\n", "      <td>88.90</td>\n", "      <td>1.36</td>\n", "      <td>0.71</td>\n", "      <td>50.43</td>\n", "      <td>3.29</td>\n", "      <td>-0.13</td>\n", "      <td>0.84</td>\n", "      <td>3.29</td>\n", "      <td>-0.13</td>\n", "      <td>1.67</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2024-01-30 06:29:00</td>\n", "      <td>0.94</td>\n", "      <td>15.84</td>\n", "      <td>0.15</td>\n", "      <td>0.06</td>\n", "      <td>2.67</td>\n", "      <td>0.09</td>\n", "      <td>-0.09</td>\n", "      <td>-0.07</td>\n", "      <td>0.09</td>\n", "      <td>-0.45</td>\n", "      <td>-0.39</td>\n", "      <td>-0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-01-30 17:41:00</td>\n", "      <td>0.92</td>\n", "      <td>13.25</td>\n", "      <td>0.10</td>\n", "      <td>0.13</td>\n", "      <td>2.23</td>\n", "      <td>0.17</td>\n", "      <td>-0.01</td>\n", "      <td>0.10</td>\n", "      <td>0.17</td>\n", "      <td>-0.16</td>\n", "      <td>0.02</td>\n", "      <td>0.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-01-31 04:44:00</td>\n", "      <td>0.77</td>\n", "      <td>16.05</td>\n", "      <td>0.22</td>\n", "      <td>0.06</td>\n", "      <td>5.61</td>\n", "      <td>0.01</td>\n", "      <td>-0.25</td>\n", "      <td>-0.15</td>\n", "      <td>0.01</td>\n", "      <td>-0.70</td>\n", "      <td>-0.58</td>\n", "      <td>-0.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-02-02 23:04:00</td>\n", "      <td>0.41</td>\n", "      <td>15.05</td>\n", "      <td>0.10</td>\n", "      <td>0.18</td>\n", "      <td>6.45</td>\n", "      <td>0.18</td>\n", "      <td>-0.09</td>\n", "      <td>0.14</td>\n", "      <td>0.27</td>\n", "      <td>-0.09</td>\n", "      <td>0.13</td>\n", "      <td>0.14</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      z  v_ratio     vt    co   v_5     nt  ho_10  lo_10  \\\n", "0   2024-01-02 15:34:00     0.65  47.03  0.94  0.21  36.08   0.38  -0.25   \n", "1   2024-01-04 13:06:00     0.74  12.52  0.23  0.14   8.06   0.22  -0.05   \n", "2   2024-01-05 16:23:00     0.59  23.51  0.26  0.10   8.80   0.01  -0.41   \n", "3   2024-01-06 04:54:00     0.59  57.54  0.44  0.15  40.77   0.28  -0.59   \n", "4   2024-01-07 01:43:00     0.88  14.17  0.18  0.11   3.21   0.21  -0.27   \n", "5   2024-01-08 14:21:00     0.60  21.77  0.94  0.47  17.38   0.47  -0.14   \n", "6   2024-01-08 23:41:00     0.51  24.21  0.44  0.27  16.88   0.13  -0.53   \n", "7   2024-01-09 08:00:00     0.52  28.22  0.04  0.09  14.35   0.18  -0.29   \n", "8   2024-01-10 01:11:00     0.61  19.52  0.35  1.56  10.73   2.54  -0.39   \n", "9   2024-01-10 06:15:00     0.55  54.77  3.27  0.22  46.09   2.57  -1.46   \n", "10  2024-01-11 17:48:00     0.87  13.04  0.06  0.15   3.91  -0.01  -0.52   \n", "11  2024-01-14 05:50:00     0.99  15.59  0.07  0.24   1.24   0.22  -0.05   \n", "12  2024-01-15 19:00:00     0.49  14.93  0.21  0.08   6.48   0.01  -0.32   \n", "13  2024-01-16 09:02:00     0.68  18.45  0.20  0.10   8.59   0.00  -0.33   \n", "14  2024-01-16 17:01:00     0.70  28.65  1.05  0.32  19.76   0.58  -0.28   \n", "15  2024-01-17 13:50:00     0.74  22.78  0.09  0.43  12.10   0.07  -0.44   \n", "16  2024-01-20 14:05:00     0.76  22.38  0.37  0.12  14.33   0.20  -0.01   \n", "17  2024-01-21 21:14:00     0.74  15.13  0.64  0.27  10.30   0.16  -0.80   \n", "18  2024-01-22 19:54:00     0.63  44.26  1.14  0.13  31.11   0.20  -0.30   \n", "19  2024-01-26 14:16:00     0.56  32.08  0.26  0.07   9.29   0.00  -0.14   \n", "20  2024-01-27 09:07:00     0.90  11.06  0.21  0.08   3.30   0.05  -0.14   \n", "21  2024-01-27 16:16:00     0.79  24.24  0.23  0.07   9.12   0.01  -0.24   \n", "22  2024-01-28 04:01:00     0.65  17.86  0.15  0.22   7.66   0.23  -0.06   \n", "23  2024-01-28 12:03:00     0.79  49.49  0.41  0.20  17.71   0.20  -0.16   \n", "24  2024-01-29 11:29:00     0.76  17.77  0.09  0.16   2.83   0.00  -0.20   \n", "25  2024-01-29 17:32:00     0.65  88.90  1.36  0.71  50.43   3.29  -0.13   \n", "26  2024-01-30 06:29:00     0.94  15.84  0.15  0.06   2.67   0.09  -0.09   \n", "27  2024-01-30 17:41:00     0.92  13.25  0.10  0.13   2.23   0.17  -0.01   \n", "28  2024-01-31 04:44:00     0.77  16.05  0.22  0.06   5.61   0.01  -0.25   \n", "29  2024-02-02 23:04:00     0.41  15.05  0.10  0.18   6.45   0.18  -0.09   \n", "\n", "    co_10  ho_30  lo_30  co_30  final  \n", "0    0.17   0.38  -0.37  -0.14   0.17  \n", "1    0.01   0.38  -0.10   0.38   0.01  \n", "2   -0.31   0.01  -0.59  -0.53  -0.31  \n", "3   -0.35   0.28  -0.82  -0.33  -0.35  \n", "4   -0.18   0.21  -0.44  -0.42  -0.18  \n", "5    0.32   0.47  -0.17  -0.14   0.40  \n", "6   -0.33   0.14  -0.53  -0.13  -0.33  \n", "7    0.15   0.28  -0.70  -0.45   0.15  \n", "8    0.53   8.47  -0.39   0.85   0.40  \n", "9   -0.97   2.57  -2.65  -2.38   0.40  \n", "10  -0.32  -0.01  -0.71  -0.09  -0.32  \n", "11  -0.02   0.22  -0.16  -0.01  -0.02  \n", "12  -0.15   0.01  -0.45  -0.16  -0.15  \n", "13  -0.21   0.00  -0.61  -0.60  -0.21  \n", "14  -0.21   0.58  -0.40  -0.24   0.40  \n", "15  -0.23   0.07  -0.44  -0.10  -0.23  \n", "16   0.14   0.22  -0.01   0.11   0.14  \n", "17  -0.65   0.58  -0.80   0.56  -0.65  \n", "18  -0.04   0.47  -0.38   0.19  -0.04  \n", "19  -0.09   0.00  -0.42  -0.41  -0.09  \n", "20   0.05   0.27  -0.14   0.25   0.05  \n", "21  -0.10   0.06  -0.31   0.04  -0.10  \n", "22   0.19   0.61  -0.06   0.41   0.19  \n", "23   0.11   0.62  -0.16   0.57   0.11  \n", "24  -0.10   0.00  -0.43  -0.40  -0.10  \n", "25   0.84   3.29  -0.13   1.67   0.40  \n", "26  -0.07   0.09  -0.45  -0.39  -0.07  \n", "27   0.10   0.17  -0.16   0.02   0.10  \n", "28  -0.15   0.01  -0.70  -0.58  -0.15  \n", "29   0.14   0.27  -0.09   0.13   0.14  "]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["columns = ['z', 'v_ratio', 'vt', 'co', 'v_5', 'nt', 'ho_10', 'lo_10', 'co_10', 'ho_30', 'lo_30', 'co_30']\n", "data = df[columns][(df['vt'] > 10) & (df['vt_max'] > 5)]\n", "mask = (data['co'] > 0)\n", "data = data[mask].reset_index(drop=True)\n", "\n", "print(f'ho_10：{round(data[\"ho_10\"].mean(), 2)}%，lo_10：{round(data[\"lo_10\"].mean(), 2)}%，co_10：{round(data[\"co_10\"].mean(), 2)}%')\n", "print(f'ho_30：{round(data[\"ho_30\"].mean(), 2)}%，lo_30：{round(data[\"lo_30\"].mean(), 2)}%，co_30：{round(data[\"co_30\"].mean(), 2)}%')\n", "\n", "data['final'] = data.apply(lambda x: 0.4 if x['ho_10'] > 0.4 else x['co_10'], axis=1)\n", "print(len(data))\n", "print(len(data[data['ho_30'] > 0.5]))\n", "print(data['final'].mean())\n", "\n", "# data.sort_values(by='ho_10', ascending=True, inplace=True)\n", "data.head(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_kline(df, start_time=None, end_time=None, save_path='kline.png'):\n", "    # 如果指定了时间范围,则过滤数据\n", "    if start_time and end_time:\n", "        mask = (df['z'] >= start_time) & (df['z'] <= end_time)\n", "        plot_df = df[mask].copy()\n", "    else:\n", "        plot_df = df.copy()\n", "        \n", "    # 设置图形大小\n", "    plt.figure(figsize=(15, 8))\n", "    \n", "    # 绘制K线图\n", "    plt.plot(plot_df.index, plot_df['h'], 'r-', alpha=0.1)  # 最高价\n", "    plt.plot(plot_df.index, plot_df['l'], 'g-', alpha=0.1)  # 最低价\n", "    \n", "    # 绘制实体部分\n", "    for idx, row in plot_df.iterrows():\n", "        if row['c'] >= row['o']:  # 收盘价大于开盘价,红色\n", "            color = 'red'\n", "        else:  # 收盘价小于开盘价,绿色\n", "            color = 'green'\n", "        plt.plot([idx, idx], [row['l'], row['h']], color=color, alpha=0.5)  # 画上下影线\n", "        plt.plot([idx, idx], [row['o'], row['c']], color=color, linewidth=2)  # 画实体\n", "    \n", "    # 设置标题和标签\n", "    plt.title('K线图')\n", "    plt.xlabel('时间')\n", "    plt.ylabel('价格')\n", "    \n", "    # 旋转x轴标签以防重叠\n", "    plt.xticks(rotation=45)\n", "    \n", "    # 自动调整布局\n", "    plt.tight_layout()\n", "    \n", "    # 保存图片\n", "    plt.savefig(save_path)\n", "    plt.close()\n", "\n", "# 示例使用:\n", "start_time = '2024-11-20 00:00:00'\n", "end_time = '2024-11-21 00:00:00'\n", "plot_kline(df, start_time, end_time, 'kline_example.png')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## kline_1s"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["path_1s = '/Users/<USER>/Downloads/doge/doge_kline1s_2024-11.parquet'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data = pd.read_parquet(path_1s)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data['sma_v'] = data['v'].rolling(60*30).mean()\n", "data['sma_v'] = data['sma_v'].shift(1)\n", "data['v_max'] = data['v'].rolling(60*5).max()\n", "data['v_max'] = data['v_max'].shift(1)\n", "data['vt'] = data['v'] / data['sma_v']\n", "data['co'] = (data['c'] / data['o'] - 1) * 100\n", "data['ho'] = (data['h'] / data['o'] - 1) * 100\n", "data['lo'] = (data['l'] / data['o'] - 1) * 100\n", "\n", "data['co'] = data['co'].round(2)\n", "data['ho'] = data['ho'].round(2)\n", "data['lo'] = data['lo'].round(2)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>z</th>\n", "      <th>vt</th>\n", "      <th>co</th>\n", "      <th>ho</th>\n", "      <th>lo</th>\n", "      <th>v</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5532</td>\n", "      <td>2024-11-01 09:32:16</td>\n", "      <td>268.376424</td>\n", "      <td>-0.21</td>\n", "      <td>0.04</td>\n", "      <td>-0.48</td>\n", "      <td>25814356.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5539</td>\n", "      <td>2024-11-01 09:32:23</td>\n", "      <td>206.576235</td>\n", "      <td>-0.19</td>\n", "      <td>0.00</td>\n", "      <td>-0.43</td>\n", "      <td>23307960.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20023</td>\n", "      <td>2024-11-01 13:33:47</td>\n", "      <td>119.298922</td>\n", "      <td>0.06</td>\n", "      <td>0.07</td>\n", "      <td>0.00</td>\n", "      <td>7166003.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>22319</td>\n", "      <td>2024-11-01 14:12:03</td>\n", "      <td>123.672731</td>\n", "      <td>-0.10</td>\n", "      <td>0.00</td>\n", "      <td>-0.18</td>\n", "      <td>8346583.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>24260</td>\n", "      <td>2024-11-01 14:44:24</td>\n", "      <td>119.813008</td>\n", "      <td>-0.08</td>\n", "      <td>0.00</td>\n", "      <td>-0.20</td>\n", "      <td>10311857.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>42787</td>\n", "      <td>2024-11-01 19:53:11</td>\n", "      <td>102.199891</td>\n", "      <td>-0.11</td>\n", "      <td>0.00</td>\n", "      <td>-0.19</td>\n", "      <td>11068899.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>49065</td>\n", "      <td>2024-11-01 21:37:49</td>\n", "      <td>159.063085</td>\n", "      <td>0.22</td>\n", "      <td>0.43</td>\n", "      <td>-0.01</td>\n", "      <td>23701884.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>64817</td>\n", "      <td>2024-11-02 02:00:21</td>\n", "      <td>148.887251</td>\n", "      <td>-0.09</td>\n", "      <td>0.00</td>\n", "      <td>-0.17</td>\n", "      <td>11643810.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>73583</td>\n", "      <td>2024-11-02 04:26:27</td>\n", "      <td>265.770981</td>\n", "      <td>0.44</td>\n", "      <td>0.52</td>\n", "      <td>0.00</td>\n", "      <td>13782260.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>77112</td>\n", "      <td>2024-11-02 05:25:16</td>\n", "      <td>144.838336</td>\n", "      <td>-0.22</td>\n", "      <td>0.01</td>\n", "      <td>-0.32</td>\n", "      <td>13140966.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>77114</td>\n", "      <td>2024-11-02 05:25:18</td>\n", "      <td>167.273368</td>\n", "      <td>-0.09</td>\n", "      <td>0.04</td>\n", "      <td>-0.33</td>\n", "      <td>17106722.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>77820</td>\n", "      <td>2024-11-02 05:37:04</td>\n", "      <td>116.993310</td>\n", "      <td>-0.20</td>\n", "      <td>0.02</td>\n", "      <td>-0.22</td>\n", "      <td>16390777.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>80747</td>\n", "      <td>2024-11-02 06:25:51</td>\n", "      <td>137.226783</td>\n", "      <td>-0.19</td>\n", "      <td>0.00</td>\n", "      <td>-0.19</td>\n", "      <td>11345284.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>87568</td>\n", "      <td>2024-11-02 08:19:32</td>\n", "      <td>107.803349</td>\n", "      <td>0.24</td>\n", "      <td>0.24</td>\n", "      <td>0.00</td>\n", "      <td>5284778.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>87599</td>\n", "      <td>2024-11-02 08:20:03</td>\n", "      <td>186.064312</td>\n", "      <td>0.16</td>\n", "      <td>0.24</td>\n", "      <td>0.00</td>\n", "      <td>10009221.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>87614</td>\n", "      <td>2024-11-02 08:20:18</td>\n", "      <td>114.837898</td>\n", "      <td>0.15</td>\n", "      <td>0.26</td>\n", "      <td>0.00</td>\n", "      <td>7132793.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>87673</td>\n", "      <td>2024-11-02 08:21:17</td>\n", "      <td>106.482903</td>\n", "      <td>0.19</td>\n", "      <td>0.23</td>\n", "      <td>0.00</td>\n", "      <td>8289192.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>87969</td>\n", "      <td>2024-11-02 08:26:13</td>\n", "      <td>133.877846</td>\n", "      <td>-0.11</td>\n", "      <td>0.12</td>\n", "      <td>-0.22</td>\n", "      <td>17261220.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>90015</td>\n", "      <td>2024-11-02 09:00:19</td>\n", "      <td>113.476206</td>\n", "      <td>0.23</td>\n", "      <td>0.27</td>\n", "      <td>0.00</td>\n", "      <td>7701580.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>93894</td>\n", "      <td>2024-11-02 10:04:58</td>\n", "      <td>209.440209</td>\n", "      <td>0.04</td>\n", "      <td>0.12</td>\n", "      <td>0.00</td>\n", "      <td>6825869.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    index                    z          vt    co    ho    lo           v\n", "0    5532  2024-11-01 09:32:16  268.376424 -0.21  0.04 -0.48  25814356.0\n", "1    5539  2024-11-01 09:32:23  206.576235 -0.19  0.00 -0.43  23307960.0\n", "2   20023  2024-11-01 13:33:47  119.298922  0.06  0.07  0.00   7166003.0\n", "3   22319  2024-11-01 14:12:03  123.672731 -0.10  0.00 -0.18   8346583.0\n", "4   24260  2024-11-01 14:44:24  119.813008 -0.08  0.00 -0.20  10311857.0\n", "5   42787  2024-11-01 19:53:11  102.199891 -0.11  0.00 -0.19  11068899.0\n", "6   49065  2024-11-01 21:37:49  159.063085  0.22  0.43 -0.01  23701884.0\n", "7   64817  2024-11-02 02:00:21  148.887251 -0.09  0.00 -0.17  11643810.0\n", "8   73583  2024-11-02 04:26:27  265.770981  0.44  0.52  0.00  13782260.0\n", "9   77112  2024-11-02 05:25:16  144.838336 -0.22  0.01 -0.32  13140966.0\n", "10  77114  2024-11-02 05:25:18  167.273368 -0.09  0.04 -0.33  17106722.0\n", "11  77820  2024-11-02 05:37:04  116.993310 -0.20  0.02 -0.22  16390777.0\n", "12  80747  2024-11-02 06:25:51  137.226783 -0.19  0.00 -0.19  11345284.0\n", "13  87568  2024-11-02 08:19:32  107.803349  0.24  0.24  0.00   5284778.0\n", "14  87599  2024-11-02 08:20:03  186.064312  0.16  0.24  0.00  10009221.0\n", "15  87614  2024-11-02 08:20:18  114.837898  0.15  0.26  0.00   7132793.0\n", "16  87673  2024-11-02 08:21:17  106.482903  0.19  0.23  0.00   8289192.0\n", "17  87969  2024-11-02 08:26:13  133.877846 -0.11  0.12 -0.22  17261220.0\n", "18  90015  2024-11-02 09:00:19  113.476206  0.23  0.27  0.00   7701580.0\n", "19  93894  2024-11-02 10:04:58  209.440209  0.04  0.12  0.00   6825869.0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["columns = ['z', 'vt', 'co', 'ho', 'lo', 'v']\n", "mask = (data['vt'] > 100)\n", "result = data[columns][mask].reset_index()\n", "result.head(20)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}