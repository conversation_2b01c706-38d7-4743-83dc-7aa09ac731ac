{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["path = '/Users/<USER>/Downloads/doge/doge_kline1m_2024.parquet'\n", "df = pd.read_parquet(path)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df['co'] = df['c'] / df['o'] - 1\n", "df['hl'] = df['h'] / df['l'] - 1"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0016118531701815471"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["i = '1m'\n", "df[df['i'] == i]['co'].std()\n", "# df[df['i'] == i]['hl'].std()\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def volatility(symbol, i):  \n", "    df = pd.read_parquet(f'/Users/<USER>/Public/symbols/{symbol.upper()}_kline1m_2024.parquet')\n", "    df['co'] = df['c'] / df['o'] - 1\n", "    df['hl'] = df['h'] / df['l'] - 1\n", "    co = df[df['i'] == i]['co'].std()\n", "    hl = df[df['i'] == i]['hl'].std()\n", "    print(symbol, i, round(co, 4), round(hl, 4))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["doge 1h 0.0118 0.0152\n", "xrp 1h 0.0096 0.0132\n", "sol 1h 0.0098 0.0108\n"]}], "source": ["for symbol in ['doge', 'xrp', 'sol']:\n", "    volatility(symbol, '1h')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}