# -*- coding: utf-8 -*
import requests, time, hmac, hashlib
from util import *

try:
    from urllib import urlencode
# python3
except ImportError:
    from urllib.parse import urlencode

class FuturesApi(object):
    BASE_URL = "https://fapi.binance.com/fapi/v1"
    BASE_URL_V2 = "https://fapi.binance.com/fapi/v2"
    BASE_URL_data = "https://fapi.binance.com/futures/data"

    def __init__(self, key, secret):
        self.key = key
        self.secret = secret

    def ping(self):
        path = "%s/ping" % self.BASE_URL_V2
        return requests.get(path, timeout=180, verify=True).json()

    def get_time(self):
        path = '%s/time' % self.BASE_URL
        return requests.get(path, timeout=180, verify=True).json()

    def get_ticker_price(self, symbol):
        path = "%s/ticker/price" % self.BASE_URL
        params = {"symbol":symbol}
        res = self._get_no_sign(path,params)
        return float(res['price'])

    def get_ticker_24hour(self,symbol):
        path = "%s/ticker/24hr" % self.BASE_URL
        params = {"symbol":symbol}
        res =  self._get_no_sign(path,params)
        return res

    def get_klines(self, symbol, interval, limit,startTime=None, endTime=None):
        path = "%s/klines" % self.BASE_URL
        params = None
        if startTime is None:
            params = {"symbol": symbol, "interval":interval, "limit":limit}
        else:
            params = {"symbol": symbol,"limit":limit, "interval":interval, "startTime":startTime, "endTime":endTime}
        return self._get_no_sign(path, params)
    
    def get_continuousklines(self, symbol, contractType, interval, limit,startTime=None, endTime=None):
        path = "%s/continuousKlines" % self.BASE_URL
        params = None
        if startTime is None:
            params = {"pair": symbol, "contractType": contractType, "interval":interval, "limit":limit}
        else:
            params = {"pair": symbol, "contractType": contractType, "limit":limit, "interval":interval, "startTime":startTime, "endTime":endTime}
        return self._get_no_sign(path, params)
    
    # 合约持仓量
    def get_openInterestHist(self, symbol, period, limit=None, startTime=None, endTime=None):
        path = "%s/openInterestHist" % self.BASE_URL_data
        params = {}
        params['symbol'] = symbol
        params['period'] = period
        if limit:
            params['limit'] = limit
        if startTime:
            params['startTime'] = startTime
        if endTime:
            params['endTime'] = endTime
        return self._get_no_sign(path, params)
    
    # 大户账户数多空比
    def get_topLongShortAccountRatio(self, symbol, period, limit=30):
        path = "%s/topLongShortAccountRatio" % self.BASE_URL_data
        params = {}
        params['symbol'] = symbol
        params['period'] = period
        params['limit'] = limit
        return self._get_no_sign(path, params)
    
    # 大户持仓量多空比
    def get_topLongShortPositionRatio(self, symbol, period, limit=30):
        path = "%s/topLongShortPositionRatio" % self.BASE_URL_data
        params = {}
        params['symbol'] = symbol
        params['period'] = period
        params['limit'] = limit
        return self._get_no_sign(path, params)
    
    # 多空持仓人数比
    def get_globalLongShortAccountRatio(self, symbol, period, limit=30):
        path = "%s/globalLongShortAccountRatio" % self.BASE_URL_data
        params = {}
        params['symbol'] = symbol
        params['period'] = period
        params['limit'] = limit
        return self._get_no_sign(path, params)
    
    # 合约主动买卖量
    def get_takerlongshortRatio(self, symbol, period, limit=30):
        path = "%s/takerlongshortRatio" % self.BASE_URL_data
        params = {}
        params['symbol'] = symbol
        params['period'] = period
        params['limit'] = limit
        return self._get_no_sign(path, params)    

    # 调整开仓杠杆
    def set_leverage(self, symbol, leverage):
        path = '%s/leverage' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['leverage'] = leverage
        params['timestamp'] = int(time.time() * 1000)
        return self._post(path, params)

    # 保证金相关
    def set_marginType(self, symbol, marginType):
        if marginType not in ['ISOLATED', 'CROSSED']:
            return 'error'
        path = '%s/marginType' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['marginType'] = marginType
        params['timestamp'] = int(time.time() * 1000)
        return self._post(path, params)
    
    def set_positionMargin(self, symbol, amount, type):
        # type 调整方向 1: 增加逐仓保证金，2: 减少逐仓保证金
        path = '%s/positionMargin' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['amount'] = amount
        params['type'] = type
        params['timestamp'] = int(time.time() * 1000)
        return self._post(path, params)
    
    def get_positionRisk(self, symbol=None):
        path = '%s/positionRisk' % self.BASE_URL_V2
        params = {}
        if symbol:
            params['symbol'] = symbol
        return self._get_sign(path, params)
    
    def set_positionSide(self, dualSidePosition='false'):
        if dualSidePosition not in ['true', 'false']:
            return 'error'
        path = '%s/positionSide/dual' % self.BASE_URL
        params = {}
        params['dualSidePosition'] = dualSidePosition
        params['timestamp'] = int(time.time() * 1000)
        return self._post(path, params)

    def get_rateLimit(self):
        path = "%s/rateLimit/order" % self.BASE_URL
        params = {}
        return self._get_sign(path, params)

    def get_apiTradingStatus(self, symbol = None):
        path = "%s/apiTradingStatus" % self.BASE_URL
        params = {}
        if symbol:
            params['symbol'] = symbol
        return self._get_sign(path, params)
    
    def get_depth(self, symbol, limit=10):
        path = "%s/depth" % self.BASE_URL
        params = {"symbol": symbol, "limit": limit}
        return self._get_no_sign(path, params)

    def get_trades(self, symbol, limit=1000):
        path = "%s/depth" % self.BASE_URL
        params = {"symbol": symbol, "limit": limit}
        return self._get_no_sign(path, params)

    def get_bookTicker(self, symbol):
        path = "%s/bookTicker" % self.BASE_URL
        params = {"symbol": symbol}
        return self._get_no_sign(path, params)

    # 止损单
    def stop(self, symbol, side, stopPrice, price, vol, newClientOrderId=None, get_params=False):
        path = '%s/order' % self.BASE_URL
        params = {}
        params['side'] = side.upper()
        params['symbol'] = symbol
        params['type'] = 'STOP'
        params['stopPrice'] = stopPrice
        params['price'] = price
        params['quantity'] = vol
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId
        if get_params:
            return params
        return self._post(path, params)
    
    def stop_market(self, symbol, side, stopPrice, vol=None, newClientOrderId=None, get_params=False):
        path = '%s/order' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['side'] = side.upper()
        params['type'] = 'STOP_MARKET'
        params['stopPrice'] = stopPrice
        if vol:
            params['quantity'] = vol
        else:
            params['closePosition'] = 'true'
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId
        if get_params:
            return params
        return self._post(path, params)


    # 止盈
    def take_profit(self, symbol, side, stopPrice, price, vol, newClientOrderId=None, get_params=False):
        path = '%s/order' % self.BASE_URL
        params = {}
        params['side'] = side.upper()
        params['symbol'] = symbol
        params['type'] = 'TAKE_PROFIT'
        params['stopPrice'] = stopPrice
        params['price'] = price
        params['quantity'] = vol
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId
        if get_params:
            return params
        return self._post(path, params)


    def take_profit_market(self, symbol, side, stopPrice, vol=None, newClientOrderId=None, get_params=False):
        path = '%s/order' % self.BASE_URL
        params = {}
        params['side'] = side.upper()
        params['symbol'] = symbol
        params['type'] = 'TAKE_PROFIT_MARKET'
        params['stopPrice'] = stopPrice
        if vol:
            params['quantity'] = vol
        else:
            params['closePosition'] = 'true'
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId
        if get_params:
            return params
        return self._post(path, params)


    # 追踪止损单
    def trail_stop_market(self, side, symbol, activationPrice, vol, callbackRate, newClientOrderId=None, get_params=False):
        path = '%s/order' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['side'] = side
        params['type'] = 'TRAILING_STOP_MARKET'
        params['activationPrice'] = activationPrice
        params['quantity'] = vol
        params['callbackRate'] = callbackRate
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId
        if get_params:
            return params
        return self._post(path, params)

    def batchOrders(self, batchOrders):
        path = '%s/batchOrders' % self.BASE_URL
        params = {}
        params['batchOrders'] = batchOrders
        return self._post(path, params)

    # 正常下单
    def limit(self, symbol, side, price, quantity, newClientOrderId=None):
        if side.upper() == 'BUY':
            return self.buy_limit(symbol, quantity, price, newClientOrderId)
        else:
            return self.sell_limit(symbol, quantity, price, newClientOrderId)
            
    def buy_limit(self, symbol, quantity, price, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "BUY", price, newClientOrderId)
        return self._post(path, params)

    def sell_limit(self, symbol, quantity, price, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "SELL", price, newClientOrderId)
        return self._post(path, params)

    def buy_market(self, symbol, quantity, newClientOrderId=None, reduceOnly=False):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "BUY", newClientOrderId=newClientOrderId, reduceOnly=reduceOnly)
        return self._post(path, params)

    def sell_market(self, symbol, quantity, newClientOrderId=None, reduceOnly=False):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "SELL", newClientOrderId=newClientOrderId, reduceOnly=reduceOnly)
        return self._post(path, params)
    
    def get_positionInfo(self, symbol):
        '''当前持仓交易对信息'''
        path = "%s/positionRisk" % self.BASE_URL
        params = {"symbol":symbol}
        return self._get_sign(path, params)
    
    def get_listen_key(self):
        path = "%s/listenKey" % self.BASE_URL
        params = {}
        return self._post(path, params)
    
    def get_exchangeInfo(self):
        path = "%s/exchangeInfo" % self.BASE_URL
        params = {}
        return self._get_no_sign(path, params)
    
    def get_spot_exchangeInfo(self):
        path = "https://api.binance.com/api/v3/exchangeInfo"
        params = {}
        return self._get_no_sign(path, params)

    ### ----删除订单----##
    def delete_order(self, symbol, orderId=None, origClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        if orderId:
            params['orderId'] = orderId
        if origClientOrderId:
            params['origClientOrderId'] = origClientOrderId
        return self._delete(path, params)

    def delete_allOpenOrders(self, symbol):
        path = "%s/allOpenOrders" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        return self._delete(path, params)
    
    def countdownCancelAll(self, symbol, countdownTime):
        path = "%s/countdownTime" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['countdownTime'] = countdownTime
        return self._post(path, params)
    
    ### ----查询----##
    # 查看当前全部挂单
    def get_openOrders(self, symbol=None):
        path = "%s/openOrders" % self.BASE_URL
        params = {}
        if symbol:
            params['symbol'] = symbol
        return self._get_sign(path, params)

    # 查询订单
    def get_order(self, symbol, orderId=None, origClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        if orderId:
            params['orderId'] = orderId
        if origClientOrderId:
            params['origClientOrderId'] = origClientOrderId
        return self._get_sign(path, params)

    
    def get_account(self):
        path = "%s/account" % self.BASE_URL_V2
        params = {}
        return self._get_sign(path, params)
    
    def get_balance(self):
        path = "%s/balance" % self.BASE_URL_V2
        params = {}
        return self._get_sign(path, params)
        
    def get_userTrades(self, symbol):
        path = "%s/userTrades" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        return self._get_sign(path, params)
    
    def get_income(self, symbol=None, incomeType=None):
        path = "%s/income" % self.BASE_URL
        params = {}
        if symbol:
            params['symbol'] = symbol
        if incomeType:
            params['incomeType'] = incomeType
        return self._get_sign(path, params)

    ### ----私有函数---- ###
    def _order(self, symbol, quantity, side, price=None, newClientOrderId=None, timeInForce='GTC',reduceOnly=False):
        params = {}
        if price is not None:
            params["type"] = "LIMIT"
            params["price"] = self._format(price)
            params["timeInForce"] = timeInForce
        else:
            params["type"] = "MARKET"
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId

        params["symbol"] = symbol
        params["side"] = side
        params["quantity"] = '%.8f' % quantity
        if reduceOnly:
            params['reduceOnly'] = 'true'

        return params
    
    def _delete(self, path, params={}):
        query = urlencode(self._sign(params))
        url = "%s?%s" % (path, query)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.delete(url, headers=header, timeout=180, verify=True).json()

    def _get_no_sign(self, path, params={}):
        query = urlencode(params)
        url = "%s?%s" % (path, query)
        return requests.get(url, timeout=180, verify=True).json()

    def _sign(self, params={}):
        data = params.copy()
        ts = int(1000 * time.time())
        data.update({"timestamp": ts})
        h = urlencode(data)
        b = bytearray()
        b.extend(self.secret.encode())
        signature = hmac.new(b, msg=h.encode('utf-8'), digestmod=hashlib.sha256).hexdigest()
        data.update({"signature": signature})
        return data
    
    def _get_sign(self, path, params={}):
        query = urlencode(self._sign(params))
        url = "%s?%s" % (path, query)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.get(url, headers=header, timeout=180, verify=True).json()

    def _post(self, path, params={}):
        params.update({"recvWindow": recv_window})
        query = urlencode(self._sign(params))
        url = "%s" % (path)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.post(url, headers=header, data=query, timeout=180, verify=True).json()

    def _format(self, price):
        return "{:.6f}".format(price)

if __name__ == "__main__":
    api = FuturesApi(api_key, api_secret)
    #print(api.buy_limit("BNBUSDT", 1, 240))
    #print(api.get_listen_key())
    print(api.get_account())
    #print(api.get_ticker_price("BNBUSDT"))
    #print(api.get_ticker_24hour("BNBUSDT"))