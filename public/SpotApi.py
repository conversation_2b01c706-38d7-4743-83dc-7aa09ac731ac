# -*- coding: utf-8 -*
import requests, time, hmac, hashlib
from util import *

try:
    from urllib import urlencode
# python3
except ImportError:
    from urllib.parse import urlencode

class SpotApi(object):
    BASE_URL = "https://api.binance.com/sapi/v1/margin"
    BASE_URL_base = "https://api.binance.com"
    BASE_URL_spot = "https://api.binance.com/api/v3"

    def __init__(self, key, secret):
        self.key = key
        self.secret = secret

    def get_time(self):
        path = '%s/time' % self.BASE_URL
        return requests.get(path, timeout=180, verify=True).json()

    def get_ticker_price(self, symbol):
        path = "%s/ticker/price" % self.BASE_URL
        params = {"symbol":symbol}
        res = self._get_no_sign(path,params)
        return float(res['price'])

    def get_ticker_24hour(self,symbol):
        path = "%s/ticker/24hr" % self.BASE_URL
        params = {"symbol":symbol}
        res =  self._get_no_sign(path,params)
        return res

    def get_klines(self, symbol, interval, limit,startTime=None, endTime=None):
        path = "%s/klines" % self.BASE_URL_spot
        params = None
        if startTime is None:
            params = {"symbol": symbol, "interval":interval, "limit":limit}
        else:
            params = {"symbol": symbol,"limit":limit, "interval":interval, "startTime":startTime, "endTime":endTime}
        return self._get_no_sign(path, params)

    # 调整开仓杠杆
    def set_leverage(self, symbol, leverage):
        path = '%s/leverage' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['leverage'] = leverage
        params['timestamp'] = int(time.time() * 1000)
        return self._post(path, params)

    # 保证金相关
    def set_marginType(self, symbol, marginType):
        if marginType not in ['ISOLATED', 'CROSSED']:
            return 'error'
        path = '%s/marginType' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['marginType'] = marginType
        params['timestamp'] = int(time.time() * 1000)
        return self._post(path, params)
    
    def set_positionSide(self, dualSidePosition='false'):
        if dualSidePosition not in ['true', 'false']:
            return 'error'
        path = '%s/positionSide/dual' % self.BASE_URL
        params = {}
        params['dualSidePosition'] = dualSidePosition
        params['timestamp'] = int(time.time() * 1000)
        return self._post(path, params)
    
    def get_depth(self, symbol, limit=10):
        path = "%s/depth" % self.BASE_URL
        params = {"symbol": symbol, "limit": limit}
        return self._get_no_sign(path, params)

    def get_trades(self, symbol, limit=1000):
        path = "%s/depth" % self.BASE_URL
        params = {"symbol": symbol, "limit": limit}
        return self._get_no_sign(path, params)

    def get_bookTicker(self, symbol):
        path = "%s/bookTicker" % self.BASE_URL
        params = {"symbol": symbol}
        return self._get_no_sign(path, params)

    # 止损止盈单
    def stop_market(self, symbol, side, stopPrice, newClientOrderId=None):
        if side == 'BUY':
            return self.buy_stop_market(symbol, stopPrice, newClientOrderId)
        if side == 'SELL':
            return self.sell_stop_market(symbol, stopPrice, newClientOrderId)
        return 'side {} is error'.format(side)

    def buy_stop_market(self, symbol, stopPrice, newClientOrderId=None):
        path = '%s/order' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['side'] = 'BUY'
        params['type'] = 'STOP_MARKET'
        params['stopPrice'] = stopPrice
        params['closePosition'] = 'true'
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId
        return self._post(path, params)
    
    def sell_stop_market(self, symbol, stopPrice, newClientOrderId=None):
        path = '%s/order' % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['side'] = 'SELL'
        params['type'] = 'STOP_MARKET'
        params['stopPrice'] = stopPrice
        params['closePosition'] = 'true'
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId
        return self._post(path, params)
    
    # 获取所有杠杆资产信息 (MARKET_DATA)
    def get_allAssets(self):
        path = '%s/allAssets' % self.BASE_URL
        return self._get_sign(path)
    
    # 获取所有全仓杠杆交易对(MARKET_DATA)
    def get_allPairs(self):
        path = '%s/allPairs' % self.BASE_URL
        return self._get_sign(path)
        #return self._get_no_sign(path)


    # 杠杆下单
    def buy_limit(self, symbol, quantity, price,  newClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "BUY", price, newClientOrderId)
        return self._post(path, params)

    def sell_limit(self, symbol, quantity, price, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "SELL", price, newClientOrderId)
        return self._post(path, params)

    def buy_market(self, symbol, quantity, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "BUY", newClientOrderId=newClientOrderId)
        return self._post(path, params)

    def sell_market(self, symbol, quantity, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = self._order(symbol, quantity, "SELL", newClientOrderId=newClientOrderId)
        return self._post(path, params)

    # 现货下单
    def buy_limit_spot(self, symbol, quantity, price,  newClientOrderId=None):
        path = "%s/order" % self.BASE_URL_spot
        params = self._order(symbol, quantity, "BUY", price, newClientOrderId)
        return self._post(path, params)

    def sell_limit_spot(self, symbol, quantity, price, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL_spot
        params = self._order(symbol, quantity, "SELL", price, newClientOrderId)
        return self._post(path, params)

    def buy_market_spot(self, symbol, quantity, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL_spot
        params = self._order(symbol, quantity, "BUY", newClientOrderId=newClientOrderId)
        return self._post(path, params)

    def sell_market_spot(self, symbol, quantity, newClientOrderId=None):
        path = "%s/order" % self.BASE_URL_spot
        params = self._order(symbol, quantity, "SELL", newClientOrderId=newClientOrderId)
        return self._post(path, params)

    
    def get_positionInfo(self, symbol):
        '''当前持仓交易对信息'''
        path = "%s/positionRisk" % self.BASE_URL
        params = {"symbol":symbol}
        return self._get_sign(path, params)
    
    # 生成 Listen Key (USER_STREAM)
    def get_margin_listen_key(self):
        path = "%s/sapi/v1/userDataStream" % self.BASE_URL_base
        params = {}
        return self._post(path, params)
    
    def get_spot_listen_key(self):
        path = "%s/api/v3/userDataStream" % self.BASE_URL_base
        params = {}
        return self._post2(path, params)
    
    def put_margin_listen_key(self, listenKey):
        path = "%s/sapi/v1/userDataStream" % self.BASE_URL_base
        params = {}
        params['listenKey'] = listenKey
        return self._put(path, params)

    def put_spot_listen_key(self, listenKey):
        path = "%s/api/v3/userDataStream" % self.BASE_URL_base
        params = {}
        params['listenKey'] = listenKey
        return self._put2(path, params)
    
    def get_exchangeInfo(self):
        path = "%s/exchangeInfo" % self.BASE_URL
        params = {}
        return self._get_no_sign(path, params)
    
    def get_spot_exchangeInfo(self):
        path = "https://api.binance.com/api/v3/exchangeInfo"
        params = {}
        return self._get_no_sign(path, params)

    ### ----删除订单----##
    def delete_order(self, symbol, orderId=None, origClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        if orderId:
            params['orderId'] = orderId
        if origClientOrderId:
            params['origClientOrderId'] = origClientOrderId
        return self._delete(path, params)

    def delete_openOrders(self, symbol):
        path = "%s/openOrders" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        return self._delete(path, params)

    def delete_openOrders_spot(self, symbol):
        path = "%s/openOrders" % self.BASE_URL_spot
        params = {}
        params['symbol'] = symbol
        return self._delete(path, params)
    
    def countdownCancelAll(self, symbol, countdownTime):
        path = "%s/countdownTime" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        params['countdownTime'] = countdownTime
        return self._post(path, params)
    
    ### ----查询----##
    # 查看当前全部挂单
    def get_openOrders(self, symbol=None):
        path = "%s/openOrders" % self.BASE_URL
        params = {}
        if symbol:
            params['symbol'] = symbol
        return self._get_sign(path, params)

    # 查询订单
    def get_order(self, symbol, orderId=None, origClientOrderId=None):
        path = "%s/order" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        if orderId:
            params['orderId'] = orderId
        if origClientOrderId:
            params['origClientOrderId'] = origClientOrderId
        return self._get_sign(path, params)

    
    def get_account(self):
        path = "%s/account" % self.BASE_URL
        params = {}
        return self._get_sign(path, params)
    
    def get_userTrades(self, symbol):
        path = "%s/userTrades" % self.BASE_URL
        params = {}
        params['symbol'] = symbol
        return self._get_sign(path, params)
    
    def get_income(self, symbol=None, incomeType=None):
        path = "%s/income" % self.BASE_URL
        params = {}
        if symbol:
            params['symbol'] = symbol
        if incomeType:
            params['incomeType'] = incomeType
        return self._get_sign(path, params)

    ### ----私有函数---- ###
    def _order(self, symbol, quantity, side, price=None, newClientOrderId=None, timeInForce='GTC'):
        params = {}
        if price is not None:
            params["type"] = "LIMIT"
            params["price"] = self._format(price)
            params["timeInForce"] = timeInForce
        else:
            params["type"] = "MARKET"
        if newClientOrderId:
            params['newClientOrderId'] = newClientOrderId

        params["symbol"] = symbol
        params["side"] = side
        params["quantity"] = '%.8f' % quantity

        return params
    
    def _delete(self, path, params={}):
        query = urlencode(self._sign(params))
        url = "%s?%s" % (path, query)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.delete(url, headers=header, timeout=180, verify=True).json()

    def _get_no_sign(self, path, params={}):
        query = urlencode(params)
        url = "%s?%s" % (path, query)
        return requests.get(url, timeout=180, verify=True).json()

    def _sign(self, params={}):
        data = params.copy()
        ts = int(1000 * time.time())
        data.update({"timestamp": ts})
        h = urlencode(data)
        b = bytearray()
        b.extend(self.secret.encode())
        signature = hmac.new(b, msg=h.encode('utf-8'), digestmod=hashlib.sha256).hexdigest()
        data.update({"signature": signature})
        return data
    
    def _get_sign(self, path, params={}):
        query = urlencode(self._sign(params))
        url = "%s?%s" % (path, query)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.get(url, headers=header, timeout=180, verify=True).json()

    def _post(self, path, params={}):
        params.update({"recvWindow": recv_window})
        query = urlencode(self._sign(params))
        url = "%s" % (path)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.post(url, headers=header, data=query, timeout=180, verify=True).json()

    def _post2(self, path, params={}):
        query = urlencode(params)
        url = "%s" % (path)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.post(url, headers=header, data=query, timeout=180, verify=True).json()

    def _put(self, path, params={}):
        params.update({"recvWindow": recv_window})
        query = urlencode(self._sign(params))
        url = "%s" % (path)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.put(url, headers=header, data=query, timeout=180, verify=True).json()
    
    def _put2(self, path, params={}):
        #params.update({"recvWindow": recv_window})
        query = urlencode(params)
        url = "%s" % (path)
        header = {"X-MBX-APIKEY": self.key, "Content-Type": "application/x-www-form-urlencoded"}
        return requests.put(url, headers=header, data=query, timeout=180, verify=True).json()

    def _format(self, price):
        return "{:.6f}".format(price)

if __name__ == "__main__":
    api = SpotApi(api_key, api_secret)
    #print(api.buy_limit("BNBUSDT", 1, 240))
    #print(api.get_listen_key())
    print(api.get_account())
    #print(api.get_ticker_price("BNBUSDT"))
    #print(api.get_ticker_24hour("BNBUSDT"))