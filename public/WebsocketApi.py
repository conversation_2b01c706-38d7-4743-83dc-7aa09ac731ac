import time
import json
import hmac
import hashlib
import base64
from websocket import create_connection

# 设置身份验证
API_KEY = 'rnUSzbanaTtXzOh9iUyVXrWHIzfKS7GjB02nza6OQVnt9CZAuemQNFyA3r6Ts4TB'
private_key = 'oLn5vNBJgTXcfsaTUiGT1OdrqypmeRwagDKY8YjJUEN6nkKTu7PuaZhjLLhvF1vI'

# api_key = 'rnUSzbanaTtXzOh9iUyVXrWHIzfKS7GjB02nza6OQVnt9CZAuemQNFyA3r6Ts4TB'
# api_secret = 'oLn5vNBJgTXcfsaTUiGT1OdrqypmeRwagDKY8YjJUEN6nkKTu7PuaZhjLLhvF1vI'

# 设置请求参数
params = {
    'symbol': 'DOGEUSDT',  # 合约交易对
    'side': 'BUY',         # 买入方向
    'positionSide': 'LONG',  # 持仓方向：LONG或SHORT
    'type': 'LIMIT',       # 订单类型
    'timeInForce': 'GTC',  # 有效期
    'quantity': '200',     # 数量
    'price': '0.25',       # 价格
    'reduceOnly': 'false', # 是否仅平仓
    'workingType': 'CONTRACT_PRICE',  # 合约价格类型
    'timestamp': int(time.time() * 1000),  # 时间戳
}

# 生成签名
payload = '&'.join([f'{param}={value}' for param, value in sorted(params.items())])
signature = hmac.new(private_key.encode('utf-8'), payload.encode('utf-8'), hashlib.sha256).hexdigest()

# 构建认证请求
auth_request = {
    "method": "REQUEST",
    "params": {
        "apiKey": API_KEY,
        "signature": signature,
        "timestamp": params['timestamp']
    },
    "id": 1
}

# 构建下单请求
order_request = {
    "method": "order.place",
    "params": params,
    "id": 2
}

try:
    # 连接 WebSocket (使用合约的WebSocket地址)
    # ws = create_connection('wss://fstream.binance.com/ws')
    ws = create_connection('wss://ws-fapi.binance.com/ws-fapi/v1')
    print("WebSocket连接成功")

    # 如果认证成功，发送下单请求
    auth_result = {'result': True}
    if "result" in auth_result and auth_result["result"]:
        ws.send(json.dumps(order_request))
        order_response = ws.recv()
        print(f"下单响应: {order_response}")
    else:
        print("认证失败")

except Exception as e:
    print(f"发生错误: {str(e)}")

finally:
    # 关闭连接
    if 'ws' in locals():
        ws.close()
        print("WebSocket连接已关闭")