import pandas as pd
import numpy as np
import json
import time
from decimal import Decimal
import gzip

def decMul(a, b):
    return float(Decimal(str(a)) * Decimal(str(b)))
def decPlus(a, b):
    return float(Decimal(str(a)) + Decimal(str(b)))

symbols = ['DOGE']
for symbol in symbols:
    klines = ['z,s,i,t,o,h,l,c,v,T,q,n,V,Q\n']
    path = f'/Users/<USER>/Public/data/bitcoin/aggTrade/{symbol}-aggTrade.csv.gz'
    f = gzip.open(path, 'r')
    a = f.readline().strip().decode("utf-8") 
    a = f.readline().strip().decode("utf-8").split(',')
    # z0,s1,a2,p3,v4,f5,l6,T7,m8,q9
    price, quantity,tran_time, is_buyer = float(a[3]),float(a[4]),int(a[7]),a[8]=='true'
    ts = tran_time // 1000
    o=h=l=c=price
    v=quantity
    V=0
    q=decMul(price, quantity)
    Q = 0
    n = 1
    if is_buyer:
        V = quantity
        Q = q
    # 1s
    i = 1
    a = f.readline().strip().decode("utf-8") 
    while a:
        a = a.split(',')
        price, quantity,tran_time, is_buyer = float(a[3]),float(a[4]),int(a[7]),a[8]=='true'
        if tran_time // 1000 > ts + i - 1:
            local_time = time.localtime(ts)
            z = time.strftime('%Y-%m-%d %H:%M:%S', local_time)
            k = f'{z},{symbol},1s,{ts*1000},{o},{h},{l},{c},{v},{ts*1000+i*1000},{q},{n},{V},{Q}\n'
            klines.append(k)
            while ts + i < tran_time // 1000:
                ts = ts + i
                local_time = time.localtime(ts)
                z = time.strftime('%Y-%m-%d %H:%M:%S', local_time)
                o=h=l=c=c
                q=Q=v=V=n=0
                k = f'{z},{symbol},1s,{ts*1000},{o},{h},{l},{c},{v},{ts*1000+i*1000},{q},{n},{V},{Q}\n'
                klines.append(k)
            
            ts = tran_time // 1000
            o=h=l=c=price
            v=quantity
            V=0
            q=decMul(price, quantity)
            Q = 0
            n = 1
            if is_buyer:
                V = quantity
                Q = q    
        else:
            n += 1
            h = max(h, price)
            l = min(l, price)
            c = price
            v = decPlus(v, quantity)
            q = decPlus(q, decMul(price, quantity))
            if is_buyer:
                V = decPlus(V, quantity)
                Q = decPlus(Q, decMul(price, quantity))
        a = f.readline().strip().decode("utf-8") 
    local_time = time.localtime(ts)
    z = time.strftime('%Y-%m-%d %H:%M:%S', local_time)
    k = f'{z},{symbol},1s,{ts*1000},{o},{h},{l},{c},{v},{ts*1000+1000},{q},{n},{V},{Q}\n'
    klines.append(k)
    f.close()

    output = f'/Users/<USER>/Public/data/bitcoin/aggTrade/{symbol}-kline.csv'
    f = open(output, 'w')
    f.writelines(klines)
    f.close()
        

    