import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from constant import trade_symbols, spot_symbols, futures_symbols
from util import *
from multiprocessing import Process,Queue
import pandas as pd
from decimal import Decimal

logger = logging.getLogger("SmallGoal-AggTrade2kline")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler('/root/workspace/trade/public/log/log.aggTrade2kline')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class Clean():
    def __init__(self, type):
        self.type = type
        #self.group_id = 'aggTrade2kline'
        self.group_id = str(time.time())
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.consumer_offset_key = 'futures_all_offset'
        self.klines = {}
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')

    def decAdd(self, a, b):
        return float(Decimal(str(a)) + Decimal(str(b)))

    def decMul(self, a, b):
        return float(Decimal(str(a)) * Decimal(str(b)))

    def init_kline(self, v):
        symbol = v['s']
        p = v['p']
        q = v['q']
        k = {}
        k['e'] = 'kline'
        k['i'] = '1s'
        k['s'] = v['s']
        k['t'] = (v['E'] // 1000) * 1000
        k['T'] = k['t'] + 1000
        k['o'] = k['h'] = k['l'] = k['c'] = p
        k['v'] = q
        k['n'] = 1
        k['q'] = self.decMul(p, q)
        if v['m']:
            k['V'] = k['v']
            k['Q'] = k['q']
        else:
            k['V'] = 0
            k['Q'] = 0
        k['x'] = False
        self.klines[symbol] = k
                  
    
    def trade2kline(self, v):
        symbol = v['s']
        k = self.klines[symbol]
        p = v['p']
        q = v['q']
        if v['E'] > k['T']:
            ts = int(time.time() * 1000)
            z = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ts/1000)) + '.' + str(ts)[-3:]
            for key in self.klines:
                k = self.klines[key]
                k['E'] = ts
                k['z'] = z
                k['x'] = True
                self.producer.send('futures_kline', key=k['s']+'_1s', value=json.dumps(k))

                k['t'] = (v['E'] // 1000) * 1000
                k['T'] = k['t'] + 1000
                k['o'] = k['h'] = k['l'] = k['c']
                k['v'] = k['q'] = k['V'] = k['Q'] = k['n'] = 0
                self.klines[key] = k

            # 新的 kline 初始化
            self.init_kline(v)
        elif k['x']:
            self.init_kline(v)
        else:
            # kline 增加交易数据
            k['n'] += 1
            k['h'] = max(k['h'], p)
            k['l'] = min(k['l'], p)
            k['c'] = p
            k['v'] = self.decAdd(k['v'], v['q'])
            k['q'] = self.decAdd(k['q'], self.decMul(p, q))
            if v['m']:
                k['V'] = self.decAdd(k['V'], q)
                k['Q'] = self.decAdd(k['Q'], self.decMul(p, q))
            

    def run(self):
        logger.info(self.type + ' aggTrade2kline starts:')
        consumer = KafkaConsumer('futures_aggTrade',
            group_id = self.group_id,
            bootstrap_servers=['localhost:9092'],
            key_deserializer= bytes.decode,
            value_deserializer= bytes.decode,
            auto_offset_reset='latest',
            auto_commit_interval_ms=5000)

        
        for msg in consumer:
            v = json.loads(msg.value)
            symbol = v['s']
            if symbol in self.klines:
                self.trade2kline(v)
            else:
                self.init_kline(v)


            
        
        '''
        for msg in consumer:
            # type_event
            self.msg = msg
            topic = msg.topic
            self.value = json.loads(msg.value)
            if 'kline' in topic:
                if self.value['i'] == '1m':
                    self.clean_kline()
                self.redis.incr(msg.topic + '_offset')
            if 'aggTrade' in topic:
                self.clean_aggtrade()
            if 'depthUpdate' in topic:
                self.clean_depthUpdate()   
            # consumer.commit_async()
            # consumer.commit_async(callback=_on_send_response)
        '''
                

def main(q, type):
    clean = Clean(type)
    clean.run()

if __name__ == "__main__":
    types = ['futures']
    q = Queue()
    process_list=[]
    for type in types:
        p = Process(target=main, args=(q, type,))
        p.start()
        process_list.append(p)
    for p in process_list:
        p.join()

    logger.info('over')
    weixin_info('finished!', weixin_token)