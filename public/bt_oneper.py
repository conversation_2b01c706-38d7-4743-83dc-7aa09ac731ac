import pandas as pd
import numpy as np
import json
import time
import matplotlib.pyplot as plt
from matplotlib.pyplot import *
import sys
sys.path.insert(0, '/Users/<USER>/workspace/bitquant/trade/public/')
from jupyter import *

df = pd.read_parquet('/Users/<USER>/Public/data/bitcoin/aggTrade/DOGE-2023-kline.parquet')
df['co'] = (df['c']/df['o']-1) * 100
df['ho'] = (df['h']/df['o']-1) * 100
df['hl'] = (df['h']/df['l']-1) * 100
df['lo'] = (df['l']/df['o']-1) * 100
df['ap'] = (df['q']/df['v']/df['o']-1) * 100

st = 1672502400000
et = st + 60 * 1000
for i in range(len(df)):
    