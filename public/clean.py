import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from FuturesApi import FuturesApi
from constant import trade_symbols, spot_symbols, futures_symbols, spot_combine, futures_combine
from util import *
from multiprocessing import Process,Queue
import pandas as pd
from util4hyt import df_to_hytstyle


logger = logging.getLogger("SmallGoal-Clean")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler('/root/workspace/trade/public/log/log.clean')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class Clean():
    def __init__(self, type):
        self.type = type
        self.group_id = 'clean'
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        
        if type == 'spot':
            self.symbols = spot_combine
        else:
            self.symbols = futures_combine
        #self.trade_symbols = trade_symbols
        # type_all_symbols = json.loads(self.redis.get(f'{type}_all_symbols'))
        # self.symbols = list(set(self.symbols) & set(type_all_symbols))
        self.k_lag = True
        self.indexes = ['o', 'h', 'l', 'c', 'n', 'v', 'q', 'V', 'Q', 't', 'T', 'E', 'co', 'ho', 'hl','z']
        self.consumer_offset_key = 'futures_all_clean_offset'
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        
        for symbol in self.symbols:
            key = '{t}_{s}_1m'.format(t=self.type, s=symbol)
            if not self.redis.exists(key):
                self.redis.lpush(key, '0')

    
    def clean_kline(self):
        kline = self.value
        x = kline['x']
        s = kline['s']
        i = kline['i']
        T = kline['T']
        key = f'{self.type}_{s}_1m'

        if self.redis.lindex(key, 0) == '0':
            a = self.redis.lpop(key)
            self.redis.lpush(key, json.dumps(kline))
            return

        if self.k_lag:
            k_redis = json.loads(self.redis.lindex(key, 0))
            if k_redis['T'] < kline['T']:
                self.k_lag = False
            else:
                return

        pre = json.loads(self.redis.lindex(key, 0))
        while T - pre['T'] > 60*1000:
            pre['T'] += 60*1000
            pre['t'] += 60*1000
            self.redis.lpush(key, json.dumps(pre))
        
        mnt = int(T /1000/60 + 60 * 8)
        remainder = mnt % (60 * 24)
        
        if remainder == 0: # 00:00
            self.redis.ltrim(key, 0, 60 * 24 * 4)

        self.redis.lpush(key, json.dumps(kline))
        self.producer.send(f'{self.type}_clean', key=f'kline_{s}',value=json.dumps(kline))
                  
    
    def clean_aggtrade(self):
        s = self.value['s']
        key = '{t}_{s}_aggTrade'.format(t=self.type, s=s)
        self.redis.lpush(key, json.dumps(self.value))
    
    def clean_depthUpdate(self):
        s = self.value['s']
        key = '{s}_depthUpdate'.format(s=s)
        self.redis.set(key, json.dumps(self.value))

    def run(self):
        logger.info(self.type + ' clean starts:')
        consumer = KafkaConsumer(group_id = self.group_id,
                                bootstrap_servers=['localhost:9092'],
                                key_deserializer= bytes.decode,
                                value_deserializer= bytes.decode,
                                auto_commit_interval_ms=3000)
        pt1 = TopicPartition(self.type + '_aggTrade', 0)
        pt2 = TopicPartition(self.type + '_kline', 0)
        pt3 = TopicPartition(self.type + '_depthUpdate', 0)
        # of1 = int(self.redis.get(self.type + '_aggTrade_offset'))
        of2 = int(self.redis.get(self.type + '_kline_offset'))
        #of3 = int(self.redis.get(self.type + '_depthUpdate_offset'))
        offset= consumer.end_offsets([pt3])
        of3 = list(offset.values())[0]

        consumer.assign([pt2,pt3])
        consumer.seek(pt2, of2)
        consumer.seek(pt3, of3)
        
        for msg in consumer:
            # type_event
            self.msg = msg
            topic = msg.topic
            self.value = json.loads(msg.value)
            if 'kline' in topic:
                if self.value['i'] == '1m' and self.value['x']:
                    self.clean_kline()
                self.redis.incr(msg.topic + '_offset')
            if 'aggTrade' in topic:
                self.clean_aggtrade()
            if 'depthUpdate' in topic:
                self.clean_depthUpdate()   
            # consumer.commit_async()
            # consumer.commit_async(callback=_on_send_response)
                

def main(q, type):
    clean = Clean(type)
    clean.run()

if __name__ == "__main__":
    types = ['spot', 'futures']
    while True:
        try:
            q = Queue()
            process_list=[]
            for type in types:
                p = Process(target=main, args=(q, type,))
                p.start()
                process_list.append(p)
            for p in process_list:
                p.join()
        except Exception as e:
            logger.error(str(e))

        logger.info('over')
        weixin_info('clean finished!', warning_token)