# -*- coding: utf-8 -*

all_symbols = ['1INCHUSDT', 'AAVEUSDT', 'ADAUSDT', 'ALGOUSDT', 'ALICEUSDT', 'ALPHAUSDT', 'ANKRUSDT', 'APEUSDT', 'API3USDT', 'ARPAUSDT', 'ARUSDT', 'ATAUSDT', 'ATOMUSDT', 'AUDIOUSDT', 'AVAXUSDT', 'AXSUSDT', 'BAKEUSDT', 'BALUSDT', 'BANDUSDT', 'BATUSDT', 'BCHUSDT', 'BELUSDT', 'BLZUSDT', 'BNBUSDT', 'BTCUSDT', 'C98USDT', 'CELOUSDT', 'CELRUSDT', 'CHRUSDT', 'CHZUSDT', 'COMPUSDT', 'COTIUSDT', 'CRVUSDT', 'CTKUSDT', 'CTSIUSDT', 'CVCUSDT', 'DARUSDT', 'DASHUSDT', 'DENTUSDT', 'DGBUSDT', 'DOGEUSDT', 'DOTUSDT', 'DUSKUSDT', 'DYDXUSDT', 'EGLDUSDT', 'ENJUSDT', 'ENSUSDT', 'EOSUSDT', 'ETCUSDT', 'ETHUSDT', 'FILUSDT', 'FLMUSDT', 'FLOWUSDT', 'FTMUSDT', 'GALAUSDT', 'GMTUSDT', 'GRTUSDT', 'GTCUSDT', 'HBARUSDT', 'HOTUSDT', 'ICPUSDT', 'ICXUSDT', 'IMXUSDT', 'IOSTUSDT', 'IOTAUSDT', 'IOTXUSDT', 'JASMYUSDT', 'KAVAUSDT', 'KNCUSDT', 'KSMUSDT', 'LINAUSDT', 'LINKUSDT', 'LPTUSDT', 'LRCUSDT', 'LTCUSDT', 'MANAUSDT', 'MASKUSDT', 'MATICUSDT', 'MKRUSDT', 'MTLUSDT', 'NEARUSDT', 'NEOUSDT', 'NKNUSDT', 'OCEANUSDT', 'OGNUSDT', 'ONEUSDT', 'ONTUSDT', 'OPUSDT', 'PEOPLEUSDT', 'QTUMUSDT', 'REEFUSDT', 'RLCUSDT', 'ROSEUSDT', 'RSRUSDT', 'RUNEUSDT', 'RVNUSDT', 'SANDUSDT', 'SFPUSDT', 'SKLUSDT', 'SNXUSDT', 'SOLUSDT', 'STMXUSDT', 'STORJUSDT', 'SUSHIUSDT', 'SXPUSDT', 'THETAUSDT', 'TOMOUSDT', 'TRBUSDT', 'TRXUSDT', 'UNIUSDT', 'VETUSDT', 'WOOUSDT', 'XEMUSDT', 'XLMUSDT', 'XMRUSDT', 'XRPUSDT', 'XTZUSDT', 'YFIUSDT', 'ZECUSDT', 'ZENUSDT', 'ZILUSDT', 'ZRXUSDT', 'MAGICUSDT', 'AGIXUSDT', 'FETUSDT', 'COCOSUSDT', 'HIGHUSDT', 'CFXUSDT']

# futures 需要加1000
spot_symbols = all_symbols + ['SHIBUSDT', 'BNXUSDT', 'PYRUSDT', 'SLPUSDT', 'TVKUSDT', 'TLMUSDT', 'GHSTUSDT', 'BURGERUSDT', 'YGGUSDT', 'MBOXUSDT', 'ILVUSDT']
futures_symbols = all_symbols + ['1000SHIBUSDT'] + []

# 新品种
spot_symbols += ['INJUSDT', 'STGUSDT', 'SPELLUSDT', 'LDOUSDT', 'CVXUSDT', 'APTUSDT', 'QNTUSDT']
futures_symbols += ['INJUSDT', 'STGUSDT', 'SPELLUSDT', '1000LUNCUSDT', 'LDOUSDT', 'CVXUSDT', 'APTUSDT', 'QNTUSDT']

#futures 有，spot没有
futures_symbols += ['BLUEBIRDUSDT', 'FOOTBALLUSDT']
futures_symbols += ['DEFIUSDT']

# futures 下架的
invalid_symbols = ['SRMUSDT', 'RAYUSDT', 'FTTUSDT', 'BNXUSDT', 'HNTUSDT']

small_symbols = []

trade_symbols = ['1INCHUSDT', 'AAVEUSDT', 'ADAUSDT', 'ALGOUSDT', 'ALICEUSDT', 'ALPHAUSDT', 'APEUSDT', 'API3USDT', 'ARUSDT', 'ATOMUSDT', 'AUDIOUSDT', 'AVAXUSDT', 'AXSUSDT', 'BAKEUSDT', 'BCHUSDT', 'BELUSDT', 'BNBUSDT', 'BTCUSDT', 'CELOUSDT', 'CELRUSDT', 'CHRUSDT', 'CHZUSDT', 'COTIUSDT', 'CRVUSDT', 'DARUSDT', 'DOGEUSDT', 'DOTUSDT', 'DYDXUSDT', 'EGLDUSDT', 'ENJUSDT', 'ENSUSDT', 'EOSUSDT', 'ETCUSDT', 'ETHUSDT', 'FILUSDT', 'FTMUSDT', 'GALAUSDT', 'GMTUSDT', 'GRTUSDT', 'IMXUSDT', 'IOSTUSDT', 'JASMYUSDT', 'KAVAUSDT', 'KNCUSDT', 'LINAUSDT', 'LINKUSDT', 'LRCUSDT', 'LTCUSDT', 'MANAUSDT', 'MATICUSDT', 'MTLUSDT', 'NEARUSDT', 'OGNUSDT', 'ONEUSDT', 'PEOPLEUSDT', 'ROSEUSDT', 'RSRUSDT', 'RUNEUSDT', 'SANDUSDT', 'SKLUSDT', 'SNXUSDT', 'SOLUSDT', 'SUSHIUSDT', 'SXPUSDT', 'THETAUSDT', 'TRXUSDT', 'UNIUSDT', 'VETUSDT', 'XLMUSDT', 'XMRUSDT', 'XRPUSDT', 'XTZUSDT', 'YFIUSDT', 'ZECUSDT', 'ZILUSDT', 'ZRXUSDT'] + ['SHIBUSDT'] + ['BANDUSDT','BLZUSDT','CTSIUSDT','FLMUSDT','GTCUSDT','TRBUSDT']



futures_trade_symbols = ['1INCHUSDT','AAVEUSDT','ADAUSDT','ALGOUSDT','ALICEUSDT','ALPHAUSDT','ANKRUSDT','APEUSDT','APTUSDT','ARUSDT','ARPAUSDT','ATOMUSDT','AVAXUSDT','AXSUSDT','BAKEUSDT','BANDUSDT','BCHUSDT','BELUSDT','BLZUSDT','BNBUSDT','BTCUSDT','C98USDT','CELOUSDT','CELRUSDT','CHRUSDT','CHZUSDT','COMPUSDT','COTIUSDT','CRVUSDT','CTSIUSDT','DARUSDT','DASHUSDT','DOGEUSDT','DOTUSDT','DUSKUSDT','DYDXUSDT','EGLDUSDT','ENJUSDT','ENSUSDT','EOSUSDT','ETCUSDT','ETHUSDT','FILUSDT','FLMUSDT','FLOWUSDT','GALAUSDT','GMTUSDT','GRTUSDT','GTCUSDT','HBARUSDT','HOTUSDT','ICXUSDT','IMXUSDT','INJUSDT','IOTAUSDT','IOTXUSDT','JASMYUSDT','KAVAUSDT','KNCUSDT','KSMUSDT','LDOUSDT','LINAUSDT','LINKUSDT','LPTUSDT','LRCUSDT','LTCUSDT','MANAUSDT','MASKUSDT','MATICUSDT','MKRUSDT','MTLUSDT','NEARUSDT','NEOUSDT','NKNUSDT','OGNUSDT','ONEUSDT','ONTUSDT','OPUSDT','PEOPLEUSDT','QNTUSDT','REEFUSDT','RLCUSDT','ROSEUSDT','RSRUSDT','RUNEUSDT','RVNUSDT','SANDUSDT','SFPUSDT','SNXUSDT','SOLUSDT','STMXUSDT','STORJUSDT','SUSHIUSDT','SXPUSDT','THETAUSDT','TRBUSDT','TRXUSDT','UNIUSDT','VETUSDT','WOOUSDT','XLMUSDT','XRPUSDT','XTZUSDT','YFIUSDT','ZECUSDT','ZENUSDT','ZILUSDT'] + ['1000SHIBUSDT']


meta = ['ALICEUSDT', 'AXSUSDT', 'COCOSUSDT', 'GALAUSDT', 'GMTUSDT', 'MAGICUSDT' 'MANAUSDT'] # + ['ILVUSDT', 'PYRUSDT', 'SLPUSDT', 'TVKUSDT']
game = ['MANAUSDT', 'AXSUSDT', 'SANDUSDT', 'DARUSDT', 'ALICEUSDT', 'GALAUSDT', 'ENJUSDT', 'MAGICUSDT'] + ['BNXUSDT', 'PYRUSDT', 'TLMUSDT', 'GHSTUSDT', 'BURGERUSDT', 'YGGUSDT', 'MBOXUSDT']
ai = ['AGIXUSDT', 'FETUSDT', 'OCEANUSDT']
dao = ['ENSUSDT', 'PEOPLEUSDT']
layer2 = ['CELRUSDT', 'CHRUSDT', 'CTSIUSDT', 'MATICUSDT', 'NEARUSDT', 'ONEUSDT', 'OPUSDT', 'SKLUSDT', 'XLMUSDT']

trade_symbols.sort()

wasee_spot = ['ALPHAUSDT', 'ATAUSDT', 'TOMOUSDT', 'ZRXUSDT']

wasee_margin = ['APEUSDT', 'AXSUSDT', 'AUDIOUSDT', 'BELUSDT', 'CELOUSDT', 'CRVUSDT', 'DARUSDT', 'DOGEUSDT', 'DYDXUSDT', 'EGLDUSDT', 'ENJUSDT', 'FILUSDT', 'FTMUSDT', 'GALUSDT', 'GALAUSDT', 'GMTUSDT', 'IMXUSDT', 'IOSTUSDT', 'KAVAUSDT', 'LRCUSDT', 'MTLUSDT', 'OGNUSDT', 'PEOPLEUSDT', 'RSRUSDT', 'RUNEUSDT', 'SKLUSDT', 'SNXUSDT', 'VETUSDT', 'WAVESUSDT', 'XMRUSDT', 'XRPUSDT', 'XTZUSDT', 'YFIUSDT', 'ZECUSDT', 'ZILUSDT']

wasee_symbols = wasee_spot + wasee_margin
#brick_symbols = ['DOGEUSDT', 'STGUSDT']



# 2024-12-14
spot_valid_symbols_full = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'NEOUSDT', 'LTCUSDT', 'QTUMUSDT', 'ADAUSDT', 'XRPUSDT', 'EOSUSDT', 'TUSDUSDT', 'IOTAUSDT', 'XLMUSDT', 'ONTUSDT', 'TRXUSDT', 'ETCUSDT', 'ICXUSDT', 'NULSUSDT', 'VETUSDT', 'USDCUSDT', 'LINKUSDT', 'ONGUSDT', 'HOTUSDT', 'ZILUSDT', 'ZRXUSDT', 'FETUSDT', 'BATUSDT', 'ZECUSDT', 'IOSTUSDT', 'CELRUSDT', 'DASHUSDT', 'THETAUSDT', 'ENJUSDT', 'ATOMUSDT', 'TFUELUSDT', 'ONEUSDT', 'FTMUSDT', 'ALGOUSDT', 'DOGEUSDT', 'DUSKUSDT', 'ANKRUSDT', 'WINUSDT', 'COSUSDT', 'MTLUSDT', 'DENTUSDT', 'WANUSDT', 'FUNUSDT', 'CVCUSDT', 'CHZUSDT', 'BANDUSDT', 'XTZUSDT', 'RVNUSDT', 'HBARUSDT', 'NKNUSDT', 'STXUSDT', 'KAVAUSDT', 'ARPAUSDT', 'IOTXUSDT', 'RLCUSDT', 'CTXCUSDT', 'BCHUSDT', 'TROYUSDT', 'VITEUSDT', 'FTTUSDT', 'EURUSDT', 'OGNUSDT', 'WRXUSDT', 'LSKUSDT', 'BNTUSDT', 'LTOUSDT', 'MBLUSDT', 'COTIUSDT', 'STPTUSDT', 'DATAUSDT', 'SOLUSDT', 'CTSIUSDT', 'HIVEUSDT', 'CHRUSDT', 'ARDRUSDT', 'MDTUSDT', 'STMXUSDT', 'KNCUSDT', 'LRCUSDT', 'COMPUSDT', 'SCUSDT', 'ZENUSDT', 'SNXUSDT', 'VTHOUSDT', 'DGBUSDT', 'SXPUSDT', 'MKRUSDT', 'DCRUSDT', 'STORJUSDT', 'MANAUSDT', 'YFIUSDT', 'BALUSDT', 'BLZUSDT', 'KMDUSDT', 'JSTUSDT', 'CRVUSDT', 'SANDUSDT', 'NMRUSDT', 'DOTUSDT', 'LUNAUSDT', 'RSRUSDT', 'PAXGUSDT', 'TRBUSDT', 'SUSHIUSDT', 'KSMUSDT', 'EGLDUSDT', 'DIAUSDT', 'RUNEUSDT', 'FIOUSDT', 'UMAUSDT', 'BELUSDT', 'WINGUSDT', 'UNIUSDT', 'OXTUSDT', 'SUNUSDT', 'AVAXUSDT', 'FLMUSDT', 'UTKUSDT', 'XVSUSDT', 'ALPHAUSDT', 'AAVEUSDT', 'NEARUSDT', 'FILUSDT', 'INJUSDT', 'AUDIOUSDT', 'CTKUSDT', 'AKROUSDT', 'AXSUSDT', 'HARDUSDT', 'STRAXUSDT', 'ROSEUSDT', 'AVAUSDT', 'SKLUSDT', 'GRTUSDT', 'JUVUSDT', 'PSGUSDT', '1INCHUSDT', 'OGUSDT', 'ATMUSDT', 'ASRUSDT', 'CELOUSDT', 'RIFUSDT', 'TRUUSDT', 'CKBUSDT', 'TWTUSDT', 'FIROUSDT', 'SFPUSDT', 'DODOUSDT', 'CAKEUSDT', 'ACMUSDT', 'BADGERUSDT', 'FISUSDT', 'OMUSDT', 'PONDUSDT', 'DEGOUSDT', 'ALICEUSDT', 'LINAUSDT', 'PERPUSDT', 'SUPERUSDT', 'CFXUSDT', 'TKOUSDT', 'PUNDIXUSDT', 'TLMUSDT', 'BARUSDT', 'FORTHUSDT', 'BAKEUSDT', 'BURGERUSDT', 'SLPUSDT', 'SHIBUSDT', 'ICPUSDT', 'ARUSDT', 'MASKUSDT', 'LPTUSDT', 'XVGUSDT', 'ATAUSDT', 'GTCUSDT', 'ERNUSDT', 'PHAUSDT', 'MLNUSDT', 'DEXEUSDT', 'C98USDT', 'CLVUSDT', 'QNTUSDT', 'FLOWUSDT', 'MINAUSDT', 'RAYUSDT', 'FARMUSDT', 'ALPACAUSDT', 'QUICKUSDT', 'MBOXUSDT', 'REQUSDT', 'GHSTUSDT', 'WAXPUSDT', 'GNOUSDT', 'XECUSDT', 'ELFUSDT', 'DYDXUSDT', 'IDEXUSDT', 'VIDTUSDT', 'USDPUSDT', 'GALAUSDT', 'ILVUSDT', 'YGGUSDT', 'SYSUSDT', 'DFUSDT', 'FIDAUSDT', 'AGLDUSDT', 'RADUSDT', 'BETAUSDT', 'RAREUSDT', 'LAZIOUSDT', 'CHESSUSDT', 'ADXUSDT', 'AUCTIONUSDT', 'DARUSDT', 'BNXUSDT', 'MOVRUSDT', 'CITYUSDT', 'ENSUSDT', 'QIUSDT', 'PORTOUSDT', 'POWRUSDT', 'JASMYUSDT', 'AMPUSDT', 'PYRUSDT', 'ALCXUSDT', 'SANTOSUSDT', 'BICOUSDT', 'FLUXUSDT', 'FXSUSDT', 'VOXELUSDT', 'HIGHUSDT', 'CVXUSDT', 'PEOPLEUSDT', 'SPELLUSDT', 'JOEUSDT', 'ACHUSDT', 'IMXUSDT', 'GLMRUSDT', 'LOKAUSDT', 'SCRTUSDT', 'API3USDT', 'BTTCUSDT', 'ACAUSDT', 'XNOUSDT', 'WOOUSDT', 'ALPINEUSDT', 'TUSDT', 'ASTRUSDT', 'GMTUSDT', 'KDAUSDT', 'APEUSDT', 'BSWUSDT', 'BIFIUSDT', 'STEEMUSDT', 'NEXOUSDT', 'REIUSDT', 'LDOUSDT', 'OPUSDT', 'LEVERUSDT', 'STGUSDT', 'LUNCUSDT', 'GMXUSDT', 'POLYXUSDT', 'APTUSDT', 'OSMOUSDT', 'HFTUSDT', 'PHBUSDT', 'HOOKUSDT', 'MAGICUSDT', 'HIFIUSDT', 'RPLUSDT', 'PROSUSDT', 'GNSUSDT', 'SYNUSDT', 'VIBUSDT', 'SSVUSDT', 'LQTYUSDT', 'AMBUSDT', 'USTCUSDT', 'GASUSDT', 'GLMUSDT', 'PROMUSDT', 'QKCUSDT', 'UFTUSDT', 'IDUSDT', 'ARBUSDT', 'RDNTUSDT', 'WBTCUSDT', 'EDUUSDT', 'SUIUSDT', 'AERGOUSDT', 'PEPEUSDT', 'FLOKIUSDT', 'ASTUSDT', 'SNTUSDT', 'COMBOUSDT', 'MAVUSDT', 'PENDLEUSDT', 'ARKMUSDT', 'WBETHUSDT', 'WLDUSDT', 'FDUSDUSDT', 'SEIUSDT', 'CYBERUSDT', 'ARKUSDT', 'CREAMUSDT', 'IQUSDT', 'NTRNUSDT', 'TIAUSDT', 'MEMEUSDT', 'ORDIUSDT', 'BEAMXUSDT', 'PIVXUSDT', 'VICUSDT', 'BLURUSDT', 'VANRYUSDT', 'AEURUSDT', 'JTOUSDT', '1000SATSUSDT', 'BONKUSDT', 'ACEUSDT', 'NFPUSDT', 'AIUSDT', 'XAIUSDT', 'MANTAUSDT', 'ALTUSDT', 'JUPUSDT', 'PYTHUSDT', 'RONINUSDT', 'DYMUSDT', 'PIXELUSDT', 'STRKUSDT', 'PORTALUSDT', 'PDAUSDT', 'AXLUSDT', 'WIFUSDT', 'METISUSDT', 'AEVOUSDT', 'BOMEUSDT', 'ETHFIUSDT', 'ENAUSDT', 'WUSDT', 'TNSRUSDT', 'SAGAUSDT', 'TAOUSDT', 'OMNIUSDT', 'REZUSDT', 'BBUSDT', 'NOTUSDT', 'IOUSDT', 'ZKUSDT', 'LISTAUSDT', 'ZROUSDT', 'GUSDT', 'BANANAUSDT', 'RENDERUSDT', 'TONUSDT', 'DOGSUSDT', 'EURIUSDT', 'SLFUSDT', 'POLUSDT', 'NEIROUSDT', 'TURBOUSDT', '1MBABYDOGEUSDT', 'CATIUSDT', 'HMSTRUSDT', 'EIGENUSDT', 'SCRUSDT', 'BNSOLUSDT', 'LUMIAUSDT', 'KAIAUSDT', 'COWUSDT', 'CETUSUSDT', 'PNUTUSDT', 'ACTUSDT', 'USUALUSDT', 'THEUSDT', 'ACXUSDT', 'ORCAUSDT', 'MOVEUSDT', 'MEUSDT', 'VELODROMEUSDT']

futures_valid_symbols_full = ['BTCUSDT', 'ETHUSDT', 'BCHUSDT', 'XRPUSDT', 'EOSUSDT', 'LTCUSDT', 'TRXUSDT', 'ETCUSDT', 'LINKUSDT', 'XLMUSDT', 'ADAUSDT', 'XMRUSDT', 'DASHUSDT', 'ZECUSDT', 'XTZUSDT', 'BNBUSDT', 'ATOMUSDT', 'ONTUSDT', 'IOTAUSDT', 'BATUSDT', 'VETUSDT', 'NEOUSDT', 'QTUMUSDT', 'IOSTUSDT', 'THETAUSDT', 'ALGOUSDT', 'ZILUSDT', 'KNCUSDT', 'ZRXUSDT', 'COMPUSDT', 'DOGEUSDT', 'SXPUSDT', 'KAVAUSDT', 'BANDUSDT', 'RLCUSDT', 'MKRUSDT', 'SNXUSDT', 'DOTUSDT', 'DEFIUSDT', 'YFIUSDT', 'BALUSDT', 'CRVUSDT', 'TRBUSDT', 'RUNEUSDT', 'SUSHIUSDT', 'EGLDUSDT', 'SOLUSDT', 'ICXUSDT', 'STORJUSDT', 'BLZUSDT', 'UNIUSDT', 'AVAXUSDT', 'FTMUSDT', 'ENJUSDT', 'FLMUSDT', 'RENUSDT', 'KSMUSDT', 'NEARUSDT', 'AAVEUSDT', 'FILUSDT', 'RSRUSDT', 'LRCUSDT', 'BELUSDT', 'AXSUSDT', 'ALPHAUSDT', 'ZENUSDT', 'SKLUSDT', 'GRTUSDT', '1INCHUSDT', 'CHZUSDT', 'SANDUSDT', 'ANKRUSDT', 'REEFUSDT', 'RVNUSDT', 'SFPUSDT', 'XEMUSDT', 'COTIUSDT', 'CHRUSDT', 'MANAUSDT', 'ALICEUSDT', 'HBARUSDT', 'ONEUSDT', 'LINAUSDT', 'STMXUSDT', 'DENTUSDT', 'CELRUSDT', 'HOTUSDT', 'MTLUSDT', 'OGNUSDT', 'NKNUSDT', '1000SHIBUSDT', 'BAKEUSDT', 'GTCUSDT', 'BTCDOMUSDT', 'IOTXUSDT', 'C98USDT', 'MASKUSDT', 'ATAUSDT', 'DYDXUSDT', '1000XECUSDT', 'GALAUSDT', 'CELOUSDT', 'ARUSDT', 'ARPAUSDT', 'CTSIUSDT', 'LPTUSDT', 'ENSUSDT', 'PEOPLEUSDT', 'ROSEUSDT', 'DUSKUSDT', 'FLOWUSDT', 'IMXUSDT', 'API3USDT', 'GMTUSDT', 'APEUSDT', 'WOOUSDT', 'JASMYUSDT', 'DARUSDT', 'OPUSDT', 'INJUSDT', 'STGUSDT', 'SPELLUSDT', '1000LUNCUSDT', 'LUNA2USDT', 'LDOUSDT', 'ICPUSDT', 'APTUSDT', 'QNTUSDT', 'FETUSDT', 'FXSUSDT', 'HOOKUSDT', 'MAGICUSDT', 'TUSDT', 'HIGHUSDT', 'MINAUSDT', 'ASTRUSDT', 'PHBUSDT', 'GMXUSDT', 'CFXUSDT', 'STXUSDT', 'BNXUSDT', 'ACHUSDT', 'SSVUSDT', 'CKBUSDT', 'PERPUSDT', 'TRUUSDT', 'LQTYUSDT', 'USDCUSDT', 'IDUSDT', 'ARBUSDT', 'JOEUSDT', 'TLMUSDT', 'AMBUSDT', 'LEVERUSDT', 'RDNTUSDT', 'HFTUSDT', 'XVSUSDT', 'BLURUSDT', 'EDUUSDT', 'SUIUSDT', '1000PEPEUSDT', '1000FLOKIUSDT', 'UMAUSDT', 'KEYUSDT', 'COMBOUSDT', 'NMRUSDT', 'MAVUSDT', 'XVGUSDT', 'WLDUSDT', 'PENDLEUSDT', 'ARKMUSDT', 'AGLDUSDT', 'YGGUSDT', 'DODOXUSDT', 'BNTUSDT', 'OXTUSDT', 'SEIUSDT', 'CYBERUSDT', 'HIFIUSDT', 'ARKUSDT', 'BICOUSDT', 'LOOMUSDT', 'BIGTIMEUSDT', 'BONDUSDT', 'ORBSUSDT', 'WAXPUSDT', 'BSVUSDT', 'RIFUSDT', 'POLYXUSDT', 'GASUSDT', 'POWRUSDT', 'TIAUSDT', 'CAKEUSDT', 'MEMEUSDT', 'TWTUSDT', 'TOKENUSDT', 'ORDIUSDT', 'STEEMUSDT', 'BADGERUSDT', 'ILVUSDT', 'NTRNUSDT', 'KASUSDT', 'BEAMXUSDT', '1000BONKUSDT', 'PYTHUSDT', 'SUPERUSDT', 'USTCUSDT', 'ONGUSDT', 'ETHWUSDT', 'JTOUSDT', '1000SATSUSDT', 'AUCTIONUSDT', '1000RATSUSDT', 'ACEUSDT', 'MOVRUSDT', 'NFPUSDT', 'AIUSDT', 'XAIUSDT', 'WIFUSDT', 'MANTAUSDT', 'ONDOUSDT', 'LSKUSDT', 'ALTUSDT', 'JUPUSDT', 'ZETAUSDT', 'RONINUSDT', 'DYMUSDT', 'OMUSDT', 'PIXELUSDT', 'STRKUSDT', 'MAVIAUSDT', 'GLMUSDT', 'PORTALUSDT', 'TONUSDT', 'AXLUSDT', 'MYROUSDT', 'METISUSDT', 'AEVOUSDT', 'VANRYUSDT', 'BOMEUSDT', 'ETHFIUSDT', 'ENAUSDT', 'WUSDT', 'TNSRUSDT', 'SAGAUSDT', 'TAOUSDT', 'OMNIUSDT', 'REZUSDT', 'BBUSDT', 'NOTUSDT', 'TURBOUSDT', 'IOUSDT', 'ZKUSDT', 'MEWUSDT', 'LISTAUSDT', 'ZROUSDT', 'RENDERUSDT', 'BANANAUSDT', 'RAREUSDT', 'GUSDT', 'SYNUSDT', 'SYSUSDT', 'VOXELUSDT', 'BRETTUSDT', 'ALPACAUSDT', 'POPCATUSDT', 'SUNUSDT', 'VIDTUSDT', 'NULSUSDT', 'DOGSUSDT', 'MBOXUSDT', 'CHESSUSDT', 'FLUXUSDT', 'BSWUSDT', 'QUICKUSDT', 'NEIROETHUSDT', 'RPLUSDT', 'AERGOUSDT', 'POLUSDT', 'UXLINKUSDT', '1MBABYDOGEUSDT', 'NEIROUSDT', 'KDAUSDT', 'FIDAUSDT', 'FIOUSDT', 'CATIUSDT', 'GHSTUSDT', 'LOKAUSDT', 'HMSTRUSDT', 'REIUSDT', 'COSUSDT', 'EIGENUSDT', 'DIAUSDT', '1000CATUSDT', 'SCRUSDT', 'GOATUSDT', 'MOODENGUSDT', 'SAFEUSDT', 'SANTOSUSDT', 'TROYUSDT', 'PONKEUSDT', 'COWUSDT', 'CETUSUSDT', '1000000MOGUSDT', 'GRASSUSDT', 'DRIFTUSDT', 'SWELLUSDT', 'ACTUSDT', 'PNUTUSDT', 'HIPPOUSDT', '1000XUSDT', 'DEGENUSDT', 'BANUSDT', 'AKTUSDT', 'SLERFUSDT', 'SCRTUSDT', '1000CHEEMSUSDT', '1000WHYUSDT']




# 25-01-23 spot 200
spot_200 = ['1000SATSUSDT','1INCHUSDT','AAVEUSDT','ACEUSDT','ACHUSDT','ADAUSDT','AEVOUSDT','AGLDUSDT','AIUSDT','ALGOUSDT','ALICEUSDT','ALPHAUSDT','ALTUSDT','AMBUSDT','ANKRUSDT','APEUSDT','API3USDT','APTUSDT','ARUSDT','ARBUSDT','ARKUSDT','ARKMUSDT','ARPAUSDT','ASTRUSDT','ATAUSDT','ATOMUSDT','AUCTIONUSDT','AVAXUSDT','AXLUSDT','AXSUSDT','BADGERUSDT','BAKEUSDT','BCHUSDT','BEAMXUSDT','BELUSDT','BICOUSDT','BLURUSDT','BNBUSDT','BNXUSDT','BOMEUSDT','BONKUSDT','BTCUSDT','C98USDT','CAKEUSDT','CELOUSDT','CFXUSDT','CHRUSDT','CHZUSDT','CKBUSDT','COMPUSDT','COTIUSDT','CRVUSDT','CTSIUSDT','CYBERUSDT','DASHUSDT','DOGEUSDT','DOTUSDT','DUSKUSDT','DYDXUSDT','DYMUSDT','EDUUSDT','EGLDUSDT','ENJUSDT','ENSUSDT','EOSUSDT','ETCUSDT','ETHUSDT','ETHFIUSDT','FETUSDT','FILUSDT','FLMUSDT','FLOKIUSDT','FLOWUSDT','FTMUSDT','FXSUSDT','GALAUSDT','GASUSDT','GLMUSDT','GMTUSDT','GMXUSDT','GRTUSDT','GTCUSDT','HBARUSDT','HIFIUSDT','HIGHUSDT','HOOKUSDT','HOTUSDT','ICPUSDT','IDUSDT','ILVUSDT','IMXUSDT','INJUSDT','IOTAUSDT','IOTXUSDT','JASMYUSDT','JOEUSDT','JTOUSDT','JUPUSDT','KAVAUSDT','KSMUSDT','LDOUSDT','LEVERUSDT','LINAUSDT','LINKUSDT','LPTUSDT','LQTYUSDT','LRCUSDT','LSKUSDT','LTCUSDT','LUNCUSDT','MAGICUSDT','MANAUSDT','MANTAUSDT','MASKUSDT','MAVUSDT','MEMEUSDT','METISUSDT','MINAUSDT','MKRUSDT','MOVRUSDT','MTLUSDT','NEARUSDT','NEOUSDT','NFPUSDT','NMRUSDT','NTRNUSDT','OGNUSDT','OMUSDT','ONEUSDT','ONGUSDT','ONTUSDT','OPUSDT','ORDIUSDT','PENDLEUSDT','PEOPLEUSDT','PEPEUSDT','PERPUSDT','PHBUSDT','PIXELUSDT','POLYXUSDT','PORTALUSDT','POWRUSDT','PYTHUSDT','QNTUSDT','QTUMUSDT','RDNTUSDT','RIFUSDT','RLCUSDT','RONINUSDT','ROSEUSDT','RSRUSDT','RUNEUSDT','RVNUSDT','SANDUSDT','SEIUSDT','SHIBUSDT','SKLUSDT','SNXUSDT','SOLUSDT','SPELLUSDT','SSVUSDT','STGUSDT','STMXUSDT','STORJUSDT','STRKUSDT','STXUSDT','SUIUSDT','SUPERUSDT','SUSHIUSDT','SXPUSDT','TUSDT','THETAUSDT','TIAUSDT','TLMUSDT','TRBUSDT','TRUUSDT','TRXUSDT','TWTUSDT','UMAUSDT','UNIUSDT','USDCUSDT','USTCUSDT','VANRYUSDT','VETUSDT','WIFUSDT','WLDUSDT','WOOUSDT','XAIUSDT','XECUSDT','XLMUSDT','XRPUSDT','XTZUSDT','XVGUSDT','YFIUSDT','YGGUSDT','ZECUSDT','ZENUSDT','ZILUSDT','ZRXUSDT']

futures_200 = ['1000SATSUSDT','1INCHUSDT','AAVEUSDT','ACEUSDT','ACHUSDT','ADAUSDT','AEVOUSDT','AGLDUSDT','AIUSDT','ALGOUSDT','ALICEUSDT','ALPHAUSDT','ALTUSDT','AMBUSDT','ANKRUSDT','APEUSDT','API3USDT','APTUSDT','ARUSDT','ARBUSDT','ARKUSDT','ARKMUSDT','ARPAUSDT','ASTRUSDT','ATAUSDT','ATOMUSDT','AUCTIONUSDT','AVAXUSDT','AXLUSDT','AXSUSDT','BADGERUSDT','BAKEUSDT','BCHUSDT','BEAMXUSDT','BELUSDT','BICOUSDT','BLURUSDT','BNBUSDT','BNXUSDT','BOMEUSDT','1000BONKUSDT','BTCUSDT','C98USDT','CAKEUSDT','CELOUSDT','CFXUSDT','CHRUSDT','CHZUSDT','CKBUSDT','COMPUSDT','COTIUSDT','CRVUSDT','CTSIUSDT','CYBERUSDT','DASHUSDT','DOGEUSDT','DOTUSDT','DUSKUSDT','DYDXUSDT','DYMUSDT','EDUUSDT','EGLDUSDT','ENJUSDT','ENSUSDT','EOSUSDT','ETCUSDT','ETHUSDT','ETHFIUSDT','FETUSDT','FILUSDT','FLMUSDT','1000FLOKIUSDT','FLOWUSDT','FTMUSDT','FXSUSDT','GALAUSDT','GASUSDT','GLMUSDT','GMTUSDT','GMXUSDT','GRTUSDT','GTCUSDT','HBARUSDT','HIFIUSDT','HIGHUSDT','HOOKUSDT','HOTUSDT','ICPUSDT','IDUSDT','ILVUSDT','IMXUSDT','INJUSDT','IOTAUSDT','IOTXUSDT','JASMYUSDT','JOEUSDT','JTOUSDT','JUPUSDT','KAVAUSDT','KSMUSDT','LDOUSDT','LEVERUSDT','LINAUSDT','LINKUSDT','LPTUSDT','LQTYUSDT','LRCUSDT','LSKUSDT','LTCUSDT','1000LUNCUSDT','MAGICUSDT','MANAUSDT','MANTAUSDT','MASKUSDT','MAVUSDT','MEMEUSDT','METISUSDT','MINAUSDT','MKRUSDT','MOVRUSDT','MTLUSDT','NEARUSDT','NEOUSDT','NFPUSDT','NMRUSDT','NTRNUSDT','OGNUSDT','OMUSDT','ONEUSDT','ONGUSDT','ONTUSDT','OPUSDT','ORDIUSDT','PENDLEUSDT','PEOPLEUSDT','1000PEPEUSDT','PERPUSDT','PHBUSDT','PIXELUSDT','POLYXUSDT','PORTALUSDT','POWRUSDT','PYTHUSDT','QNTUSDT','QTUMUSDT','RDNTUSDT','RIFUSDT','RLCUSDT','RONINUSDT','ROSEUSDT','RSRUSDT','RUNEUSDT','RVNUSDT','SANDUSDT','SEIUSDT','1000SHIBUSDT','SKLUSDT','SNXUSDT','SOLUSDT','SPELLUSDT','SSVUSDT','STGUSDT','STMXUSDT','STORJUSDT','STRKUSDT','STXUSDT','SUIUSDT','SUPERUSDT','SUSHIUSDT','SXPUSDT','TUSDT','THETAUSDT','TIAUSDT','TLMUSDT','TRBUSDT','TRUUSDT','TRXUSDT','TWTUSDT','UMAUSDT','UNIUSDT','USDCUSDT','USTCUSDT','VANRYUSDT','VETUSDT','WIFUSDT','WLDUSDT','WOOUSDT','XAIUSDT','1000XECUSDT','XLMUSDT','XRPUSDT','XTZUSDT','XVGUSDT','YFIUSDT','YGGUSDT','ZECUSDT','ZENUSDT','ZILUSDT','ZRXUSDT']


spot_2024 = ['1INCHUSDT','AAVEUSDT','ADAUSDT','ALGOUSDT','ALICEUSDT','ALPHAUSDT','ANKRUSDT','APEUSDT','APTUSDT','ARUSDT','ARPAUSDT','ATOMUSDT','AVAXUSDT','AXSUSDT','BAKEUSDT','BANDUSDT','BCHUSDT','BELUSDT','BNBUSDT','BTCUSDT','C98USDT','CELOUSDT','CELRUSDT','CHRUSDT','CHZUSDT','COMPUSDT','COTIUSDT','CRVUSDT','CTSIUSDT','DASHUSDT','DOGEUSDT','DOTUSDT','DUSKUSDT','DYDXUSDT','EGLDUSDT','ENJUSDT','ENSUSDT','EOSUSDT','ETCUSDT','ETHUSDT','FILUSDT','FLMUSDT','FLOWUSDT','GALAUSDT','GMTUSDT','GRTUSDT','GTCUSDT','HBARUSDT','HOTUSDT','ICXUSDT','IMXUSDT','INJUSDT','IOTAUSDT','IOTXUSDT','JASMYUSDT','KAVAUSDT','KNCUSDT','KSMUSDT','LDOUSDT','LINAUSDT','LINKUSDT','LPTUSDT','LRCUSDT','LTCUSDT','MANAUSDT','MASKUSDT','MKRUSDT','MTLUSDT','NEARUSDT','NEOUSDT','NKNUSDT','OGNUSDT','ONEUSDT','ONTUSDT','OPUSDT','PEOPLEUSDT','QNTUSDT','RLCUSDT','ROSEUSDT','RSRUSDT','RUNEUSDT','RVNUSDT','SANDUSDT','SFPUSDT','SHIBUSDT','SNXUSDT','SOLUSDT','STMXUSDT','STORJUSDT','SUSHIUSDT','SXPUSDT','THETAUSDT','TRBUSDT','TRXUSDT','UNIUSDT','VETUSDT','WOOUSDT','XLMUSDT','XRPUSDT','XTZUSDT','YFIUSDT','ZECUSDT','ZENUSDT','ZILUSDT']

futures_2024 = ['1INCHUSDT','AAVEUSDT','ADAUSDT','ALGOUSDT','ALICEUSDT','ALPHAUSDT','ANKRUSDT','APEUSDT','APTUSDT','ARUSDT','ARPAUSDT','ATOMUSDT','AVAXUSDT','AXSUSDT','BAKEUSDT','BANDUSDT','BCHUSDT','BELUSDT','BNBUSDT','BTCUSDT','C98USDT','CELOUSDT','CELRUSDT','CHRUSDT','CHZUSDT','COMPUSDT','COTIUSDT','CRVUSDT','CTSIUSDT','DASHUSDT','DOGEUSDT','DOTUSDT','DUSKUSDT','DYDXUSDT','EGLDUSDT','ENJUSDT','ENSUSDT','EOSUSDT','ETCUSDT','ETHUSDT','FILUSDT','FLMUSDT','FLOWUSDT','GALAUSDT','GMTUSDT','GRTUSDT','GTCUSDT','HBARUSDT','HOTUSDT','ICXUSDT','IMXUSDT','INJUSDT','IOTAUSDT','IOTXUSDT','JASMYUSDT','KAVAUSDT','KNCUSDT','KSMUSDT','LDOUSDT','LINAUSDT','LINKUSDT','LPTUSDT','LRCUSDT','LTCUSDT','MANAUSDT','MASKUSDT','MKRUSDT','MTLUSDT','NEARUSDT','NEOUSDT','NKNUSDT','OGNUSDT','ONEUSDT','ONTUSDT','OPUSDT','PEOPLEUSDT','QNTUSDT','RLCUSDT','ROSEUSDT','RSRUSDT','RUNEUSDT','RVNUSDT','SANDUSDT','SFPUSDT','1000SHIBUSDT','SNXUSDT','SOLUSDT','STMXUSDT','STORJUSDT','SUSHIUSDT','SXPUSDT','THETAUSDT','TRBUSDT','TRXUSDT','UNIUSDT','VETUSDT','WOOUSDT','XLMUSDT','XRPUSDT','XTZUSDT','YFIUSDT','ZECUSDT','ZENUSDT','ZILUSDT']


spot_combine = list(set(spot_200 + spot_2024))
futures_combine = list(set(futures_200 + futures_2024))

spot_symbols = spot_combine
futures_symbols = futures_combine

# redis 参数
'''
set lever of each symbol
group_lever
group_base
group_info 会初始化
group_positions_5min
group_balance_5min
group_positions_half_hour
group_record_history
group_balance_day
'''
