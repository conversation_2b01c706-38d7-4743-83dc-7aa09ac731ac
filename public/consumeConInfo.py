import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from constant import trade_symbols, spot_symbols, futures_symbols
from util import *
from multiprocessing import Process,Queue
import pandas as pd
from decimal import Decimal

from openai import OpenAI
import serpapi

logger = logging.getLogger("SmallGoal-contractInfo")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler('/root/workspace/trade/public/log/log.contractInfo')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

r = redis.Redis(host='localhost', password='peng1234', decode_responses=True)
ai_api_key = 'sk-uaTd5MbyH3SeEx1f71C352D79c784fF98f0595E14bCa0aC2'
serpapi_api_key = '6cfd2f12083c727892872057499b526d70a64077d36c39b02843b79c0b0d209d'

client = OpenAI(api_key=ai_api_key, base_url="https://aihubmix.com/v1")

def search_online(query):
    params = {
        "engine": "google",
        "q": query,
        "api_key": serpapi_api_key
    }
    response = serpapi.search(params)
    # results = response['organic_results']
    return json.dumps(response['organic_results']) if 'organic_results' in response else "未找到结果"

def chat_with_gpt4o(query, user_question):
    # 先进行联网查询
    search_result = search_online(query)

    # 将联网结果与用户输入一同发送到 GPT-4
    response = client.chat.completions.create(
        model="chatgpt-4o-latest",
        messages=[
            {"role": "system", "content": "你是一个智能助手，可以结合联网查询的结果回答问题。"},
            {"role": "user", "content": f"用户的问题是：{user_question}。以下是联网查询的结果：{search_result}"},
            {"role": "assistant", "content": "请根据以上信息提供详细的解答。"}
        ]
    )

    return response.choices[0].message.content

def main():
    group_id = str(time.time())
    consumer = KafkaConsumer('contractInfo',
        group_id = group_id,
        bootstrap_servers=['localhost:9092'],
        key_deserializer= bytes.decode,
        value_deserializer= bytes.decode,
        auto_offset_reset='latest',
        auto_commit_interval_ms=5000)
    for msg in consumer:
        logger.info(msg.value)
        value = json.loads(msg.value)
        # weixin_info(value, warning_token)
        symbol = value['s']
        if value['cs'] == 'PENDING_TRADING':
            symbols = json.loads(r.get('PENDING_TRADING'))
            if symbol not in symbols:
                ot = time.strftime('%Y年%m月%d日 %H时%M分', time.localtime(value['ot']/1000))
                symbols.append(symbol)
                r.set('PENDING_TRADING', json.dumps(symbols))
                # weixin_info(f'{symbol} 合约即将于 {ot} 上市!', warning_token)
                feishu_info(f'{symbol} 合约即将于 {ot} 上市!', fs_token)

                user_query = f"{symbol}币"
                user_question = f'介绍一下 {symbol}, 会在哪些平台上架，什么时候上架等等'
                answer = chat_with_gpt4o(user_query, user_question)
                # weixin_info(answer, warning_token)
                feishu_info(answer, fs_token)


        if value['cs'] == 'PRE_SETTLE' or value['dt'] < 4133404800000 :
            symbols = json.loads(r.get('PRE_SETTLE'))
            if symbol not in symbols:
                symbols.append(symbol)
                r.set('PRE_SETTLE', json.dumps(symbols))
                settle_time = time.strftime('%Y年%m月%d日 %H时%M分', time.localtime(value['dt']/1000))
                # weixin_info(f'合约交割通知：\n{symbol} 合约即将交割，交割时间: {settle_time}，请做好清仓准备！', warning_token)

        

if __name__ == "__main__":
    main()