# -*- coding: utf-8 -*
import time, json, os
import redis
import pandas as pd
import numpy as np
import math
from FuturesApi import FuturesApi
from util import *
from constant import *

r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
fapi = FuturesApi(api_key, api_secret)
notional = 0

def get_volumes():
    ret = {}
    for s in futures_trade_symbols:
        klines = r.lrange(f'futures_{s}_1m', 0, 60*24)
        klines = [json.loads(k) for k in klines]
        volumes = [k['q'] for k in klines]
        ret[s] = int(sum(volumes) / 10000)
    sorted_ret = sorted(ret.items(), key=lambda item: item[1], reverse=True)
    info = ['成交量通知：']
    for i in sorted_ret:
        info.append(f'{i[0]}: {i[1]}')
    local_time = time.localtime(time.time())
    z = time.strftime("%H", local_time)
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    info.append(f'通知时间：{tstr}')
    text = '\n'.join(info)
    weixin_info(text, wave_token)

def get_interest():
    tmp = fapi.get_exchangeInfo()
    tmp = tmp['symbols']
    symbols = []
    for i in tmp:
        s = i['symbol']
        if s.endswith('USDT'):
            symbols.append(s)
    intest = []
    for s in symbols:
        intest += fapi.get_openInterestHist(s, '5m', 25)
    df = pd.json_normalize(intest)
    ts = time.time()
    local_time = time.localtime(ts)
    z = time.strftime("%Y-%m-%d", local_time)
    interestPath = f'/root/data/openInterest/{z}.csv'
    df.to_csv(interestPath, index=False, mode='a', header= not os.path.exists(interestPath))

def set_exchange_info():
    exchange_info = fapi.get_exchangeInfo()
    symbols = exchange_info['symbols']
    futures_symbols = []
    for info in symbols:
        if info['status'] != 'TRADING':
            continue
        symbol = info['symbol']
        futures_symbols.append(symbol)
        r.set(f'{symbol}_info', json.dumps(info))
        r.set(f'futures_{symbol}_info', json.dumps(info))
    r.set('futures_all_symbols', json.dumps(futures_symbols))


def set_spot_exchange_info():
    exchange_info = fapi.get_spot_exchangeInfo()
    symbols = exchange_info['symbols']
    spot_symbols = []
    for info in symbols:
        symbol = info['symbol']
        spot_symbols.append(symbol)
        r.set(f'spot_{symbol}_info', json.dumps(info))
    r.set('spot_all_symbols', json.dumps(spot_symbols))

def set_weights_history():
    time.sleep(60 * 5)
    tmp = json.loads(r.get('trph-0514'))
    weight = {i['symbol']:round(i['weight'],6) for i in tmp}
    r.lpush('weights_list', json.dumps(weight))
    r.ltrim('weights_list', 0, 24 * 360)

def main():
    local_time = time.localtime(time.time())
    hr = time.strftime("%H", local_time)
    if hr in ['08', '20']:
        get_volumes()
    
    set_exchange_info()
    set_spot_exchange_info()
    get_interest()
    set_weights_history()


if __name__ == '__main__':
    main()