import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import numpy as np
from datetime import datetime

from util import *
from constant import *


logger = logging.getLogger("SmallGoal-info")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler(f'/root/workspace/trade/{group}/log/log.info')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class info():
    def __init__(self):
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        
    def run(self):
        group_id = 'weixin_info'
        consumer = KafkaConsumer('info',
            group_id = group_id,
            auto_offset_reset='latest', #earliest
            key_deserializer= bytes.decode,
            value_deserializer= bytes.decode,
            auto_commit_interval_ms=1000)

        for msg in consumer:
            text = msg.value
            token = msg.key
            if 'feishu' in token:
                token = token.split(':')[1]
                feishu_info(text, token)
            else:
                weixin_info(text,token)
                text = text.replace('\n', ',')
                logger.info(text)

            
    
if __name__ == "__main__":
    while True:
        try:
            t = info()
            t.run()
        except Exception as e:
            logger.error(e)
