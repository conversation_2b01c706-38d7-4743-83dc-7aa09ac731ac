import pandas as pd
import numpy as np
import json
import time
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.pyplot import *
from backtesting import Strategy, Backtest


trade_symbols = ['1000SHIBUSDT','1INCHUSDT', 'AAVEUSDT', 'ADAUSDT', 'ALGOUSDT', 'ALICEUSDT', 'ALPHAUSDT', 'ANTUSDT', 'APEUSDT', 'API3USDT', 'ARUSDT', 'ATOMUSDT', 'AUDIOUSDT', 'AVAXUSDT', 'AXSUSDT', 'BAKEUSDT', 'BCHUSDT', 'BELUSDT', 'BNBUSDT', 'BTCUSDT', 'CELOUSDT', 'CELRUSDT', 'CHRUSDT', 'CHZUSDT', 'COTIUSDT', 'CRVUSDT', 'DARUSDT', 'DOGEUSDT', 'DOTUSDT', 'DYDXUSDT', 'EGLDUSDT', 'ENJUSDT', 'ENSUSDT', 'EOSUSDT', 'ETCUSDT', 'ETHUSDT', 'FILUSDT', 'FTMUSDT', 'GALUSDT', 'GALAUSDT', 'GMTUSDT', 'GRTUSDT', 'IMXUSDT', 'IOSTUSDT', 'JASMYUSDT', 'KAVAUSDT', 'KNCUSDT', 'LINAUSDT', 'LINKUSDT', 'LRCUSDT', 'LTCUSDT', 'MANAUSDT', 'MATICUSDT', 'MTLUSDT', 'NEARUSDT', 'OGNUSDT', 'ONEUSDT', 'PEOPLEUSDT', 'ROSEUSDT', 'RSRUSDT', 'RUNEUSDT', 'SANDUSDT',  'SKLUSDT', 'SNXUSDT', 'SOLUSDT', 'SUSHIUSDT', 'SXPUSDT', 'THETAUSDT', 'TRXUSDT', 'UNIUSDT', 'VETUSDT', 'WAVESUSDT', 'XLMUSDT', 'XMRUSDT', 'XRPUSDT', 'XTZUSDT', 'YFIUSDT', 'ZECUSDT', 'ZILUSDT', 'ZRXUSDT']

kline1s_symls = ['BTC','1INCH','AAVE','ADA','ALGO','ALICE','ALPHA','ANKR','ANT','APE','API3','ARPA','AR','ATA','ATOM','AUDIO','AVAX','AXS','BAKE','BAL','BAND','BAT','BCH','BEL','BLZ','BNB','C98','CELO','CELR','CHR','CHZ','COMP','COTI','CRV','CTK','CTSI','DAR','DASH','DENT','DGB','DOGE','DOT','DUSK','DYDX','EGLD','ENJ','ENS','EOS','ETC','ETH','FIL','FLM','FLOW','FTM','GALA','GAL','GMT','GRT','GTC','HBAR','HOT','ICP','ICX','IMX','IOST','IOTA','IOTX','JASMY','KAVA','KLAY','KNC','KSM','LINA','LINK','LIT','LPT','LRC','LTC','MANA','MASK','MATIC','MKR','MTL','NEAR','NEO','NKN','OCEAN','OGN','OMG','ONE','ONT','OP','PEOPLE','QTUM','RAY','REEF','REN','RLC','ROSE','RSR','RUNE','RVN','SAND','SFP','SHIB','SKL','SNX','SOL','STMX','STORJ','SUSHI','SXP','THETA','TOMO','TRB','TRX','UNFI','UNI','VET','WAVES','WOO','XEM','XLM','XMR','XRP','XTZ','YFI','ZEC','ZEN','ZIL','ZRX','INJ','STG','SPELL','LDO','CVX','APT','QNT']

futures_symls = ['BTC','1000SHIB','1INCH','AAVE','ADA','ALGO','ALICE','ALPHA','ANKR','ANT','APE','API3','ARPA','AR','ATA','ATOM','AUDIO','AVAX','AXS','BAKE','BAL','BAND','BAT','BCH','BEL','BLZ','BNB','C98','CELO','CELR','CHR','CHZ','COMP','COTI','CRV','CTK','CTSI','DAR','DASH','DENT','DGB','DOGE','DOT','DUSK','DYDX','EGLD','ENJ','ENS','EOS','ETC','ETH','FIL','FLM','FLOW','FTM','GALA','GAL','GMT','GRT','GTC','HBAR','HOT','ICP','ICX','IMX','IOST','IOTA','IOTX','JASMY','KAVA','KLAY','KNC','KSM','LINA','LINK','LIT','LPT','LRC','LTC','MANA','MASK','MATIC','MKR','MTL','NEAR','NEO','NKN','OCEAN','OGN','OMG','ONE','ONT','OP','PEOPLE','QTUM','REEF','REN','RLC','ROSE','RSR','RUNE','RVN','SAND','SFP','SKL','SNX','SOL','STMX','STORJ','SUSHI','SXP','THETA','TOMO','TRB','TRX','UNFI','UNI','VET','WAVES','WOO','XEM','XLM','XMR','XRP','XTZ','YFI','ZEC','ZEN','ZIL','ZRX','INJ','STG','SPELL','LDO','CVX','APT','QNT','BLUEBIRD','FOOTBALL','DEFI']

all_symbols = trade_symbols + ['MAGICUSDT']

symbols = trade_symbols

trade_symls = [s[:-4] for s in trade_symbols]
all_symls = [s[:-4] for s in all_symbols]


def plot_fig(df, s):
    data = df.copy()
    data.reset_index(drop=True,inplace=True)
    plt.figure(figsize=(15, 5), dpi=200)
    data.loc[:, s] = data[s] / data.loc[0, s]
    plt.plot(data.index, data[s], label=s)
    plt.scatter(data.index, data[s])
    plt.legend()
    plt.show()

def read_kline(data_dir, symbol, i, start=None, end=None):
    symbol = symbol.upper()
    if i == '1s':
        month = data_dir.split('/')[-2]
        path = data_dir + symbol + '_'+ month +'.parquet'
    else:
        path = data_dir + symbol + '.parquet'
    df = pd.read_parquet(path)
    df = df[df['i']==i]
    if start:
        df = df[df['z'] > start]
    if end:
        df = df[df['z'] < end]
    df = df.reset_index(drop=True)
    return df

def read_column(data_dir, symbol, i, col='c', start=None, end=None, ):
    #path = data_dir + symbol + 'USDT.csv'
    if i == '1s':
        month = data_dir.split('/')[-2]
        path = data_dir + symbol + '_'+ month +'.parquet'
    else:
        path = data_dir + symbol + '.parquet'
    df = pd.read_parquet(path)
    #df = pd.read_csv(path)
    df = df[df['i']==i]
    if start:
        df = df[df['z'] > start]
    if end:
        df = df[df['z'] < end]
    df = df.rename(columns={'z':'date', col:symbol})
    df.set_index(['date'], inplace=True)
    if col == 'q':
        df = df.round(0)
    return df[[symbol]]

# periods 算变化率
def get_rate(df, periods, threshold):
    data = df.copy()
    columns = df.columns.values.tolist()
    for s in columns:
        tmp = data[s].shift(periods=periods)
        data[s] = (data[s] / tmp - 1) * 100
    tmp = data[columns]
    data['min'] = tmp.apply(np.min, axis = 1)
    data['max'] = tmp.apply(np.max, axis = 1)
    data['mean'] = tmp.apply(np.mean, axis = 1)
    def fun(x):
        pos = len([i for i in x if i > threshold])
        neg = len([i for i in x if i < -threshold])
        if pos > neg:
            return pos
        else:
            return -neg

    data['wave'] = tmp.apply(fun, axis=1)
    data = data.fillna(0)
    return data[columns + ['min', 'max', 'mean', 'wave']]


def get_vol(df, periods, threshold):
    data = df.copy()
    columns = df.columns.values.tolist()
    for s in columns:
        data[s] = data[s].rolling(periods).mean() / data[s].rolling(periods*120).mean()
    tmp = data[columns]
    data['min'] = tmp.apply(np.min, axis = 1)
    data['max'] = tmp.apply(np.max, axis = 1)
    data['mean'] = tmp.apply(np.mean, axis = 1)
    def fun(x):
        pos = len([i for i in x if i > threshold])
        return pos
    data['times'] = tmp.apply(fun, axis=1)
    data = data.fillna(0)
    return data[columns + ['min', 'max', 'mean', 'times']]


def start_end(s, before=60, after=60):
    form = "%Y-%m-%d %H:%M:%S"
    date = datetime.strptime(s, form)
    ts = date.timestamp()
    start = ts - 60 * before
    end = ts + 60 * after
    start = time.localtime(start)
    end = time.localtime(end)
    start = time.strftime("%Y-%m-%d %H:%M:%S", start)
    end = time.strftime("%Y-%m-%d %H:%M:%S", end)
    return start, end

def load_data_bt(path, i=None, start=None, end=None, base_time=None):
    df = pd.read_parquet(path)
    if start and end:
        df = df[(df['z'] >= start) & (df['z'] <= end)]
    if i:
        df = df[df['i'] == i]
    df = df[['o','l','h','c','v','z']]
    df = df.rename(columns={'o':'Open','l':'Low','h':'High','c':'Close','v':'Volume','z':'date'})
    df.set_index('date', inplace=True, drop=True)
    if base_time != None:
        base_price = df.loc[base_time, 'Open']
        df[['Open','Low','High','Close']] = df[['Open','Low','High','Close']] / base_price
    df.index = pd.to_datetime(df.index)
    df.reset_index(inplace=True)
    return df

def get_features(df):
    n = 30
    # 过去时段的涨跌幅
    df['1m'] = (df['c'] / df['c'].shift(periods=1) - 1) * 100
    df['3m'] = (df['c'] / df['c'].shift(periods=3) - 1) * 100
    df['5m'] = (df['c'] / df['c'].shift(periods=5) - 1) * 100
    df['30m'] = (df['o'] / df['o'].shift(periods=30) - 1) * 100

    # 过去的涨跌
    df['pos'] = df['1m'].apply(lambda x :1 if x>0 else 0)
    df['5m_pos'] = df['pos'].rolling(6).sum() -1

    df['high'] = df['o'].rolling(n).max()
    df['low'] = df['o'].rolling(n).min()
    df['10o_max'] = df['o'].rolling(10).max()
    df['10o_min'] = df['o'].rolling(10).min()
    df['mid'] = df['o']*0.6 + df['c']*0.4

    # kline和交易量比值
    df['shade'] = abs(df['c']-df['o']) / (df['h']-df['l'] + 1e-8)
    df['v30'] = df['q'] / df['q'].rolling(30).mean().round(1)

    # 未来的return
    df['f1m'] = (df['c'].shift(periods=-1)/df['c'] - 1) * 100
    df['f3m'] = (df['c'].shift(periods=-3)/df['c'] - 1) * 100
    df['f5m'] = (df['c'].shift(periods=-5)/df['c'] - 1) * 100
    df['f10m'] = (df['c'].shift(periods=-10)/df['c'] - 1) * 100
    df['f30m'] = (df['c'].shift(periods=-30)/df['c'] - 1) * 100
    return df

def SMA(values, n):
    return pd.Series(values).rolling(n).mean()

def EMA(values, alpha):
    return pd.Series(values).ewm(alpha=alpha, adjust=False).mean()

def Vol(values, n):
    return pd.Series(values)/pd.Series(values).rolling(n).mean()

def RATE(values):
    df = pd.Series(values)
    return (df - df.shift(periods=1)) / df * 100

class plot_bt(Strategy):
    n1 = 10
    n2 = 30
    
    def init(self):
        #self.sma1 = self.I(SMA, self.data.Close, self.n1)
        #self.sma2 = self.I(SMA, self.data.Close, self.n2)
        self.rate1 = self.I(RATE, self.data.Close)
    
    def next(self):
        if self.rate1 > 100:
            self.position.close()
            self.buy()
        elif self.rate1 < -100:
            self.position.close()
            self.sell()

def aggtrade2kline1s(data):
    # 生成完整的时间序列,包括没有成交的秒
    start_time = pd.to_datetime(data['z'].min()).floor('s')
    end_time = pd.to_datetime(data['z'].max()).floor('s') 
    full_time = pd.date_range(start=start_time, end=end_time, freq='s')
    full_time = pd.DataFrame({'z': full_time.strftime('%Y-%m-%d %H:%M:%S')})

    data['z'] = pd.to_datetime(data['z']).dt.strftime('%Y-%m-%d %H:%M:%S')
    kline_1s = pd.DataFrame()

    # 计算每秒的开高低收和成交量
    kline_1s['o'] = data.groupby('z')['p'].first()
    kline_1s['h'] = data.groupby('z')['p'].max() 
    kline_1s['l'] = data.groupby('z')['p'].min()
    kline_1s['c'] = data.groupby('z')['p'].last()
    kline_1s['v'] = data.groupby('z')['v'].sum()
    kline_1s['q'] = data.groupby('z')['q'].sum()

    # 重置索引,将时间列从索引变为普通列
    kline_1s = kline_1s.reset_index()

    # 与完整时间序列合并,填充没有成交的时间点
    kline_1s = pd.merge(full_time, kline_1s, on='z', how='left')

    # 用前一秒的收盘价填充没有成交的时间点
    kline_1s['c'] = kline_1s['c'].ffill()
    kline_1s['o'] = kline_1s['o'].fillna(kline_1s['c'])
    kline_1s['h'] = kline_1s['h'].fillna(kline_1s['c'])
    kline_1s['l'] = kline_1s['l'].fillna(kline_1s['c'])
    kline_1s['v'] = kline_1s['v'].fillna(0)
    kline_1s['q'] = kline_1s['q'].fillna(0)

    # 按时间排序
    kline_1s = kline_1s.sort_values('z')
    kline_1s = kline_1s.reset_index(drop=True)

    return kline_1s


def get_minute_data(group):
    # 对每个分钟内的数据,计算从0秒到当前秒的kline
    minute_data = pd.DataFrame()
    curr_data = []
    
    # 按秒遍历group数据
    for _, row in group.iterrows():
        s = row['second']
        
        # 第一条数据
        if len(curr_data) == 0:
            minute_data.loc[s, 'o'] = row['o']
            minute_data.loc[s, 'h'] = row['h']
            minute_data.loc[s, 'l'] = row['l']
        else:
            minute_data.loc[s, 'o'] = minute_data.loc[s-1, 'o']
            minute_data.loc[s, 'h'] = max(minute_data.loc[s-1, 'h'], row['h'])
            minute_data.loc[s, 'l'] = min(minute_data.loc[s-1, 'l'], row['l'])
        
        minute_data.loc[s, 'c'] = row['c']
        minute_data.loc[s, 'v'] = row['v'] if len(curr_data) == 0 else minute_data.loc[s-1, 'v'] + row['v']
        minute_data.loc[s, 'q'] = row['q'] if len(curr_data) == 0 else minute_data.loc[s-1, 'q'] + row['q']
        
        curr_data.append(row)
        
    return minute_data

def kline1s2kline1m(kline_1s):
    # 将时间列转换为datetime格式
    kline_1s['z'] = pd.to_datetime(kline_1s['z'])
    
    # 按分钟分组,计算每个时间点到当前分钟开始的开高低收
    kline_1s['minute'] = kline_1s['z'].dt.strftime('%Y-%m-%d %H:%M')
    kline_1s['second'] = kline_1s['z'].dt.second
    
    # 按分钟分组处理数据
    # 将数据按分钟分组并转换为列表
    grouped = kline_1s.groupby('minute').parallel_apply(get_minute_data)
    grouped = grouped.reset_index()
    grouped['z'] = grouped['minute'] + ':' + grouped['level_1'].astype(str).str.zfill(2)
    
    # 整理最终数据
    kline_1m = grouped[['z','o','h','l','c','v','q']]
    kline_1m['z'] = pd.to_datetime(kline_1m['z'])
    kline_1m = kline_1m.sort_values('z')
    
    return kline_1m