# -*- coding: utf-8 -*
import time, json
import redis
import pandas as pd
import numpy as np
from FuturesApi import FuturesApi
from util import *
from constant import *
import os
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
#from util4hyt import df_to_hytstyle2
import matplotlib.pyplot as plt

r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
fapi = FuturesApi(api_key, api_secret)
notional = 0
global info

'''
"positions": [  // 头寸，将返回所有市场symbol。
    //根据用户持仓模式展示持仓方向，即单向模式下只返回BOTH持仓情况，双向模式下只返回 LONG 和 SHORT 持仓情况
    {
        "symbol": "BTCUSDT",  // 交易对
        "initialMargin": "0",   // 当前所需起始保证金(基于最新标记价格)
        "maintMargin": "0", //维持保证金
        "unrealizedProfit": "0.00000000",  // 持仓未实现盈亏
        "positionInitialMargin": "0",  // 持仓所需起始保证金(基于最新标记价格)
        "openOrderInitialMargin": "0",  // 当前挂单所需起始保证金(基于最新标记价格)
        "leverage": "100",  // 杠杆倍率
        "isolated": true,  // 是否是逐仓模式
        "entryPrice": "0.00000",  // 持仓成本价
        "maxNotional": "250000",  // 当前杠杆下用户可用的最大名义价值
        "bidNotional": "0",  // 买单净值，忽略
        "askNotional": "0",  // 卖单净值，忽略
        "positionSide": "BOTH",  // 持仓方向
        "positionAmt": "0",      // 持仓数量
        "updateTime": 0         // 更新时间 
    }
]
'''
def get_ask_bid(symbol):
    depth = json.loads(r.get('futures_'+ symbol + '_depthUpdate'))
    gap = time.time() - depth['E']/1000
    if gap < 8:
        bid = depth['b'][0][0]
        ask = depth['a'][0][0]         
    else:
        depth = fapi.get_depth(symbol, 5)
        bid = float(depth['bids'][0][0])
        ask = float(depth['asks'][0][0])
    return bid

def df_to_hytstyle2(df, day_date):
    names = {'s':'symbol', 'o':'open', 'h':'high','l':'low','c':'close', 'v':'volume'}
    df.rename(columns=names, inplace = True)
    df['date'] = df['T'].apply(lambda x: ts2date(x))
    df['idx'] = df['T'].apply(lambda x: ts2idx(x))
    df['symbol'] = df['symbol'].apply(lambda x: x[0:-4])
    df['date'] = pd.to_datetime(df['date'])
    df = df[df['date'] == day_date]
    df = df[['date','idx','symbol','open','high','low','close','volume','q','n','V','Q']]
    df = df.set_index(['date','idx','symbol'])
    df.sort_index(inplace=True)
    return df


def check_data():
    local_time = time.localtime(time.time())
    z = time.strftime("%H", local_time)
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    key = 'spot_BTCUSDT_1m'
    kline = r.lrange(key, 0, 3)
    k = [json.loads(i)['z'] for i in kline]
    
    if len(set(k)) != 4:
        diff = 4 - len(set(k))
        text = ['数据异常通知：']
        text.append(f'diff:{diff}')
        text.append(f'通知时间：{tstr}')
        weixin_info('\n'.join(text), warning_token)

    T = json.loads(r.lindex(key, 0))['T'] / 1000
    if time.time() - T > 60:
        text = ['数据异常通知：']
        text.append('最新一条k线距离现在超过60s，如果持续，爬数据可能出现问题。')
        text.append(f'通知时间：{tstr}')
        weixin_info('\n'.join(text), warning_token)

    try:
        producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
    except Exception as e:
        print('kafka' + str(e))
        if 'NoBrokersAvailable' in str(e):
            weixin_info(str(e), warning_token)
            os.system('systemctl restart kafka')
    
    symbols = futures_symbols
    for symbol in symbols:
        key = f'futures_{symbol}_1m'
        kline = json.loads(r.lindex(key, 0))
        #if kline['q'] < 10:
        #    weixin_info(f'{symbol} q < 10', warning_token)

def generate_day_parquet(type, t):
    day_date = t
    klines = []
    if type == 'spot':
        symbols = spot_symbols
    else:
        symbols = futures_symbols
        
    for symbol in symbols:
        key = '{t}_{s}_1m'.format(t=type, s=symbol)
        k = r.lrange(key, 0, 60 * 36)
        k = list(set(k))
        k = [json.loads(i) for i in k]
        #if not check_klines(k):
        #    text = 'check_klines day failed'
        #    weixin_info(text, warning_token)
        klines += k
    df = pd.json_normalize(klines)
    df = df_to_hytstyle2(df, day_date)
    path = f'/root/data/{type}_day/{day_date}.parquet'
    df.to_parquet(path)
    
def check_parquet():
    local_time = time.localtime(time.time() - 60 * 60 * 24)
    day_date = time.strftime("%Y%m%d", local_time)
    path = f'/root/data/spot_day/{day_date}.parquet'
    if not os.path.exists(path):
        text = path + ' does not exists!'
        weixin_info(text, warning_token)


def monitor_kline():
    local_time = time.localtime(time.time())
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    minute = int(time.strftime("%M", local_time))
    futures_all_symbol = json.loads(r.get('futures_all_symbols'))
    symbols = list(set(futures_all_symbol) & set(futures_symbols))
    cos = []
    qts = []
    top_symbols = ['BTCUSDT','ETHUSDT','SOLUSDT','XRPUSDT','DOGEUSDT','1000PEPEUSDT','TRUMPUSDT','SUIUSDT','LTCUSDT','BNBUSDT','ADAUSDT']
    q_means = {}
    for symbol in top_symbols:
        key = f'futures_{symbol}_1m'
        klines = r.lrange(key, 0, 30)
        klines = [json.loads(i) for i in klines]
        q_mean = np.mean([i['q'] for i in klines])
        q_means[symbol] = q_mean
    
    r.set('top_q_means', json.dumps(q_means))
        
    top_infos = ['TOP品种 异常报警：']
    all_infos = ['全品种 异常报警：']
    for symbol in symbols:
        key = f'futures_{symbol}_1m'
        klines = r.lrange(key, 0, 30)
        klines = [json.loads(i) for i in klines]
        co = klines[0]['c'] / klines[0]['o'] - 1
        cos.append(co)
        q = klines[0]['q']
        q_mean = np.mean([i['q'] for i in klines[1:]])
        qt = min(round(q / q_mean, 2), 20)
        qts.append(qt)
        if symbol in top_symbols and qt > 8 and abs(co) > 0.003:
            text = f'{symbol}: 涨跌: {round(co*100, 2)}%, 交易倍数: {qt}，交易额:{int(q/10000)}W'
            top_infos.append(text)
    
    co_mean = round(np.mean(cos)*100, 2)
    qt_mean = round(np.mean(qts), 2)
    co_cnt = len([i for i in cos if abs(i) > 0.005])
    if co_cnt / len(cos) > 0.5 and qt_mean > 5:
        all_infos.append(f'平均涨跌：{co_mean}%')
        all_infos.append(f'监控品种：{len(symbols)}')
        all_infos.append(f'超过千五：{co_cnt}')
        all_infos.append(f'交易倍数：{qt_mean}')
        all_infos.append('通知时间：' + tstr)
    
    if len(all_infos) > 2:
        text = '\n'.join(all_infos)
        feishu_info(text, fs_token)
    
    # if len(top_infos) >= 2:
    #     top_infos.append('通知时间：' + tstr)
    #     text = '\n'.join(top_infos)
    #     feishu_info(text, fs_token)


    # co_std = np.std(cos)
    # for i in range(len(cos)):
    #     if abs(cos[i] - co_mean) > 2 * co_std:
    #         text = f'{symbols[i]}, rate: {cos[i]*100}% qt: {qts[i]}'
    #         all_infos.append(text)
            



def monitor_market():
    local_time = time.localtime(time.time())
    tstr = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
    minute = int(time.strftime("%M", local_time))

    #futures_all_symbol = json.loads(r.get('futures_all_symbols'))
    #futures_all_symbol = [x for x in futures_all_symbol if x.endswith('USDT')]
    minutes = [1, 5, 15, 60]
    thd = [0.01, 0.02, 0.05, 0.08]
    warn = [[] for i in thd]
    symbols = futures_symbols
    for symbol in symbols:
        key = f'futures_{symbol}_1m'
        #if symbol == 'INJUSDT':
        #    break
        price = json.loads(r.lindex(key, 0))['c']
        for i in range(len(minutes)):
            m = minutes[i]
            th = thd[i]
            rate = price / json.loads(r.lindex(key, m))['c'] - 1
            if abs(rate) > th:
                warn[i].append('{}, {}%'.format(symbol, round(rate * 100, 2)))
    text = ['涨跌幅异常报警：']
    for i in range(len(warn)):
        if i == 3 and minute % 5 != 0:
            continue
        x = warn[i]
        if len(x) > 0:
            if minutes[i] <= 60:
                text.append('{}分钟：'.format(minutes[i]))
            else:
                text.append('{}小时：'.format(int(minutes[i]/60)))
            text += x
            text.append('-------------------')
    text.append('通知时间：' + tstr)
    text = '\n'.join(text)
    if len(text.split('\n')) > 2:
        weixin_info(text, zhangdie_token)
    if len(warn[0]) > 40 or len(warn[1]) > 40:
        weixin_info(text, dapan_token)


def market_wave():
    rates = []
    for s in futures_symbols:
        key = f'futures_{s}_1m'
        first = json.loads(r.lindex(key, 5))
        last = json.loads(r.lindex(key, 0))
        rate = last['c'] / first['c'] - 1
        rate = max(min(rate, 0.03), -0.03)
        rates.append(rate)
    rates.sort()
    rate_ = round(np.mean(rates[2:-2]) * 100, 2)
    r.lpush('market_wave', json.dumps({'rate':rate_}))

    history = r.lrange('market_wave', 0, -1)[::-1][-288:]
    history = [json.loads(i) for i in history]
    df = pd.json_normalize(history)
    for i in range(1, len(df)):
        df.loc[i,'rate'] += df.loc[i-1, 'rate']
    plt.figure(figsize=(17, 8), dpi=60)
    plt.plot(df.index, df['rate'], label='rate')
    plt.title('rate')
    plt.legend()
    plt.grid()
    path = f'/root/data/figs/market_wave.jpg'
    plt.savefig(path, bbox_inches='tight')
    weixin_img(path, wave_token)

def trun_kline_1s():
    for s in wasee_symbols:
        r.ltrim(f'spot_{s}_1s', 0, 60 * 5)

def main():
    local_time = time.localtime(time.time())
    hr = time.strftime("%H", local_time)
    minute = int(time.strftime("%M", local_time))
    trun_kline_1s()

    time.sleep(10)
    check_data()
    if hr == '00' and minute == 0:
        local_time = time.localtime(time.time() - 60 * 60 * 18) # 18h前
        tstr = time.strftime("%Y%m%d", local_time)
        generate_day_parquet('spot', tstr)
        generate_day_parquet('futures', tstr)
    if minute in [1]:
        check_parquet()

    monitor_market()
    monitor_kline()
    if minute % 5 == 0:
        market_wave()

if __name__ == '__main__':
    main()
