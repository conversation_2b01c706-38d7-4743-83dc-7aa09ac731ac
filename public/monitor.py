import json, time, logging
import redis
from kafka import KafkaProducer, KafkaConsumer, TopicPartition
import threading
from concurrent.futures import ThreadPoolExecutor
from FuturesApi import FuturesApi
from constant import trade_symbols, spot_symbols, futures_symbols, spot_combine, futures_combine
from util import *
from multiprocessing import Process,Queue
import pandas as pd
from util4hyt import df_to_hytstyle


logger = logging.getLogger("SmallGoal-Clean")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler('/root/workspace/trade/public/log/log.clean')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)

class Clean():
    def __init__(self, type):
        self.type = type
        self.group_id = 'clean'
        self.redis = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        
        if type == 'spot':
            self.symbols = spot_combine
        else:
            self.symbols = futures_combine
        #self.trade_symbols = trade_symbols
        # type_all_symbols = json.loads(self.redis.get(f'{type}_all_symbols'))
        # self.symbols = list(set(self.symbols) & set(type_all_symbols))
        self.k_lag = True
        self.indexes = ['o', 'h', 'l', 'c', 'n', 'v', 'q', 'V', 'Q', 't', 'T', 'E', 'co', 'ho', 'hl','z']
        self.consumer_offset_key = 'futures_all_clean_offset'
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
        
        for symbol in self.symbols:
            key = '{t}_{s}_1m'.format(t=self.type, s=symbol)
            if not self.redis.exists(key):
                self.redis.lpush(key, '0')
                  
    
    def clean_aggtrade(self):
        s = self.value['s']
        key = '{t}_{s}_aggTrade'.format(t=self.type, s=s)
        self.redis.lpush(key, json.dumps(self.value))

    def run(self):
        top_symbols = ['BTCUSDT','ETHUSDT','SOLUSDT','XRPUSDT','DOGEUSDT','1000PEPEUSDT','TRUMPUSDT','SUIUSDT','LTCUSDT','BNBUSDT','ADAUSDT']
        logger.info('monitor kline starts:')
        topic = 'futures_kline'
        group_id = str(time.time())
        consumer = KafkaConsumer(topic,
                                key_deserializer= bytes.decode, 
                                value_deserializer= bytes.decode,
                                auto_offset_reset='latest', #earliest
                                group_id=group_id)
        
        for msg in consumer:
            self.msg = msg
            kline = json.loads(msg.value)
            if kline['s'] not in top_symbols:
                continue
            if int(kline['E'] % 60) <= 15:
                has_get_q_means = False
                continue
            if not has_get_q_means:
                q_means = json.loads(self.redis.get('top_q_means'))
                has_get_q_means = True
            q_mean = q_means[kline['s']]
            q = kline['q']
            qt = min(round(q / q_mean, 2), 20)
            if qt > 8:
                text = f'{kline['s']}: 交易倍数: {qt}, 交易额: {int(q/10000)}W'
                feishu_info(text, fs_token)
                logger.info(json.dumps(kline))
            

            # consumer.commit_async()
            # consumer.commit_async(callback=_on_send_response)
                

def main(q, type):
    clean = Clean(type)
    clean.run()

if __name__ == "__main__":
    types = ['spot', 'futures']
    while True:
        try:
            q = Queue()
            process_list=[]
            for type in types:
                p = Process(target=main, args=(q, type,))
                p.start()
                process_list.append(p)
            for p in process_list:
                p.join()
        except Exception as e:
            logger.error(str(e))

        logger.info('over')
        weixin_info('clean finished!', warning_token)