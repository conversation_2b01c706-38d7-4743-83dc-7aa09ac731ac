# -*- coding: utf-8 -*-
import websocket
import time, json, os, sys
import random
from websocket import create_connection
import pandas as pd
import logging
import redis
from kafka import KafkaProducer, KafkaConsumer
from multiprocessing import Process,Queue
from util import *
from constant import *

logger = logging.getLogger("SmallGoal-Sub_symbols")
logger.setLevel(level=logging.INFO)
format_str = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
sh = logging.StreamHandler()
sh.setFormatter(format_str)
fh = logging.FileHandler('/root/workspace/trade/public/log/log.symbols-sub')
fh.setFormatter(format_str)
#logger.addHandler(sh)
logger.addHandler(fh)


class sub_futures():
    def __init__(self, topic, symbols):
        self.symbols = symbols
        params = []
        indexes = []
        indexes.append('kline_1m')
        #indexes.append('depth10@100ms')

        for symbol in symbols:
            for index in indexes:
                params.append(symbol + '@' + index)
            # if topic == 'spot':
                # params.append(symbol + '@' + 'kline_1s')
                
                #params.append(symbol + '@depth5@100ms')
            if topic == 'futures':
                if symbol in ['btcusdt', 'ethusdt', 'dogeusdt', 'xrpusdt', 'solusdt']:
                    params.append(symbol + '@' + 'aggTrade')
                params.append(symbol + '@' + 'depth5@500ms')
                if symbol == 'btcusdt':
                    params.append('!contractInfo')

        self.streams = {"method": "SUBSCRIBE", "params": params, "id": 1989}

        if topic == 'spot':
            self.topic = 'spot'
            self.ws = create_connection("wss://stream.binance.com:9443/ws")
        else:
            self.topic = 'futures'
            self.ws = create_connection("wss://fstream.binance.com/ws")

        self.index_float = ['o', 'h', 'l', 'c', 'v', 'q', 'V', 'Q']
        #self.kline_x = dict.fromkeys(self.symbols, False)

        self.r = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.producer = KafkaProducer(bootstrap_servers=['localhost:9092'],
                                    key_serializer= str.encode,
                                    value_serializer= str.encode,
                                    compression_type='gzip')
    
    def get_timestr(self, ts):
        local_time = time.localtime(ts / 1000)
        z = time.strftime("%Y-%m-%d_%H:%M:%S", local_time) + '.' + str(ts)[-3:]
        #z = time.strftime("%Y-%m-%d %H:%M:%S", local_time)
        return z
    
    def normalize_kline(self, kline):
        k = kline.pop('k', None)
        kline = {**kline, **k}
        for index in self.index_float:
            kline[index] = float(kline[index])
        kline['T'] += 1
        kline['z'] = self.get_timestr(kline['E'])
        if 'B' in kline:
            del kline['B']
        return kline
    
    def do_kline(self, kline):
        if kline['k']['x'] or True:
            symbol = kline['s']
            kline = self.normalize_kline(kline)
            self.producer.send(self.topic + '_kline', key=symbol+'_' + kline['i'], value=json.dumps(kline))

        '''
        if (symbol != self.symbol and len(self.kline)>0 ) or len(self.kline) > 25:
            self.producer.send(self.topic + '_kline', key=self.symbol, value=json.dumps(self.kline))
            self.kline = []

        if kline['k']['i'] == '1m':
            self.kline_x[symbol] = kline['k']['x']

        if self.kline_x.get(symbol):
            kline = self.normalize_kline(kline)
            self.kline.append(kline)
        self.symbol = symbol
        '''

    def do_aggTrade(self, recv):
        symbol = recv['s']
        recv.pop('e')
        recv.pop('a')
        recv.pop('f')
        recv.pop('l')
        now = int(time.time() * 1000)
        recv['now'] = now
        recv['p'] = float(recv['p'])
        recv['q'] = float(recv['q'])
        recv['z'] = self.get_timestr(recv['E'])
        self.producer.send(self.topic + '_aggTrade', key=symbol, value=json.dumps(recv))
    
    def do_depthUpdate(self, recv):
        symbol = recv['s']
        recv['b'] = [[float(i) for i in j] for j in recv['b']]
        recv['a'] = [[float(i) for i in j] for j in recv['a']]
        recv['z'] = self.get_timestr(recv['E'])
        s = recv['s']
        key = f'{self.topic}_{s}_depthUpdate'
        self.r.set(key, json.dumps(recv))
        key = f'spot_{s}_depthUpdate'
        self.r.set(key, json.dumps(recv))
        self.producer.send(self.topic + '_depthUpdate', key=symbol, value=json.dumps(recv))
    
    def do_contractInfo(self, recv):
        self.producer.send('contractInfo', key='contractInfo', value=json.dumps(recv))

    def loop_run(self):
        self.ws.send(json.dumps(self.streams))
        self.ws.recv()
        logger.info(self.topic + ':' + str(self.symbols))
        while True:
            try:
                rec = self.ws.recv()
                recv = json.loads(rec)
            except Exception as e:
                logger.error('to json error ' + type + str(self.symbols) + str(e) + rec)
                #weixin_info(f'{type} {str(self.symbols)} {str(e)} {rec}', warning_token)
                if 'Expecting value: line 1' in str(e):
                    continue
                return
            event = recv['e']
            
            if event == 'kline':
                self.do_kline(recv)

            elif event == 'aggTrade':
                self.do_aggTrade(recv)

            elif event == 'depthUpdate':
                self.do_depthUpdate(recv)
            
            elif event == 'contractInfo':
                logger.info(json.dumps(recv))
                self.do_contractInfo(recv)
            else:
                logger.warning(f"未知的事件类型: {json.dumps(recv)}")
                
            

def main(q, type, symbols):
    while True:
        try:
            instance = sub_futures(type, symbols)
            instance.loop_run()
        except Exception as e:
            logger.error(type + str(symbols) + str(e))
            #if 'NoBrokersAvailable' in str(e):
            #    weixin_info(str(e), warning_token)
            #    os.system('systemctl restart kafka')

if __name__ == "__main__":
    q = Queue()
    process_list=[]
    types = ['spot', 'futures']
    for type in types:
        if type == 'futures':
            symbols = futures_combine
            # symbols = futures_symbols
        else:
            symbols = spot_combine
            # symbols = spot_symbols

        symbols = [i.lower() for i in symbols]
        random.shuffle(symbols)
        steps = len(symbols) // 12 + 1

        for i in range(steps):
            symls = symbols[i * 12 : (i+1)*12]
            p = Process(target=main, args=(q, type, symls,))
            p.start()
            process_list.append(p)
    for p in process_list:
        p.join()
    logger.info('over')
    weixin_info('sub_symbols finished!', weixin_token)
