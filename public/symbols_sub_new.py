import json
import time
import logging
import redis
from binance import ThreadedWebsocketManager
from kafka import KafkaProducer
from util import *
from constant import *
import argparse

logger = logging.getLogger("SmallGoal-SubSymbols")
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
fh = logging.FileHandler('/root/workspace/trade/public/log/log.symbols-sub-new')
fh.setFormatter(formatter)
logger.addHandler(fh)

class BinanceSubscriber:
    def __init__(self, spot_symbols, futures_symbols):
        self.spot_symbols = spot_symbols
        self.futures_symbols = futures_symbols
        self.redis_client = redis.Redis(host='localhost', password=redis_pw, decode_responses=True)
        self.producer = KafkaProducer(
            bootstrap_servers=['localhost:9092'],
            key_serializer=str.encode,
            value_serializer=str.encode,
            compression_type='gzip'
        )
        self.twm = ThreadedWebsocketManager()
        self.twm.start()
        self.index_float = ['o', 'h', 'l', 'c', 'v', 'q', 'V', 'Q']

    def get_timestr(self, ts):
        local_time = time.localtime(ts / 1000)
        return time.strftime("%Y-%m-%d_%H:%M:%S", local_time) + '.' + str(ts % 1000).zfill(3)

    def normalize_kline(self, kline):
        k = kline.pop('k', {})
        kline.update(k)
        for key in self.index_float:
            if key in kline:
                kline[key] = float(kline[key])
        kline['T'] += 1
        kline['z'] = self.get_timestr(kline['E'])
        kline.pop('B', None)
        return kline

    def handle_message(self, msg, topic):
        logger.info(f"{topic}: {json.dumps(msg)}")
        try:
            msg = msg['data']
        except Exception as e:
            print(msg)
            return
        event_type = msg.get('e', None)
        
        if event_type == 'kline':
            logger.info(f"{topic}: {json.dumps(msg)}")
            # self.handle_kline(msg, topic)
            
            
        # elif event_type == 'aggTrade':
        #     self.handle_aggtrade(msg, topic)
        # elif event_type == 'depthUpdate':
        #     self.handle_depth_update(msg, topic)
        # elif event_type == 'contractInfo':
        #     self.handle_contract_info(msg)
        # else:
        #     logger.warning(f"未知的事件类型: {event_type}")

    def handle_kline(self, msg, topic):
        symbol = msg['s']
        kline = self.normalize_kline(msg)
        topic_key = f"{symbol}_{kline['i']}"
        self.producer.send(f"{topic}_kline", key=topic_key, value=json.dumps(kline))

    def handle_aggtrade(self, msg, topic):
        symbol = msg['s']
        msg['now'] = int(time.time() * 1000)
        msg['p'] = float(msg['p'])
        msg['q'] = float(msg['q'])
        msg['z'] = self.get_timestr(msg['E'])
        for key in ['e', 'a', 'f', 'l']:
            msg.pop(key, None)
        self.producer.send(f"{topic}_aggTrade", key=symbol, value=json.dumps(msg))

    def handle_depth_update(self, msg, topic):
        symbol = msg['s']
        msg['b'] = [[float(price), float(qty)] for price, qty in msg['b']]
        msg['a'] = [[float(price), float(qty)] for price, qty in msg['a']]
        msg['z'] = self.get_timestr(msg['E'])
        key1 = f"{topic}_{symbol}_depthUpdate"
        key2 = f'spot_{symbol}_depthUpdate'
        self.redis_client.set(key1, json.dumps(msg))
        self.redis_client.set(key2, json.dumps(msg))
        self.producer.send(f"{topic}_depthUpdate", key=symbol, value=json.dumps(msg))

    def handle_contract_info(self, msg):
        self.producer.send('contractInfo', key='contractInfo', value=json.dumps(msg))

    def subscribe(self):
        spot_streams = []
        futures_streams = []
        for symbol in self.spot_symbols:
            spot_streams.append(f"{symbol}@kline_1m")
            spot_streams.append(f"{symbol}@kline_1s")

        for symbol in self.futures_symbols:
            symbol = symbol.lower()
            futures_streams.extend([
                f"{symbol}@kline_1m",
                f"{symbol}@aggTrade",
                f"{symbol}@depth5@500ms"
                ])
            if symbol == 'btcusdt':
                futures_streams.append("!contractInfo")
        logger.info(f"订阅的流：{futures_streams}")
        
        self.twm.start_multiplex_socket(callback=lambda msg: self.handle_message(msg, 'spot'), streams=spot_streams)
        self.twm.start_futures_multiplex_socket(callback=lambda msg: self.handle_message(msg, 'futures'), streams=futures_streams)

    def run(self):
        self.subscribe()
        self.twm.join()

if __name__ == "__main__":
    futures_symbols = [sym.lower() for sym in futures_symbols]
    spot_symbols = [sym.lower() for sym in spot_symbols]

    subscriber = BinanceSubscriber(spot_symbols, futures_symbols)
    try:
        subscriber.run()
    except Exception as e:
        logger.error(f"{topic} 订阅出现错误：{e}")
        weixin_info(f"{topic} 订阅出现错误：{e}", warning_token) 