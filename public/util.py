# -*- coding: utf-8 -*

import requests, json
import time
import base64
from hashlib import md5

group = 'public'

api_key = 'ynR0rmsqOzWYPIwGm5F0tK575YYlGfQTYyff5FUay3q8W6qjjOToee4Sb3gE3be7'
api_secret = '7Og5pRcumpB1Fqbs95LbsA8YOPHzQsKTmxTesMW2iQ3EbbKnaKG8gbLAmcZn0udg'
redis_pw = 'peng1234'
recv_window = 3000

weixin_token = '6f01ebb2-e61c-44b9-8183-1d4472b311b0'
account_token = 'ceacacc1-3943-42d4-837e-1326d6d907fb'
order_token = '2852902e-5037-49c3-8903-9a8f96587dbc'
warning_token='34c0705a-9d81-41fc-a56f-f19115174ee8'
order_merge_token = 'fa7c8118-48b9-4534-b81f-a755ec2f04f6'
lever_token = '29411a35-116f-4082-a75b-c99c96420c2b'
zhangdie_token = '903a411e-b059-4876-8f3e-312e8af4edb0'
wave_token = '9df48d8d-1d63-435e-bb86-788ceb6db6e9' # 大盘播报

# dingding_token = test_token

# 小意思
dapan_token = '027e50f0-1e58-4fcd-82af-537d99d8b533'

# fs
fs_token = '031dc1b6-e668-4aee-ba28-ec12d5c08bc8'


def _msg(text):
    json_text = {
        "msgtype": "text",
        "at": {
            "atMobiles": ["11111"],
            "isAtAll": False
        },
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_msg(text):
    json_text = {
        "msgtype": "text",
        "text": {
            "content": text
        }
    }
    return json_text

def _weixin_img(base64_data, md5_data):
    json_text = {
        "msgtype": "image",
        "image": {
            "base64": str(base64_data,'utf-8'),
            "md5": md5_data
        }
    }
    return json_text

def dingding_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = "https://oapi.dingtalk.com/robot/send?access_token=%s" % token
    json_text = _msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content


def _feishu_msg(text):
    json_text = {"msg_type":"text", "content":{"text":text}}
    return json_text

def feishu_info(text, token):
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/%s' % token
    json_text = _feishu_msg(text)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def weixin_img(path, token):
    hash = md5()
    img = open(path, 'rb')
    data = img.read()
    hash.update(data)
    base64_data = base64.b64encode(data)
    md5_data = hash.hexdigest()
    img.close()
    
    headers = {'Content-Type': 'application/json;charset=utf-8'}
    api_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s' % token
    json_text = _weixin_img(base64_data, md5_data)
    requests.post(api_url, json.dumps(json_text), headers=headers).content

def ts2date(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%Y-%m-%d", local_time)
    return z

def ts2idx(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%H%M", local_time)
    return int(z)

def check_klines(klines):
        for i in range(len(klines)-1):
            if klines[i]['t'] - klines[i+1]['t'] != 60 * 1000:
                print(klines[i], klines[i+1])
                return False
        return True


