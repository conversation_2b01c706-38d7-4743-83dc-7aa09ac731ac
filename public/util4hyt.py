import requests, json
import time
import redis
import pandas as pd
import numpy as np
from kafka import KafkaConsumer,TopicPartition,KafkaProducer


# trade_symbols = ['1INCHUSDT', 'AAVEUSDT', 'ADAUSDT', 'ALGOUSDT', 'ALICEUSDT', 'ALPHAUSDT', 'ANTUSDT', 'APEUSDT', 'API3USDT', 'ARUSDT', 'ATOMUSDT', 'AUDIOUSDT', 'AVAXUSDT', 'AXSUSDT', 'BAKEUSDT', 'BCHUSDT', 'BELUSDT', 'BNBUSDT', 'BNXUSDT', 'BTCUSDT', 'CELOUSDT', 'CELRUSDT', 'CHRUSDT', 'CHZUSDT', 'COTIUSDT', 'CRVUSDT', 'DARUSDT', 'DOGEUSDT', 'DOTUSDT', 'DYDXUSDT', 'EGLDUSDT', 'ENJUSDT', 'ENSUSDT', 'EOSUSDT', 'ETCUSDT', 'ETHUSDT', 'FILUSDT', 'FTMUSDT', 'GALUSDT', 'GALAUSDT', 'GMTUSDT', 'GRTUSDT', 'IMXUSDT', 'IOSTUSDT', 'JASMYUSDT', 'KAVAUSDT', 'KNCUSDT', 'LINAUSDT', 'LINKUSDT', 'LRCUSDT', 'LTCUSDT', 'MANAUSDT', 'MATICUSDT', 'MTLUSDT', 'NEARUSDT', 'OGNUSDT', 'ONEUSDT', 'PEOPLEUSDT', 'ROSEUSDT', 'RSRUSDT', 'RUNEUSDT', 'SANDUSDT', 'SKLUSDT', 'SNXUSDT', 'SOLUSDT', 'SUSHIUSDT', 'SXPUSDT', 'THETAUSDT', 'TRXUSDT', 'UNIUSDT', 'VETUSDT', 'WAVESUSDT', 'XLMUSDT', 'XMRUSDT', 'XRPUSDT', 'XTZUSDT', 'YFIUSDT', 'ZECUSDT', 'ZILUSDT', 'ZRXUSDT'] + ['SHIBUSDT']


r = redis.Redis(host='localhost', password='peng1234', decode_responses=True)
producer = KafkaProducer(bootstrap_servers=['localhost:9092'], 
                         key_serializer= str.encode,
                         value_serializer= str.encode)

trade_symbols = json.loads(r.get('trade_symbols'))

def ts2date(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%Y-%m-%d", local_time)
    return z

def ts2idx(ts):
    local_time = time.localtime(ts / 1000)
    z = time.strftime("%H%M", local_time)
    return int(z)

def df_to_hytstyle(df):
    local_time = time.localtime(time.time()) # 8h前
    day_date = time.strftime("%Y%m%d", local_time)

    names = {'s':'symbol', 'o':'open', 'h':'high','l':'low','c':'close', 'v':'volume'}
    df.rename(columns=names, inplace = True)
    df['date'] = df['T'].apply(lambda x: ts2date(x))
    df['idx'] = df['T'].apply(lambda x: ts2idx(x))
    df['symbol'] = df['symbol'].apply(lambda x: x[0:-4])
    df['date'] = pd.to_datetime(df['date'])
    df = df[df['date'] == day_date]
    
    df = df[['date','idx','symbol','open','high','low','close','volume','q','n','V','Q']]
    df = df.set_index(['date','idx','symbol'])
    df.sort_index(inplace=True)
    return df

def df_to_hytstyle2(df, day_date):
    names = {'s':'symbol', 'o':'open', 'h':'high','l':'low','c':'close', 'v':'volume'}
    df.rename(columns=names, inplace = True)
    df['date'] = df['T'].apply(lambda x: ts2date(x))
    df['idx'] = df['T'].apply(lambda x: ts2idx(x))
    df['symbol'] = df['symbol'].apply(lambda x: x[0:-4])
    df['date'] = pd.to_datetime(df['date'])
    df = df[df['date'] == day_date]
    df = df[['date','idx','symbol','open','high','low','close','volume','q','n','V','Q']]
    df = df.set_index(['date','idx','symbol'])
    df.sort_index(inplace=True)
    return df


def get_today_klines(type='spot'):
    latest = r.lindex(type + '_BTCUSDT_1m', 0)
    latest = json.loads(latest)
    T = latest['T']/1000 + 60 * 60 * 8
    minute = int(T / 60)
    day_mit = 60 * 24
    remainder = minute % day_mit
    klines = []
    for s in trade_symbols:
        key = '{type}_{s}_1m'.format(type=type, s=s)
        k = r.lrange(key, 0, remainder + 10)
        k = list(set(k))
        k = [json.loads(i) for i in k]
        klines += k
    df = pd.json_normalize(klines)
    return df_to_hytstyle(df)

def get_latest_kline(type='spot'):
    latest = []
    for s in trade_symbols:
        key = '{type}_{s}_1m'.format(type=type, s=s)
        kline = json.loads(r.lindex(key, 0))
        latest.append(kline)
    df = pd.json_normalize(latest)
    return df_to_hytstyle(df)

def send_rich_msg(money):
    producer.send('trade', key='trph', value=json.dumps(money))
    r.set('trph', json.dumps(money))

def send_grid_orders(orders):
    producer.send('trade', key='grid', value=json.dumps(orders))
    r.set('grid_orders', json.dumps(orders))


def get_grid_pa(symbol):
    p = r.get('grid_' + symbol + '_p')
    if p:
        return json.loads(p)['pa']
    else:
        return 0.0


def adjust_price_volume(symbol, price, volume):
    info = json.loads(r.get(symbol + '_info'))
    filters = info['filters']
    notional = 5
    tick_size = 0.0
    for flt in filters:
        if flt['filterType'] == 'MARKET_LOT_SIZE':
            step_size = float(flt['stepSize'])
            min_qty = float(flt['minQty'])
        if flt['filterType'] == 'MIN_NOTIONAL':
            notional = float(flt['notional'])
        if flt['filterType'] == 'PRICE_FILTER':
            tick_size = float(flt['tickSize'])

    price = int(price / tick_size) * tick_size

    volume = int(volume / step_size) * step_size
    if abs(volume) < min_qty or price * abs(volume) < notional * 1.1:
        print(f'{symbol} price: {price}  volume:{volume} is not valid! Volume should be at least {min_qty} and price * volume should be at least {notional}!')
        return price, 0
    return price, volume

def get_min_qty_notional(symbol):
    info = json.loads(r.get(symbol + '_info'))
    filters = info['filters']
    min_qty = 0
    min_notional = 5
    for flt in filters:
        if flt['filterType'] == 'MARKET_LOT_SIZE':
            min_qty = float(flt['minQty'])
        if flt['filterType'] == 'MIN_NOTIONAL':
            min_notional = float(flt['notional'])
    return min_qty, min_notional


def get_trph_positions(key):
  return json.loads(r.get(f'{key}_positions'))


def get_lastest_positions(key):
    return json.loads(r.get(f'{key}_lastest_positions'))