#!/usr/bin/env python3
import base64
import time
import json
import hmac
import hashlib
from websocket import create_connection
# 设置身份验证：
API_KEY = 'rnUSzbanaTtXzOh9iUyVXrWHIzfKS7GjB02nza6OQVnt9CZAuemQNFyA3r6Ts4TB'
private_key = 'oLn5vNBJgTXcfsaTUiGT1OdrqypmeRwagDKY8YjJUEN6nkKTu7PuaZhjLLhvF1vI'

# 设置请求参数：
params = {
    'apiKey':        API_KEY,	
    'symbol':       'DOGEUSDT',
    'side':         'BUY',
    'type':         'LIMIT',
    'timeInForce':  'GTC',
    'quantity':     '200',
    'price':        '0.25'
}
# 参数中加时间戳：
timestamp = int(time.time() * 1000) # 以毫秒为单位的 UNIX 时间戳
params['timestamp'] = timestamp
# 参数中加签名：
payload = '&'.join([f'{param}={value}' for param, value in sorted(params.items())])
# signature = base64.b64encode(private_key.sign(payload.encode('ASCII')))
b = bytearray()
b.extend(private_key.encode())
signature = hmac.new(b, msg=payload.encode('utf-8'), digestmod=hashlib.sha256).hexdigest()

params['signature'] = signature
# 发送请求：
request = {	
    'id': '1',	
    'method': 'order.place',	
    'params': params
}
ws = create_connection('wss://ws-fapi.binance.com/ws-fapi/v1')
print(time.time())
ws.send(json.dumps(request))	
result = ws.recv()	
print(result)

print(time.time())
request = {	
    'id': '2',	
    'method': 'order.place',	
    'params': params
}
ws.send(json.dumps(request))	
result =  ws.recv()	
print(result)
ws.close()	
