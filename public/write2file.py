# -*- coding: utf-8 -*-
import time, json, os, sys
import pandas as pd
from kafka import  KafkaConsumer, TopicPartition, KafkaProducer
import redis
from multiprocessing import Process,Queue

class Run_Main():
    def __init__(self, topic):

        self.group_id = 'bitcoin_' + topic
        self.topic = topic + '_depthUpdate'
        self.base_dir = '/mnt/data/depthUpdate/'
        self.file = None
        self.cur_date = None
        self.values = []
    
    def write2file(self):
        if len(self.values) == 0:
            return
        value = json.loads(self.values[0])
        if 'z' not in value:
            local_time = time.localtime(v['E'] /1000)
            z = time.strftime("%Y-%m-%d_%H:%M:%S", local_time)
            value['z'] = z
        date = value['z'].split('_')[0]
        if self.cur_date != date:
            if self.cur_date != None:
                self.file.close()
            self.cur_date = date
            self.file = open(self.base_dir + date + '.txt', 'a')
        self.file.writelines(self.values)

    def loop_run(self):
        consumer = KafkaConsumer('futures_depthUpdate',
                                group_id = self.group_id,
                                auto_offset_reset='latest',
                                key_deserializer= bytes.decode,
                                value_deserializer= bytes.decode)
        
        while True:
            data = consumer.poll()
            self.values = []
            for keys,values in data.items():
                for v in values:
                    value = json.loads(v.value)
                    value['b'] = value['b'][0:10]
                    value['a'] = value['a'][0:10]
                    self.values.append(json.dumps(value) + '\n')
            self.write2file()
            consumer.commit_async()            

def sub_coin(q, topic):
    instance = Run_Main(topic)
    instance.loop_run()


if __name__ == "__main__":
    q = Queue()
    process_list = []
    topics = ['futures']
    for topic in topics:
        p  = Process(target=sub_coin, args=(q, topic,))
        p.start()
        process_list.append(p)
    for p in process_list:
        p.join()
    print('over')
    
