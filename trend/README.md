# DOGE趋势动量策略回测系统

## 概述

这是一个完整的数字货币（DOGE）趋势动量策略回测系统，基于2025年1-5月的历史数据进行策略开发、参数优化和回测分析。

## 系统架构

### 核心模块

1. **数据加载器** (`doge_data_loader.py`)
   - 加载和预处理DOGE 2025年数据
   - 计算技术指标（EMA、RSI、MACD、ATR等）
   - 数据质量验证

2. **策略模块** (`momentum_strategy.py`)
   - 双均线交叉策略（增强版）
   - Granville八大法则策略
   - RSI动量策略
   - 突破策略
   - MACD策略
   - 组合策略

3. **回测引擎** (`backtest_engine.py`)
   - 完整的交易执行模拟
   - 订单管理和成交逻辑
   - 风险控制（止损/止盈）
   - 性能指标计算

4. **参数优化器** (`optimizer.py`)
   - 网格搜索优化
   - 多进程并行计算
   - 步进式优化
   - 多种目标函数

5. **结果分析** (`analysis.py`)
   - 净值曲线绘制
   - 交易分析图表
   - 性能指标可视化
   - HTML报告生成

6. **主程序** (`main.py`)
   - 完整的回测流程
   - 命令行接口
   - 配置管理

## 快速开始

### 1. 数据准备

确保DOGE数据文件存在于 `/Users/<USER>/workspace/data/doge/` 目录：
```
DOGE_2025-01.parquet
DOGE_2025-02.parquet
DOGE_2025-03.parquet
DOGE_2025-04.parquet
DOGE_2025-05.parquet
```

### 2. 系统测试

运行系统测试确保所有模块正常：
```bash
python test_system.py
```

### 3. 运行回测

#### 基本用法
```bash
# 运行所有策略的完整回测
python main.py

# 运行特定策略
python main.py --strategies dual_ma rsi_momentum

# 跳过参数优化（使用配置文件参数）
python main.py --no-optimization

# 跳过结果分析（不生成图表）
python main.py --no-analysis
```

#### 配置文件
修改 `config.json` 来自定义：
- 策略参数
- 回测设置
- 优化参数
- 输出选项

## 策略说明

### 1. 双均线交叉策略 (dual_ma)
- 快速EMA(9) vs 慢速EMA(21)
- 金叉做多，死叉做空
- 可选成交量确认

### 2. RSI动量策略 (rsi_momentum)
- RSI(14)指标
- 超买超卖信号
- 动量确认

### 3. 突破策略 (breakout)
- 价格突破阻力/支撑位
- ATR动态阈值
- 成交量确认

### 4. MACD策略 (macd)
- MACD线与信号线交叉
- 直方图信号
- 价格趋势确认

### 5. 组合策略 (composite)
- 多策略信号加权
- 信号强度评分
- 灵活的权重配置

## 回测结果

系统会在 `results/` 目录生成以下文件：
- `backtest_summary.json` - 回测结果摘要
- `optimization_results.json` - 参数优化结果
- `{strategy_name}/` - 各策略详细结果
  - `trades.csv` - 交易记录
  - `portfolio.csv` - 投资组合历史
  - `plots/` - 可视化图表
  - `{strategy_name}_report.html` - HTML报告

## 性能指标

系统计算以下关键指标：
- 总收益率
- 夏普比率
- 最大回撤
- 胜率
- 平均盈亏
- 盈亏比
- 总交易次数

## 技术要求

### 必需依赖
- pandas
- numpy
- matplotlib
- talib
- sklearn
- plotly (可选，用于HTML报告)
- seaborn (可选，用于图表美化)

### 硬件要求
- 推荐8GB+ RAM
- 多核CPU（用于并行优化）
- 足够的磁盘空间存储结果

## 使用示例

### 示例1：快速回测单一策略
```bash
python main.py --strategies dual_ma --no-optimization
```

### 示例2：参数优化
```bash
python main.py --strategies dual_ma
```

### 示例3：批量策略比较
```bash
python main.py --strategies dual_ma rsi_momentum breakout macd
```

## 自定义扩展

### 添加新策略
1. 继承 `BaseStrategy` 类
2. 实现 `generate_signals()` 方法
3. 在 `MomentumStrategyManager` 中注册

### 自定义优化目标
1. 在 `ObjectiveFunction` 类中添加新方法
2. 在配置文件中指定目标函数名

### 扩展技术指标
1. 在 `DogeDataLoader.prepare_features()` 中添加新指标
2. 在策略中使用新指标

## 注意事项

1. **数据质量**：确保数据文件完整且格式正确
2. **计算资源**：参数优化可能需要较长时间
3. **结果解释**：回测结果仅供参考，不构成投资建议
4. **风险控制**：实盘交易需要额外的风险控制措施

## 故障排除

### 常见问题
1. **数据文件找不到**：检查数据路径设置
2. **内存不足**：减少数据量或优化参数组合数
3. **计算时间过长**：减少并行进程数或优化范围
4. **图表显示问题**：检查matplotlib后端设置

### 性能优化
1. 使用已处理的数据文件
2. 限制参数优化的组合数量
3. 调整并行进程数
4. 使用数据分块处理

## 版本历史

- v1.0.0 (2025-07-17): 初始版本
  - 完整的回测系统
  - 多策略支持
  - 参数优化功能
  - 可视化分析

## 贡献

欢迎提交问题和改进建议！

## 许可证

MIT License

## 作者

Claude - 2025年7月17日