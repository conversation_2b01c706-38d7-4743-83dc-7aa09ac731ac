import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 可选导入
try:
    import seaborn as sns
    HAS_SEABORN = True
    sns.set_style("whitegrid")
except ImportError:
    HAS_SEABORN = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    HAS_PLOTLY = True
except ImportError:
    HAS_PLOTLY = False

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class BacktestAnalyzer:
    """回测结果分析器"""
    
    def __init__(self, figsize: Tuple[int, int] = (15, 10)):
        self.figsize = figsize
        self.color_palette = {
            'profit': '#00ff00',
            'loss': '#ff0000',
            'neutral': '#808080',
            'buy': '#00ff00',
            'sell': '#ff0000',
            'price': '#1f77b4',
            'volume': '#ff7f0e',
            'signal': '#2ca02c'
        }
    
    def plot_equity_curve(self, 
                         portfolio_history: pd.DataFrame,
                         show_drawdown: bool = True,
                         save_path: str = None) -> None:
        """绘制净值曲线"""
        
        if portfolio_history.empty:
            print("投资组合历史数据为空")
            return
        
        fig, axes = plt.subplots(2 if show_drawdown else 1, 1, 
                                figsize=self.figsize, 
                                gridspec_kw={'height_ratios': [3, 1]} if show_drawdown else None)
        
        if not isinstance(axes, np.ndarray):
            axes = [axes]
        
        # 绘制净值曲线
        axes[0].plot(portfolio_history.index, portfolio_history['total_value'], 
                    color=self.color_palette['price'], linewidth=2, label='投资组合价值')
        
        # 添加基准线
        initial_value = portfolio_history['total_value'].iloc[0]
        axes[0].axhline(y=initial_value, color='gray', linestyle='--', alpha=0.7, label='初始资本')
        
        # 设置标题和标签
        axes[0].set_title('投资组合净值曲线', fontsize=16, fontweight='bold')
        axes[0].set_ylabel('投资组合价值', fontsize=12)
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 绘制回撤曲线
        if show_drawdown and len(axes) > 1:
            # 计算回撤
            rolling_max = portfolio_history['total_value'].expanding().max()
            drawdown = (portfolio_history['total_value'] - rolling_max) / rolling_max * 100
            
            axes[1].fill_between(portfolio_history.index, drawdown, 0, 
                               color=self.color_palette['loss'], alpha=0.3)
            axes[1].plot(portfolio_history.index, drawdown, 
                        color=self.color_palette['loss'], linewidth=1.5)
            
            axes[1].set_title('回撤曲线', fontsize=14)
            axes[1].set_ylabel('回撤 (%)', fontsize=12)
            axes[1].set_xlabel('时间', fontsize=12)
            axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"净值曲线图已保存到: {save_path}")
        
        plt.show()
    
    def plot_trades_analysis(self, 
                           trade_history: pd.DataFrame,
                           save_path: str = None) -> None:
        """绘制交易分析图"""
        
        if trade_history.empty:
            print("交易历史数据为空")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # 1. 盈亏分布
        profits = trade_history['pnl']
        winning_trades = profits[profits > 0]
        losing_trades = profits[profits < 0]
        
        axes[0, 0].hist(winning_trades, bins=30, alpha=0.7, color=self.color_palette['profit'], 
                       label=f'盈利交易 ({len(winning_trades)})')
        axes[0, 0].hist(losing_trades, bins=30, alpha=0.7, color=self.color_palette['loss'], 
                       label=f'亏损交易 ({len(losing_trades)})')
        axes[0, 0].set_title('交易盈亏分布', fontweight='bold')
        axes[0, 0].set_xlabel('盈亏')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 累积盈亏
        cumulative_pnl = profits.cumsum()
        axes[0, 1].plot(cumulative_pnl.index, cumulative_pnl, 
                       color=self.color_palette['price'], linewidth=2)
        axes[0, 1].axhline(y=0, color='gray', linestyle='--', alpha=0.7)
        axes[0, 1].set_title('累积盈亏', fontweight='bold')
        axes[0, 1].set_xlabel('交易序号')
        axes[0, 1].set_ylabel('累积盈亏')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 交易方向分布
        if 'side' in trade_history.columns:
            side_counts = trade_history['side'].value_counts()
            axes[1, 0].pie(side_counts.values, labels=side_counts.index, autopct='%1.1f%%',
                          colors=[self.color_palette['buy'], self.color_palette['sell']])
            axes[1, 0].set_title('交易方向分布', fontweight='bold')
        
        # 4. 月度盈亏
        if 'timestamp' in trade_history.columns:
            trade_history['month'] = pd.to_datetime(trade_history['timestamp']).dt.to_period('M')
            monthly_pnl = trade_history.groupby('month')['pnl'].sum()
            
            colors = [self.color_palette['profit'] if x > 0 else self.color_palette['loss'] 
                     for x in monthly_pnl.values]
            
            axes[1, 1].bar(range(len(monthly_pnl)), monthly_pnl.values, color=colors)
            axes[1, 1].set_title('月度盈亏', fontweight='bold')
            axes[1, 1].set_xlabel('月份')
            axes[1, 1].set_ylabel('盈亏')
            axes[1, 1].set_xticks(range(len(monthly_pnl)))
            axes[1, 1].set_xticklabels([str(m) for m in monthly_pnl.index], rotation=45)
            axes[1, 1].axhline(y=0, color='gray', linestyle='--', alpha=0.7)
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"交易分析图已保存到: {save_path}")
        
        plt.show()
    
    def plot_strategy_signals(self, 
                            df: pd.DataFrame,
                            signal_column: str = 'signal',
                            price_column: str = 'close',
                            volume_column: str = 'volume',
                            start_date: str = None,
                            end_date: str = None,
                            save_path: str = None) -> None:
        """绘制策略信号图"""
        
        # 数据过滤
        plot_df = df.copy()
        if start_date:
            plot_df = plot_df[plot_df['datetime'] >= start_date]
        if end_date:
            plot_df = plot_df[plot_df['datetime'] <= end_date]
        
        if plot_df.empty:
            print("绘图数据为空")
            return
        
        fig, axes = plt.subplots(3, 1, figsize=self.figsize, 
                                gridspec_kw={'height_ratios': [3, 1, 1]})
        
        # 1. 价格和信号
        axes[0].plot(plot_df['datetime'], plot_df[price_column], 
                    color=self.color_palette['price'], linewidth=1.5, label='价格')
        
        # 绘制买入信号
        buy_signals = plot_df[plot_df[signal_column] == 1]
        if not buy_signals.empty:
            axes[0].scatter(buy_signals['datetime'], buy_signals[price_column], 
                          color=self.color_palette['buy'], marker='^', s=100, 
                          label='买入信号', zorder=5)
        
        # 绘制卖出信号
        sell_signals = plot_df[plot_df[signal_column] == -1]
        if not sell_signals.empty:
            axes[0].scatter(sell_signals['datetime'], sell_signals[price_column], 
                          color=self.color_palette['sell'], marker='v', s=100, 
                          label='卖出信号', zorder=5)
        
        axes[0].set_title('价格走势与交易信号', fontsize=14, fontweight='bold')
        axes[0].set_ylabel('价格', fontsize=12)
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 2. 交易信号
        axes[1].plot(plot_df['datetime'], plot_df[signal_column], 
                    color=self.color_palette['signal'], linewidth=2, drawstyle='steps-post')
        axes[1].axhline(y=0, color='gray', linestyle='--', alpha=0.7)
        axes[1].set_title('交易信号', fontsize=12, fontweight='bold')
        axes[1].set_ylabel('信号', fontsize=12)
        axes[1].set_ylim(-1.5, 1.5)
        axes[1].grid(True, alpha=0.3)
        
        # 3. 成交量
        if volume_column in plot_df.columns:
            axes[2].bar(plot_df['datetime'], plot_df[volume_column], 
                       color=self.color_palette['volume'], alpha=0.7, width=0.8)
            axes[2].set_title('成交量', fontsize=12, fontweight='bold')
            axes[2].set_ylabel('成交量', fontsize=12)
            axes[2].grid(True, alpha=0.3)
        
        # 格式化x轴
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=7))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        axes[-1].set_xlabel('时间', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"策略信号图已保存到: {save_path}")
        
        plt.show()
    
    def plot_performance_metrics(self, 
                               metrics: Dict[str, Any],
                               save_path: str = None) -> None:
        """绘制性能指标图"""
        
        fig, axes = plt.subplots(2, 3, figsize=self.figsize)
        
        # 1. 收益率
        axes[0, 0].bar(['总收益率'], [metrics.get('total_return_pct', 0)], 
                      color=self.color_palette['profit'] if metrics.get('total_return_pct', 0) > 0 
                      else self.color_palette['loss'])
        axes[0, 0].set_title('总收益率', fontweight='bold')
        axes[0, 0].set_ylabel('收益率 (%)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 风险指标
        risk_metrics = ['夏普比率', '最大回撤']
        risk_values = [metrics.get('sharpe_ratio', 0), -metrics.get('max_drawdown', 0)]
        colors = [self.color_palette['profit'] if x > 0 else self.color_palette['loss'] 
                 for x in risk_values]
        
        axes[0, 1].bar(risk_metrics, risk_values, color=colors)
        axes[0, 1].set_title('风险指标', fontweight='bold')
        axes[0, 1].axhline(y=0, color='gray', linestyle='--', alpha=0.7)
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 交易统计
        total_trades = metrics.get('total_trades', 0)
        winning_trades = metrics.get('winning_trades', 0)
        losing_trades = metrics.get('losing_trades', 0)
        
        axes[0, 2].bar(['总交易', '盈利交易', '亏损交易'], 
                      [total_trades, winning_trades, losing_trades],
                      color=[self.color_palette['neutral'], 
                            self.color_palette['profit'], 
                            self.color_palette['loss']])
        axes[0, 2].set_title('交易统计', fontweight='bold')
        axes[0, 2].set_ylabel('交易次数')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 胜率
        win_rate = metrics.get('win_rate', 0)
        axes[1, 0].pie([win_rate, 100-win_rate], 
                      labels=['胜率', '败率'], 
                      autopct='%1.1f%%',
                      colors=[self.color_palette['profit'], self.color_palette['loss']])
        axes[1, 0].set_title('胜率分布', fontweight='bold')
        
        # 5. 平均盈亏
        avg_win = metrics.get('avg_win', 0)
        avg_loss = metrics.get('avg_loss', 0)
        axes[1, 1].bar(['平均盈利', '平均亏损'], [avg_win, avg_loss],
                      color=[self.color_palette['profit'], self.color_palette['loss']])
        axes[1, 1].set_title('平均盈亏', fontweight='bold')
        axes[1, 1].set_ylabel('金额')
        axes[1, 1].axhline(y=0, color='gray', linestyle='--', alpha=0.7)
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 盈亏比
        profit_factor = metrics.get('profit_factor', 0)
        axes[1, 2].bar(['盈亏比'], [profit_factor], 
                      color=self.color_palette['profit'] if profit_factor > 1 
                      else self.color_palette['loss'])
        axes[1, 2].set_title('盈亏比', fontweight='bold')
        axes[1, 2].axhline(y=1, color='gray', linestyle='--', alpha=0.7)
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"性能指标图已保存到: {save_path}")
        
        plt.show()
    
    def plot_optimization_results(self, 
                                optimization_results: List[Dict],
                                top_n: int = 20,
                                save_path: str = None) -> None:
        """绘制优化结果图"""
        
        if not optimization_results:
            print("优化结果为空")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(optimization_results)
        
        # 取前N个结果
        top_results = df.nlargest(top_n, 'score')
        
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # 1. 评分分布
        axes[0, 0].hist(df['score'], bins=30, alpha=0.7, color=self.color_palette['price'])
        axes[0, 0].axvline(x=top_results['score'].iloc[0], color=self.color_palette['profit'], 
                          linestyle='--', linewidth=2, label='最优评分')
        axes[0, 0].set_title('优化评分分布', fontweight='bold')
        axes[0, 0].set_xlabel('评分')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 收益率vs回撤
        scatter = axes[0, 1].scatter(df['max_drawdown'], df['total_return_pct'], 
                                   c=df['score'], cmap='RdYlGn', alpha=0.7)
        axes[0, 1].set_title('收益率 vs 最大回撤', fontweight='bold')
        axes[0, 1].set_xlabel('最大回撤 (%)')
        axes[0, 1].set_ylabel('总收益率 (%)')
        axes[0, 1].grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=axes[0, 1], label='评分')
        
        # 3. 夏普比率vs胜率
        axes[1, 0].scatter(df['win_rate'], df['sharpe_ratio'], 
                         c=df['score'], cmap='RdYlGn', alpha=0.7)
        axes[1, 0].set_title('夏普比率 vs 胜率', fontweight='bold')
        axes[1, 0].set_xlabel('胜率 (%)')
        axes[1, 0].set_ylabel('夏普比率')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 前N个策略的评分
        axes[1, 1].bar(range(len(top_results)), top_results['score'], 
                      color=self.color_palette['profit'])
        axes[1, 1].set_title(f'前{top_n}个策略评分', fontweight='bold')
        axes[1, 1].set_xlabel('策略排名')
        axes[1, 1].set_ylabel('评分')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"优化结果图已保存到: {save_path}")
        
        plt.show()
    
    def generate_html_report(self, 
                           results: Dict[str, Any],
                           portfolio_history: pd.DataFrame,
                           trade_history: pd.DataFrame,
                           save_path: str = "backtest_report.html") -> None:
        """生成HTML回测报告"""
        
        if not HAS_PLOTLY:
            print("Plotly未安装，跳过HTML报告生成")
            return
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=('净值曲线', '回撤曲线', '累积盈亏', '交易盈亏分布', '月度盈亏', '关键指标'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 1. 净值曲线
        if not portfolio_history.empty:
            fig.add_trace(
                go.Scatter(x=portfolio_history.index, y=portfolio_history['total_value'],
                          mode='lines', name='投资组合价值', line=dict(color='blue')),
                row=1, col=1
            )
        
        # 2. 回撤曲线
        if not portfolio_history.empty:
            rolling_max = portfolio_history['total_value'].expanding().max()
            drawdown = (portfolio_history['total_value'] - rolling_max) / rolling_max * 100
            
            fig.add_trace(
                go.Scatter(x=portfolio_history.index, y=drawdown,
                          mode='lines', name='回撤', line=dict(color='red'),
                          fill='tonexty'),
                row=1, col=2
            )
        
        # 3. 累积盈亏
        if not trade_history.empty:
            cumulative_pnl = trade_history['pnl'].cumsum()
            fig.add_trace(
                go.Scatter(x=trade_history.index, y=cumulative_pnl,
                          mode='lines', name='累积盈亏', line=dict(color='green')),
                row=2, col=1
            )
        
        # 4. 交易盈亏分布
        if not trade_history.empty:
            fig.add_trace(
                go.Histogram(x=trade_history['pnl'], name='盈亏分布',
                           marker_color='lightblue'),
                row=2, col=2
            )
        
        # 5. 月度盈亏
        if not trade_history.empty and 'timestamp' in trade_history.columns:
            trade_history['month'] = pd.to_datetime(trade_history['timestamp']).dt.to_period('M')
            monthly_pnl = trade_history.groupby('month')['pnl'].sum()
            
            colors = ['green' if x > 0 else 'red' for x in monthly_pnl.values]
            fig.add_trace(
                go.Bar(x=[str(m) for m in monthly_pnl.index], y=monthly_pnl.values,
                      name='月度盈亏', marker_color=colors),
                row=3, col=1
            )
        
        # 6. 关键指标
        metrics_labels = ['总收益率(%)', '夏普比率', '最大回撤(%)', '胜率(%)', '盈亏比']
        metrics_values = [
            results.get('total_return_pct', 0),
            results.get('sharpe_ratio', 0),
            results.get('max_drawdown', 0),
            results.get('win_rate', 0),
            results.get('profit_factor', 0)
        ]
        
        fig.add_trace(
            go.Bar(x=metrics_labels, y=metrics_values, name='关键指标',
                  marker_color=['green' if x > 0 else 'red' for x in metrics_values]),
            row=3, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text="回测结果综合分析",
            showlegend=False,
            height=1200
        )
        
        # 保存HTML文件
        pyo.plot(fig, filename=save_path, auto_open=False)
        print(f"HTML报告已保存到: {save_path}")
    
    def print_performance_summary(self, results: Dict[str, Any]) -> None:
        """打印性能摘要"""
        
        print("\n" + "="*60)
        print("回测结果摘要")
        print("="*60)
        
        print(f"总收益: {results.get('total_return', 0):.2f}")
        print(f"总收益率: {results.get('total_return_pct', 0):.2f}%")
        print(f"年化波动率: {results.get('volatility', 0):.2f}%")
        print(f"夏普比率: {results.get('sharpe_ratio', 0):.4f}")
        print(f"最大回撤: {results.get('max_drawdown', 0):.2f}%")
        
        print(f"\n交易统计:")
        print(f"总交易次数: {results.get('total_trades', 0)}")
        print(f"盈利交易: {results.get('winning_trades', 0)}")
        print(f"亏损交易: {results.get('losing_trades', 0)}")
        print(f"胜率: {results.get('win_rate', 0):.2f}%")
        print(f"平均盈利: {results.get('avg_win', 0):.2f}")
        print(f"平均亏损: {results.get('avg_loss', 0):.2f}")
        print(f"盈亏比: {results.get('profit_factor', 0):.2f}")
        
        print(f"\n资金统计:")
        print(f"期末价值: {results.get('final_value', 0):.2f}")
        print(f"最高价值: {results.get('max_value', 0):.2f}")
        print(f"总手续费: {results.get('total_commission', 0):.2f}")
        
        print("="*60)

if __name__ == "__main__":
    # 测试分析模块
    from doge_data_loader import DogeDataLoader
    from momentum_strategy import MomentumStrategyManager
    from backtest_engine import BacktestEngine
    
    try:
        # 加载数据
        loader = DogeDataLoader()
        df = loader.load_processed_data()
        
        # 使用小部分数据进行测试
        test_df = df.iloc[:5000].copy()
        
        # 创建策略
        manager = MomentumStrategyManager()
        manager.create_default_strategies()
        
        # 生成信号
        strategy_results = manager.run_strategy('dual_ma', test_df)
        signals = strategy_results['ma_signal']
        
        # 运行回测
        engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            position_size_type='percent',
            position_size=0.1
        )
        
        results = engine.run_backtest(test_df, signals)
        portfolio_history = engine.get_portfolio_history()
        trade_history = engine.get_trade_history()
        
        # 创建分析器
        analyzer = BacktestAnalyzer()
        
        # 打印性能摘要
        analyzer.print_performance_summary(results)
        
        # 绘制净值曲线
        if not portfolio_history.empty:
            analyzer.plot_equity_curve(portfolio_history)
        
        # 绘制交易分析
        if not trade_history.empty:
            analyzer.plot_trades_analysis(trade_history)
        
        # 绘制策略信号
        analyzer.plot_strategy_signals(
            df=test_df,
            signal_column='ma_signal',
            start_date=test_df['datetime'].iloc[0],
            end_date=test_df['datetime'].iloc[-1]
        )
        
        # 绘制性能指标
        analyzer.plot_performance_metrics(results)
        
        print("\n分析模块测试完成!")
        
    except Exception as e:
        print(f"分析模块测试失败: {str(e)}")
        raise