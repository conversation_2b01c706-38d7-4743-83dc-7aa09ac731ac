import pandas as pd
import numpy as np
import json
import time
import matplotlib.pyplot as plt
from matplotlib.pyplot import *

import sys
sys.path.insert(0, '/Users/<USER>/workspace/bitquant/trade/public/')
from jupyter import *

class backtest():
    def __init__(self):
        self.mdd_th1 = 0.005
        self.mdd_th2 = 0.01

    def get_data(self, i, symbol, start, end):
        path = f'/Users/<USER>/Public/data/bitcoin/kline/futures_pqt/{symbol.upper()}.parquet'
        df = pd.read_parquet(path)
        df = df[df['i'] == i]
        df = df[(df['z'] > start) & (df['z'] < end)]
        #df.reset_index(inplace=True)
        df['5m'] = (df['c'] / df['c'].shift(periods=1) - 1) * 100
        df['1h'] = (df['o'] / df['c'].shift(periods=12) - 1) * 100
        df['2h'] = (df['o'] / df['c'].shift(periods=24) - 1) * 100
        df['3h'] = (df['o'] / df['c'].shift(periods=36) - 1) * 100
        df['c1'] = df['c'].shift(periods=1)
        df['q'] = df['q'] / 10000
        df['ratio'] = (df['c'] -df['o']) / (df['h'] - df['l'])
        df['v'] = df['q'] / df['q'].rolling(24).mean().round(1)
        #df['v1'] = df['q'] / (df['q'].rolling(3).sum() - df['q']) * 2
        df['std12'] = df['c1'].rolling(12).std()
        df['std24'] = df['c1'].rolling(24).std()
        df['high'] = df['c1'].rolling(15).max().round(1)
        df['o_high'] = (df['o'] / df['high'] - 1) * 100
        df['c_high'] = (df['c'] / df['high'] - 1) * 100
        df = df.round({'q':0,'v':1, 'std12':0, 'std24':0, '5m':2,'1h':2,'2h':2, '3h':2, 'ratio':2, 'o_high':2, 'c_high':2})
        df = df[['z', 'o', 'q', 'c', 'ratio', '5m', '1h','2h', '3h', 'v', 'std12', 'std24', 'o_high', 'c_high']]
        df = df[(df.z > start) & (df.z < end)]
        df = df.reset_index(drop=True)
        return df
    
    def get_features(self, i, df):
        return df.loc[i,'z'],df.loc[i,'5m'],df.loc[i,'1h'],df.loc[i,'v'],df.loc[i,'o'],df.loc[i,'c'],df.loc[i,'std12'],df.loc[i,'std24']
    
    def buy_and_sell(self, df):
        position = None
        mdd_th1 = self.mdd_th1
        mdd_th2 = self.mdd_th2
        rtn = 0
        for i in range(24, len(df)):
            z,co5m,co1h,v,o,c,std12,std24 = self.get_features(i, df)
            if position == None:
                if co5m > 1 and v > 3 and co5m < 2 and co1h > -0.7:
                    buy_z,buy_c,buy_o,buy_v,buy_i,high,bottom = z,c,o,v,i,c,c*(1 - mdd_th1)
                    position = 'long'
                    mdd = 0
                    under_bottom = 0
                    under_high = 0
                    print(z, buy_c, 'buy')
                if co5m < -1 and v > 3 and co5m > -2 and co1h < 0.7:
                    buy_z,buy_c,buy_o,buy_v,buy_i,low,top = z,c,o,v,i,c,c*(1+mdd_th1)
                    position = 'short'
                    mdd = 0
                    over_top = 0
                    over_low = 0
                    print(z, buy_c, 'sell')
            if position == 'long':
                high = max(high, c)
                # 
                if c < bottom:
                    under_bottom += 1
                    if under_bottom > 6:
                        position = 'close_long'
                        reason = 'under_bottom'
                if c < buy_o:
                    position = 'close_long'
                    reanson = 'buy_o'
                if high == c:
                    under_high = 0
                if c < high * (1-mdd_th1):
                    under_high += 1
                    if under_high > 6:
                        position = 'close_long'
                        reason = 'under_high1'
                if c < high * (1-mdd_th2):
                    position = 'close_long'
                    reason = 'under_high2'
            if position == 'short':
                low = min(low, c)
                if c > top:
                    over_top += 1
                    if over_top > 6:
                        position = 'close_short'
                        reason = 'over_top'
                if c > buy_o:
                    position = 'close_short'
                    reanson = 'buy_o'
                if low == c:
                    over_low = 0
                if c > low * (1+mdd_th1):
                    over_low += 1
                    if over_low > 4:
                        position = 'close_short'
                        reason = 'over_low1'
                if c > low * (1+mdd_th2):
                    position = 'close_short'
                    reason = 'over_low2'
            if position == 'close_long':
                position = None
                profit = int(df.loc[i,'c'] - buy_c)
                print(df.loc[i,'z'], df.loc[i,'c'], 'reason:'+reason, profit)
                print('-------------------------')
                rtn += profit
            if position == 'close_short':
                position = None
                profit = int(buy_c - df.loc[i,'c'])
                print(df.loc[i,'z'], df.loc[i,'c'], 'reason:'+reason, profit)
                print('-------------------------')
                rtn += profit
        print(rtn)
    
    def run(self):
        i = '5m'
        symbol = 'btc'
        start = '2023-01-01'
        end = '2023-02-01'
        df = self.get_data(i, symbol, start, end)
        self.buy_and_sell(df)

if __name__ == "__main__":
    bt = backtest()
    bt.run()
