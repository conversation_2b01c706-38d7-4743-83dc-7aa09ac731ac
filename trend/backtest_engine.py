import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    PARTIALLY_FILLED = "partially_filled"

@dataclass
class Order:
    """订单类"""
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: float
    stop_price: Optional[float] = None
    timestamp: datetime = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0
    filled_price: float = 0
    commission: float = 0

@dataclass
class Trade:
    """交易记录"""
    id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    commission: float
    pnl: float = 0

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: float
    avg_price: float
    current_price: float
    unrealized_pnl: float = 0
    realized_pnl: float = 0
    
    @property
    def market_value(self) -> float:
        return self.quantity * self.current_price
    
    @property
    def is_long(self) -> bool:
        return self.quantity > 0
    
    @property
    def is_short(self) -> bool:
        return self.quantity < 0
    
    @property
    def is_flat(self) -> bool:
        return abs(self.quantity) < 1e-8

@dataclass
class Portfolio:
    """投资组合"""
    cash: float
    positions: Dict[str, Position]
    total_value: float = 0
    total_pnl: float = 0
    max_drawdown: float = 0
    max_value: float = 0
    
    def update_total_value(self):
        """更新总价值"""
        position_value = sum(pos.market_value for pos in self.positions.values())
        self.total_value = self.cash + position_value
        
        # 更新最大回撤
        if self.total_value > self.max_value:
            self.max_value = self.total_value
        
        current_drawdown = (self.max_value - self.total_value) / self.max_value
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, 
                 initial_capital: float = 100000,
                 commission_rate: float = 0.001,
                 slippage: float = 0.0001,
                 min_commission: float = 1.0,
                 position_size_type: str = 'fixed',  # 'fixed', 'percent', 'kelly'
                 position_size: float = 0.1,
                 max_position_size: float = 1.0,
                 enable_short: bool = True,
                 risk_free_rate: float = 0.02):
        
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.min_commission = min_commission
        self.position_size_type = position_size_type
        self.position_size = position_size
        self.max_position_size = max_position_size
        self.enable_short = enable_short
        self.risk_free_rate = risk_free_rate
        
        # 交易记录
        self.orders: List[Order] = []
        self.trades: List[Trade] = []
        self.portfolio_history: List[Portfolio] = []
        
        # 当前状态
        self.current_time = None
        self.current_data = None
        self.order_id_counter = 0
        self.trade_id_counter = 0
        
        # 风险控制
        self.stop_loss_pct = None
        self.take_profit_pct = None
        
    def reset(self):
        """重置回测状态"""
        self.orders.clear()
        self.trades.clear()
        self.portfolio_history.clear()
        self.order_id_counter = 0
        self.trade_id_counter = 0
        self.current_time = None
        self.current_data = None
    
    def set_risk_management(self, stop_loss_pct: float = None, take_profit_pct: float = None):
        """设置风险管理参数"""
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
    
    def calculate_position_size(self, signal: int, current_price: float, portfolio: Portfolio) -> float:
        """计算仓位大小"""
        if signal == 0:
            return 0
        
        available_cash = portfolio.cash
        
        if self.position_size_type == 'fixed':
            # 固定金额
            target_value = min(self.position_size * self.initial_capital, available_cash)
            return target_value / current_price
        
        elif self.position_size_type == 'percent':
            # 固定百分比
            target_value = min(self.position_size * portfolio.total_value, available_cash)
            return target_value / current_price
        
        elif self.position_size_type == 'kelly':
            # Kelly公式（简化版）
            # 这里需要历史胜率和盈亏比数据
            if len(self.trades) < 10:
                # 如果交易次数不足，使用固定百分比
                target_value = min(0.1 * portfolio.total_value, available_cash)
                return target_value / current_price
            
            # 计算胜率和平均盈亏
            winning_trades = [t for t in self.trades if t.pnl > 0]
            losing_trades = [t for t in self.trades if t.pnl < 0]
            
            if len(losing_trades) == 0:
                win_rate = 1.0
                avg_win_loss_ratio = 2.0
            else:
                win_rate = len(winning_trades) / len(self.trades)
                avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0
                avg_loss = np.mean([abs(t.pnl) for t in losing_trades]) if losing_trades else 1
                avg_win_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 1
            
            # Kelly公式
            kelly_f = (win_rate * avg_win_loss_ratio - (1 - win_rate)) / avg_win_loss_ratio
            kelly_f = max(0, min(kelly_f, self.max_position_size))
            
            target_value = min(kelly_f * portfolio.total_value, available_cash)
            return target_value / current_price
        
        return 0
    
    def create_order(self, symbol: str, side: OrderSide, quantity: float, 
                    order_type: OrderType = OrderType.MARKET, 
                    price: float = None, stop_price: float = None) -> Order:
        """创建订单"""
        order_id = f"ORDER_{self.order_id_counter:06d}"
        self.order_id_counter += 1
        
        order = Order(
            id=order_id,
            symbol=symbol,
            side=side,
            order_type=order_type,
            quantity=abs(quantity),
            price=price or self.current_data['close'],
            stop_price=stop_price,
            timestamp=self.current_time
        )
        
        self.orders.append(order)
        return order
    
    def execute_order(self, order: Order, portfolio: Portfolio) -> Optional[Trade]:
        """执行订单"""
        if order.status != OrderStatus.PENDING:
            return None
        
        current_price = self.current_data['close']
        
        # 检查订单类型和执行条件
        if order.order_type == OrderType.MARKET:
            execution_price = current_price
        elif order.order_type == OrderType.LIMIT:
            if order.side == OrderSide.BUY and current_price <= order.price:
                execution_price = order.price
            elif order.side == OrderSide.SELL and current_price >= order.price:
                execution_price = order.price
            else:
                return None  # 限价单未达到执行条件
        else:
            return None  # 暂不支持其他订单类型
        
        # 应用滑点
        if order.side == OrderSide.BUY:
            execution_price *= (1 + self.slippage)
        else:
            execution_price *= (1 - self.slippage)
        
        # 计算手续费
        commission = max(execution_price * order.quantity * self.commission_rate, 
                        self.min_commission)
        
        # 检查资金是否充足
        if order.side == OrderSide.BUY:
            required_cash = execution_price * order.quantity + commission
            if portfolio.cash < required_cash:
                order.status = OrderStatus.CANCELLED
                return None
        
        # 创建交易记录
        trade_id = f"TRADE_{self.trade_id_counter:06d}"
        self.trade_id_counter += 1
        
        trade = Trade(
            id=trade_id,
            order_id=order.id,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            price=execution_price,
            timestamp=self.current_time,
            commission=commission
        )
        
        # 更新订单状态
        order.status = OrderStatus.FILLED
        order.filled_quantity = order.quantity
        order.filled_price = execution_price
        order.commission = commission
        
        # 更新持仓
        self.update_position(trade, portfolio)
        
        self.trades.append(trade)
        return trade
    
    def update_position(self, trade: Trade, portfolio: Portfolio):
        """更新持仓"""
        symbol = trade.symbol
        
        if symbol not in portfolio.positions:
            portfolio.positions[symbol] = Position(
                symbol=symbol,
                quantity=0,
                avg_price=0,
                current_price=trade.price
            )
        
        position = portfolio.positions[symbol]
        
        if trade.side == OrderSide.BUY:
            # 买入
            if position.quantity >= 0:
                # 加仓或开多仓
                new_quantity = position.quantity + trade.quantity
                if new_quantity > 0:
                    position.avg_price = (position.avg_price * position.quantity + 
                                        trade.price * trade.quantity) / new_quantity
                position.quantity = new_quantity
            else:
                # 平空仓
                if trade.quantity >= abs(position.quantity):
                    # 完全平仓并可能反向开仓
                    close_quantity = abs(position.quantity)
                    pnl = close_quantity * (position.avg_price - trade.price)
                    position.realized_pnl += pnl
                    trade.pnl = pnl
                    
                    remaining_quantity = trade.quantity - close_quantity
                    if remaining_quantity > 0:
                        position.quantity = remaining_quantity
                        position.avg_price = trade.price
                    else:
                        position.quantity = 0
                        position.avg_price = 0
                else:
                    # 部分平仓
                    pnl = trade.quantity * (position.avg_price - trade.price)
                    position.realized_pnl += pnl
                    trade.pnl = pnl
                    position.quantity += trade.quantity
        
        else:  # SELL
            # 卖出
            if position.quantity <= 0:
                # 加仓或开空仓
                new_quantity = position.quantity - trade.quantity
                if new_quantity < 0:
                    position.avg_price = (position.avg_price * abs(position.quantity) + 
                                        trade.price * trade.quantity) / abs(new_quantity)
                position.quantity = new_quantity
            else:
                # 平多仓
                if trade.quantity >= position.quantity:
                    # 完全平仓并可能反向开仓
                    close_quantity = position.quantity
                    pnl = close_quantity * (trade.price - position.avg_price)
                    position.realized_pnl += pnl
                    trade.pnl = pnl
                    
                    remaining_quantity = trade.quantity - close_quantity
                    if remaining_quantity > 0:
                        position.quantity = -remaining_quantity
                        position.avg_price = trade.price
                    else:
                        position.quantity = 0
                        position.avg_price = 0
                else:
                    # 部分平仓
                    pnl = trade.quantity * (trade.price - position.avg_price)
                    position.realized_pnl += pnl
                    trade.pnl = pnl
                    position.quantity -= trade.quantity
        
        # 更新现金
        if trade.side == OrderSide.BUY:
            portfolio.cash -= trade.price * trade.quantity + trade.commission
        else:
            portfolio.cash += trade.price * trade.quantity - trade.commission
        
        # 更新持仓当前价格
        position.current_price = trade.price
    
    def check_risk_management(self, portfolio: Portfolio) -> List[Order]:
        """检查风险管理，返回需要执行的订单"""
        risk_orders = []
        
        if self.stop_loss_pct is None and self.take_profit_pct is None:
            return risk_orders
        
        current_price = self.current_data['close']
        
        for symbol, position in portfolio.positions.items():
            if position.is_flat:
                continue
            
            position.current_price = current_price
            
            # 计算未实现盈亏百分比
            if position.is_long:
                pnl_pct = (current_price - position.avg_price) / position.avg_price
            else:
                pnl_pct = (position.avg_price - current_price) / position.avg_price
            
            # 检查止损
            if self.stop_loss_pct and pnl_pct <= -self.stop_loss_pct:
                side = OrderSide.SELL if position.is_long else OrderSide.BUY
                order = self.create_order(
                    symbol=symbol,
                    side=side,
                    quantity=abs(position.quantity),
                    order_type=OrderType.MARKET
                )
                risk_orders.append(order)
            
            # 检查止盈
            elif self.take_profit_pct and pnl_pct >= self.take_profit_pct:
                side = OrderSide.SELL if position.is_long else OrderSide.BUY
                order = self.create_order(
                    symbol=symbol,
                    side=side,
                    quantity=abs(position.quantity),
                    order_type=OrderType.MARKET
                )
                risk_orders.append(order)
        
        return risk_orders
    
    def process_signals(self, signals: pd.Series, portfolio: Portfolio) -> List[Order]:
        """处理交易信号"""
        orders = []
        symbol = "DOGE"  # 假设交易DOGE
        current_price = self.current_data['close']
        
        if symbol not in portfolio.positions:
            portfolio.positions[symbol] = Position(
                symbol=symbol,
                quantity=0,
                avg_price=0,
                current_price=current_price
            )
        
        position = portfolio.positions[symbol]
        current_signal = signals.iloc[-1] if len(signals) > 0 else 0
        
        # 如果没有信号变化，不执行操作
        if len(signals) > 1 and current_signal == signals.iloc[-2]:
            return orders
        
        # 根据信号创建订单
        if current_signal == 1:  # 做多信号
            if position.is_short:
                # 先平空仓
                close_order = self.create_order(
                    symbol=symbol,
                    side=OrderSide.BUY,
                    quantity=abs(position.quantity),
                    order_type=OrderType.MARKET
                )
                orders.append(close_order)
            
            if not position.is_long:
                # 开多仓
                quantity = self.calculate_position_size(1, current_price, portfolio)
                if quantity > 0:
                    open_order = self.create_order(
                        symbol=symbol,
                        side=OrderSide.BUY,
                        quantity=quantity,
                        order_type=OrderType.MARKET
                    )
                    orders.append(open_order)
        
        elif current_signal == -1:  # 做空信号
            if not self.enable_short:
                # 如果不允许做空，只平多仓
                if position.is_long:
                    close_order = self.create_order(
                        symbol=symbol,
                        side=OrderSide.SELL,
                        quantity=position.quantity,
                        order_type=OrderType.MARKET
                    )
                    orders.append(close_order)
            else:
                if position.is_long:
                    # 先平多仓
                    close_order = self.create_order(
                        symbol=symbol,
                        side=OrderSide.SELL,
                        quantity=position.quantity,
                        order_type=OrderType.MARKET
                    )
                    orders.append(close_order)
                
                if not position.is_short:
                    # 开空仓
                    quantity = self.calculate_position_size(-1, current_price, portfolio)
                    if quantity > 0:
                        open_order = self.create_order(
                            symbol=symbol,
                            side=OrderSide.SELL,
                            quantity=quantity,
                            order_type=OrderType.MARKET
                        )
                        orders.append(open_order)
        
        elif current_signal == 0:  # 平仓信号
            if not position.is_flat:
                side = OrderSide.SELL if position.is_long else OrderSide.BUY
                close_order = self.create_order(
                    symbol=symbol,
                    side=side,
                    quantity=abs(position.quantity),
                    order_type=OrderType.MARKET
                )
                orders.append(close_order)
        
        return orders
    
    def run_backtest(self, df: pd.DataFrame, strategy_signals: pd.Series) -> Dict[str, Any]:
        """运行回测"""
        print(f"开始回测，数据长度: {len(df)}")
        
        # 重置状态
        self.reset()
        
        # 初始化投资组合
        portfolio = Portfolio(
            cash=self.initial_capital,
            positions={}
        )
        portfolio.update_total_value()
        
        # 逐条处理数据
        for i in range(len(df)):
            self.current_time = df.iloc[i]['datetime']
            self.current_data = df.iloc[i]
            
            # 更新持仓当前价格
            current_price = self.current_data['close']
            for position in portfolio.positions.values():
                position.current_price = current_price
                if not position.is_flat:
                    if position.is_long:
                        position.unrealized_pnl = (current_price - position.avg_price) * position.quantity
                    else:
                        position.unrealized_pnl = (position.avg_price - current_price) * abs(position.quantity)
            
            # 检查风险管理
            risk_orders = self.check_risk_management(portfolio)
            for order in risk_orders:
                self.execute_order(order, portfolio)
            
            # 处理策略信号
            if i > 0:  # 需要至少一个历史数据点
                signals = strategy_signals.iloc[:i+1]
                signal_orders = self.process_signals(signals, portfolio)
                for order in signal_orders:
                    self.execute_order(order, portfolio)
            
            # 更新投资组合价值
            portfolio.update_total_value()
            
            # 记录投资组合历史
            if i % 1440 == 0:  # 每天记录一次（1440分钟）
                portfolio_snapshot = Portfolio(
                    cash=portfolio.cash,
                    positions=portfolio.positions.copy(),
                    total_value=portfolio.total_value,
                    total_pnl=portfolio.total_value - self.initial_capital,
                    max_drawdown=portfolio.max_drawdown,
                    max_value=portfolio.max_value
                )
                self.portfolio_history.append(portfolio_snapshot)
        
        # 计算回测结果
        results = self.calculate_performance_metrics(portfolio)
        
        print(f"回测完成，总交易次数: {len(self.trades)}")
        
        return results
    
    def calculate_performance_metrics(self, portfolio: Portfolio) -> Dict[str, Any]:
        """计算回测指标"""
        if not self.portfolio_history:
            return {}
        
        # 基本指标
        total_return = portfolio.total_value - self.initial_capital
        total_return_pct = total_return / self.initial_capital * 100
        
        # 计算日收益率
        daily_values = [p.total_value for p in self.portfolio_history]
        daily_returns = pd.Series(daily_values).pct_change().dropna()
        
        # 风险指标
        volatility = daily_returns.std() * np.sqrt(252) * 100  # 年化波动率
        sharpe_ratio = (daily_returns.mean() * 252 - self.risk_free_rate) / (daily_returns.std() * np.sqrt(252)) if daily_returns.std() > 0 else 0
        
        # 最大回撤
        max_drawdown = portfolio.max_drawdown * 100
        
        # 交易指标
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        losing_trades = len([t for t in self.trades if t.pnl < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        # 盈亏比
        avg_win = np.mean([t.pnl for t in self.trades if t.pnl > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([t.pnl for t in self.trades if t.pnl < 0]) if losing_trades > 0 else 0
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # 总手续费
        total_commission = sum(t.commission for t in self.trades)
        
        return {
            'total_return': total_return,
            'total_return_pct': total_return_pct,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'total_commission': total_commission,
            'final_value': portfolio.total_value,
            'max_value': portfolio.max_value
        }
    
    def get_trade_history(self) -> pd.DataFrame:
        """获取交易历史"""
        if not self.trades:
            return pd.DataFrame()
        
        trade_data = []
        for trade in self.trades:
            trade_data.append({
                'trade_id': trade.id,
                'timestamp': trade.timestamp,
                'symbol': trade.symbol,
                'side': trade.side.value,
                'quantity': trade.quantity,
                'price': trade.price,
                'commission': trade.commission,
                'pnl': trade.pnl
            })
        
        return pd.DataFrame(trade_data)
    
    def get_portfolio_history(self) -> pd.DataFrame:
        """获取投资组合历史"""
        if not self.portfolio_history:
            return pd.DataFrame()
        
        portfolio_data = []
        for i, portfolio in enumerate(self.portfolio_history):
            portfolio_data.append({
                'period': i,
                'total_value': portfolio.total_value,
                'cash': portfolio.cash,
                'total_pnl': portfolio.total_pnl,
                'max_drawdown': portfolio.max_drawdown
            })
        
        return pd.DataFrame(portfolio_data)

if __name__ == "__main__":
    # 测试回测引擎
    from doge_data_loader import DogeDataLoader
    from momentum_strategy import MomentumStrategyManager
    
    try:
        # 加载数据
        loader = DogeDataLoader()
        df = loader.load_processed_data()
        
        # 创建策略
        manager = MomentumStrategyManager()
        manager.create_default_strategies()
        
        # 生成信号
        strategy_results = manager.run_strategy('dual_ma', df)
        signals = strategy_results['ma_signal']
        
        # 创建回测引擎
        engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            slippage=0.0001,
            position_size_type='percent',
            position_size=0.1,
            enable_short=True
        )
        
        # 设置风险管理
        engine.set_risk_management(stop_loss_pct=0.05, take_profit_pct=0.10)
        
        # 运行回测
        results = engine.run_backtest(df, signals)
        
        # 打印结果
        print("\n回测结果:")
        print(f"总收益: {results['total_return']:.2f}")
        print(f"总收益率: {results['total_return_pct']:.2f}%")
        print(f"年化波动率: {results['volatility']:.2f}%")
        print(f"夏普比率: {results['sharpe_ratio']:.2f}")
        print(f"最大回撤: {results['max_drawdown']:.2f}%")
        print(f"总交易次数: {results['total_trades']}")
        print(f"胜率: {results['win_rate']:.2f}%")
        print(f"盈亏比: {results['profit_factor']:.2f}")
        print(f"总手续费: {results['total_commission']:.2f}")
        
        print("\n回测引擎测试完成!")
        
    except Exception as e:
        print(f"回测引擎测试失败: {str(e)}")
        raise