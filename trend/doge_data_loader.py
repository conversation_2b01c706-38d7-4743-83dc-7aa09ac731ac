import pandas as pd
import numpy as np
from pathlib import Path
from typing import Optional, List, Dict
import glob
import talib
from datetime import datetime

class DogeDataLoader:
    def __init__(self, data_path: str = "/Users/<USER>/workspace/data/doge"):
        self.data_path = Path(data_path)
        
    def load_2025_data(self) -> pd.DataFrame:
        """加载2025年1-5月的DOGE数据"""
        print("开始加载2025年DOGE数据...")
        
        # 获取2025年的所有parquet文件
        pattern = str(self.data_path / "DOGE_2025-*.parquet")
        files = glob.glob(pattern)
        files.sort()
        
        if not files:
            raise FileNotFoundError(f"在路径 {self.data_path} 中未找到2025年的DOGE数据文件")
        
        print(f"找到 {len(files)} 个数据文件:")
        for file in files:
            print(f"  - {Path(file).name}")
        
        # 加载并合并所有数据
        dfs = []
        for file in files:
            df = pd.read_parquet(file)
            df = df[df['i'] == '5m']  # 使用1分钟数据
            dfs.append(df)
        
        # 合并所有数据
        combined_df = pd.concat(dfs, ignore_index=True)
        
        # 数据预处理
        processed_df = self._preprocess_data(combined_df)
        
        print(f"数据加载完成，总共 {len(processed_df)} 条记录")
        print(f"时间范围: {processed_df['datetime'].min()} 到 {processed_df['datetime'].max()}")
        
        return processed_df
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        # 重命名列名为标准格式
        df = df.rename(columns={
            'z': 'datetime',
            'o': 'open',
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume',
            'q': 'quote_volume',
            'n': 'trades',
            'V': 'taker_buy_volume',
            'Q': 'taker_buy_quote_volume'
        })
        
        # 转换时间格式
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 确保价格数据为数值类型（float64）
        price_cols = ['open', 'high', 'low', 'close']
        for col in price_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce').astype(np.float64)
        
        # 确保成交量数据为数值类型（float64）
        volume_cols = ['volume', 'quote_volume', 'trades', 'taker_buy_volume', 'taker_buy_quote_volume']
        for col in volume_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce').astype(np.float64)
        
        # 按时间排序
        df = df.sort_values('datetime').reset_index(drop=True)
        
        # 去除重复数据
        df = df.drop_duplicates(subset=['datetime']).reset_index(drop=True)
        
        return df
    
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """准备技术指标特征"""
        print("计算技术指标...")
        
        # 复制数据避免修改原始数据
        df = df.copy()
        
        # 基础价格特征
        df['price_change'] = df['close'].pct_change()
        df['price_change_abs'] = abs(df['price_change'])
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # 移动平均线
        df['ema_9'] = talib.EMA(df['close'].values, timeperiod=9)
        df['ema_21'] = talib.EMA(df['close'].values, timeperiod=21)
        df['ema_50'] = talib.EMA(df['close'].values, timeperiod=50)
        df['sma_20'] = talib.SMA(df['close'].values, timeperiod=20)
        
        # 均线交叉信号
        df['ema_cross'] = np.where(df['ema_9'] > df['ema_21'], 1, -1)
        df['ema_cross_signal'] = df['ema_cross'].diff()
        
        # RSI指标
        df['rsi'] = talib.RSI(df['close'].values, timeperiod=14)
        df['rsi_overbought'] = np.where(df['rsi'] > 70, 1, 0)
        df['rsi_oversold'] = np.where(df['rsi'] < 30, 1, 0)
        
        # 动量指标
        df['momentum'] = talib.MOM(df['close'].values, timeperiod=10)
        df['momentum_signal'] = np.where(df['momentum'] > 0, 1, -1)
        
        # MACD指标
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'].values)
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        df['macd_cross_signal'] = df['macd_cross'].diff()
        
        # 布林带
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'].values, timeperiod=20)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # ATR指标
        df['atr'] = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=14)
        df['atr_ratio'] = df['atr'] / df['close']
        
        # 成交量指标
        df['volume_ma'] = talib.SMA(df['volume'].values, timeperiod=20)
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        df['volume_spike'] = np.where(df['volume_ratio'] > 2, 1, 0)
        
        # 相对成交量
        df['volume_sma_short'] = talib.SMA(df['volume'].values, timeperiod=10)
        df['volume_sma_long'] = talib.SMA(df['volume'].values, timeperiod=30)
        df['volume_trend'] = df['volume_sma_short'] / df['volume_sma_long']
        
        # 价格波动率
        df['volatility'] = df['close'].rolling(window=20).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=100).mean()
        
        # Granville策略相关指标
        # 多种周期的移动平均线
        for period in [5, 10, 20, 30, 60]:
            df[f'sma_{period}'] = talib.SMA(df['close'].values, timeperiod=period)
            df[f'ema_{period}'] = talib.EMA(df['close'].values, timeperiod=period)
            
            # 计算均线方向
            df[f'sma_{period}_slope'] = (df[f'sma_{period}'] - df[f'sma_{period}'].shift(5)) / 5
            df[f'ema_{period}_slope'] = (df[f'ema_{period}'] - df[f'ema_{period}'].shift(5)) / 5
            
            # 计算价格偏离度
            df[f'deviation_sma_{period}'] = (df['close'] - df[f'sma_{period}']) / df[f'sma_{period}'] * 100
            df[f'deviation_ema_{period}'] = (df['close'] - df[f'ema_{period}']) / df[f'ema_{period}'] * 100
        
        # 支撑阻力位计算
        for window in [5, 10, 20]:
            df[f'support_{window}'] = df['low'].rolling(window=window).min()
            df[f'resistance_{window}'] = df['high'].rolling(window=window).max()
            
            # 价格与支撑阻力位的关系
            df[f'above_resistance_{window}'] = df['close'] > df[f'resistance_{window}'].shift(1)
            df[f'below_support_{window}'] = df['close'] < df[f'support_{window}'].shift(1)
        
        # 价格趋势强度
        df['price_trend_5'] = (df['close'] - df['close'].shift(5)) / df['close'].shift(5) * 100
        df['price_trend_10'] = (df['close'] - df['close'].shift(10)) / df['close'].shift(10) * 100
        df['price_trend_20'] = (df['close'] - df['close'].shift(20)) / df['close'].shift(20) * 100
        
        # 成交量趋势强度
        df['volume_trend_5'] = (df['volume'] - df['volume'].shift(5)) / df['volume'].shift(5) * 100
        df['volume_trend_10'] = (df['volume'] - df['volume'].shift(10)) / df['volume'].shift(10) * 100
        
        # 价格与均线的交叉状态
        df['cross_sma_20'] = np.where(df['close'] > df['sma_20'], 1, -1)
        df['cross_sma_20_change'] = df['cross_sma_20'].diff()
        
        # 均线排列状态
        df['ma_alignment'] = np.where(
            (df['ema_5'] > df['ema_10']) & 
            (df['ema_10'] > df['ema_20']) & 
            (df['ema_20'] > df['ema_30']), 1,
            np.where(
                (df['ema_5'] < df['ema_10']) & 
                (df['ema_10'] < df['ema_20']) & 
                (df['ema_20'] < df['ema_30']), -1, 0
            )
        )
        
        print("技术指标计算完成")
        
        return df
    
    def validate_data(self, df: pd.DataFrame) -> Dict[str, any]:
        """验证数据质量"""
        print("验证数据质量...")
        
        validation_results = {}
        
        # 检查缺失值
        missing_data = df.isnull().sum()
        validation_results['missing_data'] = missing_data[missing_data > 0].to_dict()
        
        # 检查时间序列连续性
        time_gaps = df['datetime'].diff().dt.total_seconds()
        expected_interval = 60  # 1分钟 = 60秒
        gaps = time_gaps[time_gaps > expected_interval * 2]  # 超过2分钟的间隔
        validation_results['time_gaps'] = len(gaps)
        
        # 检查价格数据合理性
        price_cols = ['open', 'high', 'low', 'close']
        for col in price_cols:
            validation_results[f'{col}_negative'] = (df[col] <= 0).sum()
            validation_results[f'{col}_extreme'] = ((df[col] > df[col].quantile(0.99) * 10) | 
                                                  (df[col] < df[col].quantile(0.01) / 10)).sum()
        
        # 检查OHLC逻辑
        validation_results['ohlc_logic_errors'] = (
            (df['high'] < df['low']) | 
            (df['high'] < df['open']) | 
            (df['high'] < df['close']) |
            (df['low'] > df['open']) | 
            (df['low'] > df['close'])
        ).sum()
        
        # 检查成交量数据
        validation_results['volume_negative'] = (df['volume'] < 0).sum()
        validation_results['volume_zero'] = (df['volume'] == 0).sum()
        
        print("数据质量验证完成")
        
        return validation_results
    
    def get_data_info(self, df: pd.DataFrame) -> Dict[str, any]:
        """获取数据统计信息"""
        info = {
            'total_records': len(df),
            'date_range': {
                'start': df['datetime'].min(),
                'end': df['datetime'].max()
            },
            'price_stats': {
                'min': df['close'].min(),
                'max': df['close'].max(),
                'mean': df['close'].mean(),
                'std': df['close'].std()
            },
            'volume_stats': {
                'min': df['volume'].min(),
                'max': df['volume'].max(),
                'mean': df['volume'].mean(),
                'std': df['volume'].std()
            },
            'columns': list(df.columns)
        }
        
        return info
    
    def save_processed_data(self, df: pd.DataFrame, filename: str = "doge_2025_processed.parquet"):
        """保存处理后的数据"""
        output_path = Path("/Users/<USER>/workspace/bitquant/trade/trend/data")
        output_path.mkdir(exist_ok=True)
        
        full_path = output_path / filename
        df.to_parquet(full_path, index=False)
        print(f"处理后的数据已保存到: {full_path}")
    
    def load_processed_data(self, filename: str = "doge_2025_processed.parquet") -> pd.DataFrame:
        """加载已处理的数据"""
        data_path = Path("/Users/<USER>/workspace/bitquant/trade/trend/data")
        full_path = data_path / filename
        
        if full_path.exists():
            return pd.read_parquet(full_path)
        else:
            raise FileNotFoundError(f"找不到处理后的数据文件: {full_path}")

if __name__ == "__main__":
    # 测试数据加载功能
    loader = DogeDataLoader()
    
    try:
        # 加载2025年数据
        df = loader.load_2025_data()
        
        # 计算技术指标
        df_with_features = loader.prepare_features(df)
        
        # 验证数据质量
        validation_results = loader.validate_data(df_with_features)
        print("\n数据质量验证结果:")
        for key, value in validation_results.items():
            print(f"  {key}: {value}")
        
        # 获取数据统计信息
        info = loader.get_data_info(df_with_features)
        print("\n数据统计信息:")
        print(f"  总记录数: {info['total_records']}")
        print(f"  时间范围: {info['date_range']['start']} 到 {info['date_range']['end']}")
        print(f"  价格范围: {info['price_stats']['min']:.4f} - {info['price_stats']['max']:.4f}")
        print(f"  平均价格: {info['price_stats']['mean']:.4f}")
        print(f"  平均成交量: {info['volume_stats']['mean']:.0f}")
        
        # 保存处理后的数据
        loader.save_processed_data(df_with_features)
        
        print("\n数据加载器测试完成!")
        
    except Exception as e:
        print(f"数据加载器测试失败: {str(e)}")
        raise