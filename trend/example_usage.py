#!/usr/bin/env python3
"""
DOGE趋势动量策略系统使用示例

展示如何使用系统的各个功能
"""

import sys
import time
import json
from pathlib import Path

# 导入系统模块
from doge_data_loader import DogeDataLoader
from momentum_strategy import MomentumStrategyManager, DualMovingAverageStrategy
from backtest_engine import BacktestEngine
from optimizer import ParameterOptimizer
from analysis import BacktestAnalyzer

def example_1_basic_backtest():
    """示例1：基本回测流程"""
    print("="*60)
    print("示例1：基本回测流程")
    print("="*60)
    
    # 1. 加载数据
    print("1. 加载数据...")
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    
    # 使用部分数据进行示例
    sample_df = df.iloc[:10000].copy()
    print(f"使用样本数据: {len(sample_df)} 条记录")
    
    # 2. 创建策略
    print("\n2. 创建双均线策略...")
    strategy = DualMovingAverageStrategy({
        'fast_period': 9,
        'slow_period': 21,
        'signal_type': 'ema',
        'volume_confirm': False
    })
    
    # 3. 生成信号
    print("3. 生成交易信号...")
    manager = MomentumStrategyManager()
    manager.add_strategy('dual_ma', strategy)
    strategy_results = manager.run_strategy('dual_ma', sample_df)
    signals = strategy_results['ma_signal']
    
    # 4. 运行回测
    print("4. 运行回测...")
    engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        position_size_type='percent',
        position_size=0.1
    )
    
    results = engine.run_backtest(sample_df, signals)
    
    # 5. 分析结果
    print("\n5. 分析结果...")
    analyzer = BacktestAnalyzer()
    analyzer.print_performance_summary(results)
    
    print("\n示例1完成！")
    return results

def example_2_parameter_optimization():
    """示例2：参数优化"""
    print("\n" + "="*60)
    print("示例2：参数优化")
    print("="*60)
    
    # 1. 加载数据
    print("1. 加载数据...")
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    
    # 使用更小的样本进行优化示例
    sample_df = df.iloc[:5000].copy()
    print(f"使用样本数据: {len(sample_df)} 条记录")
    
    # 2. 创建优化器
    print("\n2. 创建参数优化器...")
    optimizer = ParameterOptimizer(
        objective_function='composite_score',
        n_jobs=2,
        verbose=True
    )
    
    # 3. 定义参数网格
    param_grid = {
        'fast_period': [5, 9, 12],
        'slow_period': [20, 21, 26],
        'signal_type': ['ema', 'sma'],
        'volume_confirm': [True, False]
    }
    
    # 4. 运行优化
    print("3. 运行参数优化...")
    optimization_results = optimizer.optimize_single_strategy(
        df=sample_df,
        strategy_name='dual_ma',
        param_grid=param_grid,
        max_combinations=12  # 限制组合数量
    )
    
    # 5. 显示最优结果
    if optimization_results:
        best_result = optimization_results[0]
        print(f"\n最优参数: {best_result.params}")
        print(f"最优评分: {best_result.score:.4f}")
        print(f"总收益率: {best_result.metrics.get('total_return_pct', 0):.2f}%")
        print(f"夏普比率: {best_result.metrics.get('sharpe_ratio', 0):.4f}")
        print(f"最大回撤: {best_result.metrics.get('max_drawdown', 0):.2f}%")
    
    print("\n示例2完成！")
    return optimization_results

def example_3_strategy_comparison():
    """示例3：策略比较"""
    print("\n" + "="*60)
    print("示例3：策略比较")
    print("="*60)
    
    # 1. 加载数据
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    sample_df = df.iloc[:8000].copy()
    
    # 2. 创建策略管理器
    manager = MomentumStrategyManager()
    manager.create_default_strategies()
    
    # 3. 运行多个策略
    strategies_to_test = ['dual_ma', 'rsi_momentum', 'breakout']
    results_comparison = {}
    
    for strategy_name in strategies_to_test:
        print(f"\n测试策略: {strategy_name}")
        
        # 生成信号
        strategy_results = manager.run_strategy(strategy_name, sample_df)
        
        # 获取信号列
        signal_column = {
            'dual_ma': 'ma_signal',
            'rsi_momentum': 'rsi_signal',
            'breakout': 'breakout_signal'
        }[strategy_name]
        
        signals = strategy_results[signal_column]
        
        # 运行回测
        engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            position_size_type='percent',
            position_size=0.08
        )
        
        results = engine.run_backtest(sample_df, signals)
        results_comparison[strategy_name] = results
        
        print(f"  总收益率: {results['total_return_pct']:.2f}%")
        print(f"  夏普比率: {results['sharpe_ratio']:.4f}")
        print(f"  最大回撤: {results['max_drawdown']:.2f}%")
        print(f"  胜率: {results['win_rate']:.2f}%")
    
    # 4. 比较结果
    print("\n" + "-"*50)
    print("策略比较摘要:")
    print("-"*50)
    print(f"{'策略':<15} {'收益率':<10} {'夏普比率':<10} {'最大回撤':<10} {'胜率':<10}")
    print("-"*50)
    
    for strategy_name, results in results_comparison.items():
        print(f"{strategy_name:<15} {results['total_return_pct']:<10.2f} "
              f"{results['sharpe_ratio']:<10.4f} {results['max_drawdown']:<10.2f} "
              f"{results['win_rate']:<10.2f}")
    
    print("\n示例3完成！")
    return results_comparison

def example_4_custom_strategy():
    """示例4：自定义策略"""
    print("\n" + "="*60)
    print("示例4：自定义策略")
    print("="*60)
    
    from momentum_strategy import BaseStrategy
    import pandas as pd
    import numpy as np
    import talib
    
    class CustomMomentumStrategy(BaseStrategy):
        """自定义动量策略示例"""
        
        def __init__(self, params=None):
            if params is None:
                params = {
                    'momentum_period': 14,
                    'volatility_period': 20,
                    'momentum_threshold': 0.5,
                    'volatility_threshold': 0.02
                }
            super().__init__("CustomMomentum", params)
        
        def generate_signals(self, df):
            """生成自定义信号"""
            df = df.copy()
            
            momentum_period = self.get_param('momentum_period', 14)
            volatility_period = self.get_param('volatility_period', 20)
            momentum_threshold = self.get_param('momentum_threshold', 0.5)
            volatility_threshold = self.get_param('volatility_threshold', 0.02)
            
            # 计算动量指标
            df['momentum'] = talib.MOM(df['close'].values, timeperiod=momentum_period)
            df['momentum_pct'] = df['momentum'] / df['close'] * 100
            
            # 计算波动率
            df['volatility'] = df['close'].rolling(window=volatility_period).std()
            df['volatility_pct'] = df['volatility'] / df['close'] * 100
            
            # 生成信号
            df['custom_signal'] = 0
            
            # 做多条件：正动量且低波动
            long_condition = (df['momentum_pct'] > momentum_threshold) & (df['volatility_pct'] < volatility_threshold)
            df.loc[long_condition, 'custom_signal'] = 1
            
            # 做空条件：负动量且低波动
            short_condition = (df['momentum_pct'] < -momentum_threshold) & (df['volatility_pct'] < volatility_threshold)
            df.loc[short_condition, 'custom_signal'] = -1
            
            return df
    
    # 1. 加载数据
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    sample_df = df.iloc[:6000].copy()
    
    # 2. 创建自定义策略
    print("1. 创建自定义动量策略...")
    custom_strategy = CustomMomentumStrategy({
        'momentum_period': 10,
        'volatility_period': 15,
        'momentum_threshold': 0.3,
        'volatility_threshold': 0.025
    })
    
    # 3. 生成信号
    print("2. 生成交易信号...")
    strategy_results = custom_strategy.generate_signals(sample_df)
    signals = strategy_results['custom_signal']
    
    # 4. 运行回测
    print("3. 运行回测...")
    engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        position_size_type='percent',
        position_size=0.1
    )
    
    results = engine.run_backtest(sample_df, signals)
    
    # 5. 分析结果
    print("\n4. 分析结果...")
    analyzer = BacktestAnalyzer()
    analyzer.print_performance_summary(results)
    
    print("\n示例4完成！")
    return results

def example_5_save_and_load():
    """示例5：保存和加载结果"""
    print("\n" + "="*60)
    print("示例5：保存和加载结果")
    print("="*60)
    
    # 1. 运行一个简单的回测
    print("1. 运行回测...")
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    sample_df = df.iloc[:3000].copy()
    
    strategy = DualMovingAverageStrategy()
    manager = MomentumStrategyManager()
    manager.add_strategy('dual_ma', strategy)
    strategy_results = manager.run_strategy('dual_ma', sample_df)
    signals = strategy_results['ma_signal']
    
    engine = BacktestEngine(initial_capital=100000, commission_rate=0.001)
    results = engine.run_backtest(sample_df, signals)
    
    # 2. 保存结果
    print("2. 保存结果...")
    output_dir = Path("example_results")
    output_dir.mkdir(exist_ok=True)
    
    # 保存回测结果
    results_file = output_dir / "backtest_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # 保存交易记录
    trade_history = engine.get_trade_history()
    if not trade_history.empty:
        trades_file = output_dir / "trades.csv"
        trade_history.to_csv(trades_file, index=False)
    
    # 保存投资组合历史
    portfolio_history = engine.get_portfolio_history()
    if not portfolio_history.empty:
        portfolio_file = output_dir / "portfolio.csv"
        portfolio_history.to_csv(portfolio_file, index=False)
    
    print(f"结果已保存到: {output_dir}")
    
    # 3. 加载结果
    print("\n3. 加载结果...")
    with open(results_file, 'r') as f:
        loaded_results = json.load(f)
    
    print(f"加载的结果 - 总收益率: {loaded_results['total_return_pct']:.2f}%")
    
    if trades_file.exists():
        loaded_trades = pd.read_csv(trades_file)
        print(f"加载的交易记录: {len(loaded_trades)} 条")
    
    if portfolio_file.exists():
        loaded_portfolio = pd.read_csv(portfolio_file)
        print(f"加载的投资组合历史: {len(loaded_portfolio)} 条")
    
    print("\n示例5完成！")
    return loaded_results

def main():
    """运行所有示例"""
    print("DOGE趋势动量策略系统使用示例")
    print("="*60)
    
    examples = [
        ("基本回测流程", example_1_basic_backtest),
        ("参数优化", example_2_parameter_optimization),
        ("策略比较", example_3_strategy_comparison),
        ("自定义策略", example_4_custom_strategy),
        ("保存和加载", example_5_save_and_load)
    ]
    
    results = {}
    
    for name, func in examples:
        try:
            print(f"\n开始运行: {name}")
            start_time = time.time()
            
            result = func()
            results[name] = result
            
            elapsed = time.time() - start_time
            print(f"✓ {name} 完成，耗时: {elapsed:.2f}秒")
            
        except Exception as e:
            print(f"✗ {name} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*60)
    print("所有示例运行完成！")
    print("="*60)
    
    return results

if __name__ == "__main__":
    main()