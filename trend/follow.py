import pandas as pd
import numpy as np
from backtesting import Strategy, Backtest
import talib
from typing import List, Tuple
from sklearn.model_selection import ParameterGrid
import warnings
warnings.filterwarnings('ignore')

class VolumeBreakStrategy(Strategy):
    # 定义策略参数
    volume_window = 30  # 计算交易量均值的窗口
    volume_threshold = 10  # 交易量突破阈值
    price_window = 20  # 价格指标窗口
    rsi_window = 14  # RSI窗口
    rsi_upper = 70  # RSI超买阈值  
    rsi_lower = 30  # RSI超卖阈值
    atr_window = 14  # ATR窗口
    stop_loss = 2.0  # 止损比例
    take_profit = 4.0  # 止盈比例

    def init(self):
        # 计算交易量相关指标
        volume_series = pd.Series(self.data.Volume)
        
        def calc_sma(values, window):
            return pd.Series(values).rolling(window).mean()
        
        self.volume = self.I(lambda: self.data.Volume)
        self.volume_sma = self.I(lambda: calc_sma(self.data.Volume, self.volume_window))
        self.volume_ratio = self.I(lambda: self.data.Volume / self.volume_sma)
        
        # 计算价格相关指标
        close = pd.Series(self.data.Close)
        high = pd.Series(self.data.High) 
        low = pd.Series(self.data.Low)
        
        # 计算ATR
        self.atr = self.I(lambda: talib.ATR(high.values, low.values, 
                                           close.values, timeperiod=self.atr_window))
        
        # 计算RSI
        self.rsi = self.I(lambda: talib.RSI(close.values, timeperiod=self.rsi_window))
        
        # 计算MACD
        macd, signal, hist = talib.MACD(close.values)
        self.macd = self.I(lambda: macd)
        self.signal = self.I(lambda: signal)
        
        # 计算布林带
        upper, middle, lower = talib.BBANDS(close.values, timeperiod=self.price_window)
        self.bb_upper = self.I(lambda: upper)
        self.bb_middle = self.I(lambda: middle)
        self.bb_lower = self.I(lambda: lower)

    def next(self):
        # 检查是否有足够的历史数据
        if len(self.data) < max(self.volume_window, self.price_window):
            return
        
        # 检查指标是否已经计算完成
        if (np.isnan(self.volume_sma[-1]) or np.isnan(self.rsi[-1]) or 
            np.isnan(self.macd[-1]) or np.isnan(self.signal[-1])):
            return
        
        # 获取当前价格
        price = self.data.Close[-1]
        
        # 计算动态止损止盈点位
        stop_loss = price * (1 - self.stop_loss/100)
        take_profit = price * (1 + self.take_profit/100)
        
        # 交易量突破信号
        volume_break = self.volume_ratio[-1] > self.volume_threshold
        
        # RSI超买超卖信号
        rsi_buy = self.rsi[-1] < self.rsi_lower
        rsi_sell = self.rsi[-1] > self.rsi_upper
        
        # MACD金叉死叉信号
        macd_cross_up = self.macd[-2] < self.signal[-2] and self.macd[-1] > self.signal[-1]
        macd_cross_down = self.macd[-2] > self.signal[-2] and self.macd[-1] < self.signal[-1]
        
        # 布林带突破信号
        bb_break_up = self.data.Close[-1] > self.bb_upper[-1]
        bb_break_down = self.data.Close[-1] < self.bb_lower[-1]
        
        # 开仓逻辑
        if not self.position:  # 没有持仓
            # 做多信号
            if (volume_break and rsi_buy and macd_cross_up and not bb_break_up):
                self.buy(sl=stop_loss, tp=take_profit, size=0.1)  # 使用10%的资金开仓
                
            # 做空信号    
            elif (volume_break and rsi_sell and macd_cross_down and not bb_break_down):
                self.sell(sl=stop_loss, tp=take_profit, size=0.1)  # 使用10%的资金开仓
                
        # 持仓管理
        else:
            # 多头持仓
            if self.position.is_long:
                # 平多信号
                if rsi_sell or macd_cross_down:
                    self.position.close()
                    
            # 空头持仓    
            elif self.position.is_short:
                # 平空信号
                if rsi_buy or macd_cross_up:
                    self.position.close()

def optimize_strategy(df: pd.DataFrame, param_grid: dict) -> Tuple[dict, float]:
    """
    使用网格搜索优化策略参数
    """
    best_params = None
    best_return = float('-inf')
    
    # 生成参数组合
    param_combinations = ParameterGrid(param_grid)
    total_combinations = len(list(ParameterGrid(param_grid)))
    print(f"开始参数优化，共 {total_combinations} 组参数组合")
    
    for i, params in enumerate(param_combinations, 1):
        # 设置策略参数
        VolumeBreakStrategy.volume_window = params['volume_window']
        VolumeBreakStrategy.volume_threshold = params['volume_threshold']
        VolumeBreakStrategy.rsi_window = params['rsi_window']
        VolumeBreakStrategy.price_window = params['price_window']
        VolumeBreakStrategy.stop_loss = params['stop_loss']
        VolumeBreakStrategy.take_profit = params['take_profit']
        
        try:
            # 运行回测
            bt = Backtest(df, VolumeBreakStrategy, cash=100000)
            stats = bt.run()
            
            # 使用综合评分来评估策略
            score = (
                stats['Return [%]'] * 0.4 +  # 收益率权重40%
                stats['Sharpe Ratio'] * 0.3 +  # 夏普比率权重30%
                stats['Win Rate [%]'] * 0.2 -  # 胜率权重20%
                abs(stats['Max. Drawdown [%]']) * 0.1  # 最大回撤权重10%
            )
            
            # 更新最优参数
            if score > best_return:
                best_return = score
                best_params = params
                print(f"找到更优参数组合 ({i}/{total_combinations}):")
                print(f"参数: {params}")
                print(f"收益率: {stats['Return [%]']:.2f}%")
                print(f"夏普比率: {stats['Sharpe Ratio']:.2f}")
                print(f"胜率: {stats['Win Rate [%]']:.2f}%")
                print(f"最大回撤: {stats['Max. Drawdown [%]']:.2f}%")
                print("------------------------")
            
            if i % 10 == 0:
                print(f"已完成 {i}/{total_combinations} 组参数测试")
                
        except Exception as e:
            print(f"参数组合 {params} 回测失败: {str(e)}")
            continue
            
    return best_params, best_return

def prepare_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    准备回测数据
    """
    try:
        # 只保留需要的列并重命名
        df = df[['t', 'o', 'h', 'l', 'c', 'v']]
        df = df.rename(columns={
            'o': 'Open',
            'h': 'High',
            'l': 'Low',
            'c': 'Close',
            'v': 'Volume'
        })
        
        # 将时间设为索引
        df.set_index('t', inplace=True)
        df.index = pd.to_datetime(df.index, unit='ms')  # 假设时间戳是毫秒格式
        
        # 确保所有数据都是数值类型
        numeric_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 删除任何包含NaN的行
        df = df.dropna()
        
        # 按时间排序
        df = df.sort_index()
        
        return df
    except Exception as e:
        print(f"数据预处理失败: {str(e)}")
        raise

def run_strategy(data_path: str):
    """
    运行策略主函数
    """
    try:
        # 读取数据
        df = pd.read_parquet(data_path)
        
        # 如果数据中包含'i'列，只选择1分钟数据
        if 'i' in df.columns:
            df = df[df['i'] == '1m']
        
        # 准备数据
        df = prepare_data(df)
        
        print("数据预处理完成，样本数量:", len(df))
        print("数据列:", df.columns.tolist())
        print("数据预览:\n", df.head())
        
        # 定义参数网格
        param_grid = {
            'volume_window': [15, 30, 45],
            'volume_threshold': [3, 5, 10],  # 降低阈值以增加交易机会
            'rsi_window': [7, 14, 21],
            'price_window': [10, 20, 30],
            'stop_loss': [1.0, 2.0, 3.0],
            'take_profit': [2.0, 3.0, 4.0]  # 调整止盈比例
        }
        
        # 优化参数
        best_params, best_return = optimize_strategy(df, param_grid)
        
        if best_params is None:
            print("未找到有效的参数组合，使用默认参数")
            best_params = {
                'volume_window': 30,
                'volume_threshold': 5,
                'rsi_window': 14,
                'price_window': 20,
                'stop_loss': 2.0,
                'take_profit': 3.0
            }
        else:
            print(f"最优参数: {best_params}")
            print(f"最优收益率: {best_return:.2f}%")
        
        # 使用最优参数运行回测
        VolumeBreakStrategy.volume_window = best_params['volume_window']
        VolumeBreakStrategy.volume_threshold = best_params['volume_threshold']
        VolumeBreakStrategy.rsi_window = best_params['rsi_window']
        VolumeBreakStrategy.price_window = best_params['price_window']
        VolumeBreakStrategy.stop_loss = best_params['stop_loss']
        VolumeBreakStrategy.take_profit = best_params['take_profit']
        
        bt = Backtest(df, VolumeBreakStrategy, cash=100000)
        stats = bt.run()
        print("\n策略统计:")
        print(f"总收益率: {stats['Return [%]']:.2f}%")
        print(f"最大回撤: {stats['Max. Drawdown [%]']:.2f}%")
        print(f"夏普比率: {stats['Sharpe Ratio']:.2f}")
        print(f"交易次数: {stats['# Trades']}")
        print(f"胜率: {stats['Win Rate [%]']:.2f}%")
        print(f"盈亏比: {stats['Profit Factor']:.2f}")
        print(f"最大连续亏损次数: {stats['Longest Losing Streak']}")
        
        # 绘制回测结果
        bt.plot()
        
    except Exception as e:
        print(f"策略运行失败: {str(e)}")
        raise

if __name__ == "__main__":
    data_path = '/Users/<USER>/Downloads/doge/doge_kline1m_2024.parquet'
    run_strategy(data_path)
