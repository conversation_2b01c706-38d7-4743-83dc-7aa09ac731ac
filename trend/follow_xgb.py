import pandas as pd
import numpy as np
from xgboost import XGBClassifier
from sklearn.model_selection import train_test_split
import talib
from sklearn.preprocessing import LabelEncoder

class TrendXGBStrategy:
    def __init__(self):
        self.model = None
        self.label_encoder = LabelEncoder()
        
    def prepare_features(self, df):
        """准备特征数据"""
        # 确保索引唯一
        df = df.reset_index(drop=True)
        
        # 基础量价特征
        df['sma_v'] = df['v'].rolling(30).mean()
        df['sma_v'] = df['sma_v'].shift(1)
        df['max_v'] = df['v'].rolling(30).max()
        df['max_v'] = df['max_v'].shift(1)
        
        df['vt'] = df['v'] / df['sma_v']
        df['vt_max'] = df['v'] / df['max_v']
        
        # 价格变化特征
        df['co'] = (df['c'] / df['o'] - 1) * 100  # 当前K线涨跌幅
        df['ho'] = (df['h'] / df['o'] - 1) * 100  # 当前K线最高涨幅
        df['lo'] = (df['l'] / df['o'] - 1) * 100  # 当前K线最低跌幅
        
        # 前期价格趋势
        for period in [5, 10, 30]:
            df[f'oo_pre_{period}'] = (df['o'] / df['o'].shift(period) - 1) * 100
            
        # 成交量趋势
        for period in [5, 10, 30]:
            df[f'v_pre_{period}'] = df['v'].rolling(period).mean() / df['sma_v']
            
        # 技术指标
        df['rsi'] = talib.RSI(df['c'], timeperiod=14)
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['c'])
        df['slowk'], df['slowd'] = talib.STOCH(df['h'], df['l'], df['c'])
        df['atr'] = talib.ATR(df['h'], df['l'], df['c'], timeperiod=14)
        df['atr_ratio'] = df['atr'] / df['c'] * 100
        
        # 波动率特征
        df['volatility'] = df['c'].rolling(30).std() / df['c'].rolling(30).mean() * 100
        
        # 目标变量: 未来10分钟的最大涨跌幅
        df['future_ho'] = (df['h'].shift(-10).rolling(10).max() / df['c'] - 1) * 100
        df['future_lo'] = (df['l'].shift(-10).rolling(10).min() / df['c'] - 1) * 100
        df['future_co'] = (df['c'].shift(-10) / df['c'] - 1) * 1
        
        # 生成标签
        df['label'] = 0  # 默认不操作
        long_mask = (df['future_ho'] > 0.5)  # 做多信号
        short_mask = (df['future_lo'] < -0.5)  # 做空信号
        df.loc[long_mask, 'label'] = 1
        df.loc[short_mask, 'label'] = -1
        
        return df
        
    def get_features(self):
        """获取特征列表"""
        features = [
            'vt', 'vt_max', 'co', 'ho', 'lo',
            'oo_pre_5', 'oo_pre_10', 'oo_pre_30',
            'v_pre_5', 'v_pre_10', 'v_pre_30',
            'rsi', 'macd', 'macd_signal', 'macd_hist',
            'slowk', 'slowd', 'atr_ratio', 'volatility'
        ]
        return features
        
    def train(self, df):
        """训练模型"""
        df = self.prepare_features(df)
        features = self.get_features()
        
        # 去除包含NaN的行
        df = df.dropna()
        
        # 分割训练集和测试集
        X = df[features]
        y = df['label']
        
        # 将标签转换为0,1,2
        y = self.label_encoder.fit_transform(y)
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 训练模型
        self.model = XGBClassifier(
            n_estimators=100,
            max_depth=5,
            learning_rate=0.1,
            objective='multi:softmax',
            num_class=3
        )
        self.model.fit(X_train, y_train)
        
        # 评估模型
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        print(f"训练集准确率: {train_score:.4f}")
        print(f"测试集准确率: {test_score:.4f}")
        
        # 打印标签分布
        unique, counts = np.unique(y, return_counts=True)
        print("\n标签分布:")
        for label, count in zip(unique, counts):
            print(f"标签 {label}: {count} ({count/len(y)*100:.2f}%)")
        
        return self.model
        
    def predict(self, df):
        """预测交易信号"""
        df = self.prepare_features(df)
        features = self.get_features()
        
        # 确保所有特征都存在
        df = df.dropna()
        if len(df) == 0:
            return pd.Series()
            
        # 预测
        X = df[features]
        predictions = self.model.predict(X)
        
        # 将预测结果转换回原始标签
        predictions = self.label_encoder.inverse_transform(predictions)
        
        return pd.Series(predictions, index=df.index)
        
    def backtest(self, df):
        """回测策略"""
        df = self.prepare_features(df)
        predictions = self.predict(df)
        
        # 计算收益
        df['position'] = predictions  # 下一分钟才能交易
        # df['position'] = df['position'].shift(1).fillna(0)
        # df['returns'] = df['position'] * df['future_co']  # 简化的收益计算
        def fun(x):
            if x['vt'] < 10:
                return 0
            if x['position'] == 1:
                if x['future_ho'] > 0.5:
                    return 0.005
                else:
                    return x['future_co']
            elif x['position'] == -1:
                if x['future_lo'] < -0.5:
                    return 0.005
                else:
                    return -x['future_co']
            else:
                return 0
        df['returns'] = df.apply(lambda x: fun(x), axis=1)
        df['returns'] = df['returns'].fillna(0)
        
        # 计算累积收益
        df['cumulative_returns'] = df['returns'].cumsum()
        
        # 计算回测指标
        total_return = df['cumulative_returns'].iloc[-1]
        sharpe = np.sqrt(365*24*60) * df['returns'].mean() / df['returns'].std()
        max_drawdown = (df['cumulative_returns'].cummax() - df['cumulative_returns']).max()
        
        print(f"\n回测结果:")
        print(f"总收益率: {total_return:.2f}%")
        print(f"夏普比率: {sharpe:.2f}")
        print(f"最大回撤: {max_drawdown:.2f}%")
        
        # 计算胜率
        trades = df[df['position'] != 0]['returns']
        win_rate = (trades > 0).sum() / len(trades) if len(trades) > 0 else 0
        print(f"交易次数: {len(trades)}")
        print(f"胜率: {win_rate:.2f}")
        
        return df

def main():
    # 加载数据
    path = '/Users/<USER>/Downloads/doge/doge_kline1m_2024.parquet'
    df = pd.read_parquet(path)
    df = df[df['i'] == '1m']
    df = df[['t', 'o', 'h', 'l', 'c', 'v']]  # 只保留需要的列
    df = df.sort_values('t').reset_index(drop=True)  # 按时间排序并重置索引
    
    print(f"数据范围: {df['t'].min()} 到 {df['t'].max()}")
    print(f"数据条数: {len(df)}")
    
    # 创建策略实例
    strategy = TrendXGBStrategy()
    
    # 训练模型
    model = strategy.train(df)
    
    # 回测
    results = strategy.backtest(df)

    results.to_csv('/Users/<USER>/Downloads/doge/results.csv', index=False)
    
if __name__ == "__main__":
    main()
