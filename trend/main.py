#!/usr/bin/env python3
"""
DOGE趋势动量策略回测主程序

功能:
- 数据加载和预处理
- 策略信号生成
- 参数优化
- 回测执行
- 结果分析和可视化
- 报告生成

日期: 2025-07-17
"""

import argparse
import json
import sys
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from doge_data_loader import DogeDataLoader
from momentum_strategy import (
    GranvilleStrategy,
    MomentumStrategyManager,
    DualMovingAverageStrategy,
    RSIMomentumStrategy,
    BreakoutStrategy,
    MACDStrategy,
    CompositeStrategy
)
from backtest_engine import BacktestEngine
from optimizer import ParameterOptimizer
from analysis import BacktestAnalyzer

class TrendMomentumBacktester:
    """趋势动量策略回测器"""
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化回测器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self.load_config()
        self.data_loader = DogeDataLoader(self.config['data']['path'])
        self.strategy_manager = MomentumStrategyManager()
        self.optimizer = ParameterOptimizer(
            objective_function=self.config['optimization']['objective_function'],
            n_jobs=self.config['optimization']['n_jobs'],
            verbose=self.config['optimization']['verbose']
        )
        self.analyzer = BacktestAnalyzer()
        
        # 数据和结果
        self.df = None
        self.optimization_results = {}
        self.backtest_results = {}
        self.best_params = {}
        
        # 创建输出目录
        self.output_dir = Path(self.config['output']['directory'])
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"回测器初始化完成")
        print(f"配置文件: {config_path}")
        print(f"输出目录: {self.output_dir}")
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            print(f"配置文件 {self.config_path} 不存在，使用默认配置")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "data": {
                "path": "/Users/<USER>/workspace/data/doge",
                "symbol": "DOGE",
                "timeframe": "1m",
                "use_processed": True
            },
            "strategies": {
                "dual_ma": {
                    "enabled": True,
                    "params": {
                        "fast_period": 9,
                        "slow_period": 21,
                        "signal_type": "ema",
                        "volume_confirm": True,
                        "volume_threshold": 1.5
                    }
                },
                "rsi_momentum": {
                    "enabled": True,
                    "params": {
                        "rsi_period": 14,
                        "rsi_overbought": 70,
                        "rsi_oversold": 30,
                        "rsi_middle": 50,
                        "momentum_confirm": True
                    }
                },
                "breakout": {
                    "enabled": True,
                    "params": {
                        "lookback_period": 20,
                        "breakout_threshold": 0.015,
                        "volume_confirm": True,
                        "volume_multiplier": 2.0,
                        "atr_period": 14
                    }
                },
                "macd": {
                    "enabled": True,
                    "params": {
                        "fast_period": 12,
                        "slow_period": 26,
                        "signal_period": 9,
                        "histogram_threshold": 0,
                        "price_confirm": True
                    }
                },
                "granville": {
                    "enabled": True,
                    "params": {
                        "ma_period": 20,
                        "ma_type": "sma",
                        "min_deviation": 0.005,
                        "volume_threshold": 1.5,
                        "direction_periods": 3,
                        "strength_threshold": 0.7
                    }
                },
                "composite": {
                    "enabled": True,
                    "params": {
                        "signal_threshold": 0.5,
                        "weight_method": "equal"
                    }
                }
            },
            "backtest": {
                "initial_capital": 100000,
                "commission_rate": 0.001,
                "slippage": 0.0001,
                "position_size_type": "percent",
                "position_size": 0.1,
                "max_position_size": 1.0,
                "enable_short": True,
                "stop_loss_pct": 0.05,
                "take_profit_pct": 0.10,
                "risk_free_rate": 0.02
            },
            "optimization": {
                "enabled": True,
                "objective_function": "composite_score",
                "n_jobs": 4,
                "verbose": True,
                "max_combinations_per_strategy": 200,
                "strategies_to_optimize": ["dual_ma", "rsi_momentum", "breakout", "macd", "granville"]
            },
            "analysis": {
                "generate_plots": True,
                "generate_html_report": True,
                "plot_start_date": None,
                "plot_end_date": None,
                "save_plots": True
            },
            "output": {
                "directory": "results",
                "save_results": True,
                "save_trades": True,
                "save_portfolio": True
            }
        }
    
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        print("开始加载数据...")
        start_time = time.time()
        
        try:
            if self.config['data']['use_processed']:
                # 尝试加载已处理的数据
                try:
                    self.df = self.data_loader.load_processed_data()
                    print(f"成功加载已处理数据，共 {len(self.df)} 条记录")
                except FileNotFoundError:
                    print("未找到已处理数据，开始重新处理...")
                    self.df = self.data_loader.load_2025_data()
                    self.df = self.data_loader.prepare_features(self.df)
                    self.data_loader.save_processed_data(self.df)
            else:
                # 重新加载和处理数据
                self.df = self.data_loader.load_2025_data()
                self.df = self.data_loader.prepare_features(self.df)
                self.data_loader.save_processed_data(self.df)
            
            # 验证数据质量
            validation_results = self.data_loader.validate_data(self.df)
            print("数据质量验证完成")
            
            # 打印数据信息
            info = self.data_loader.get_data_info(self.df)
            print(f"数据时间范围: {info['date_range']['start']} - {info['date_range']['end']}")
            print(f"价格范围: {info['price_stats']['min']:.4f} - {info['price_stats']['max']:.4f}")
            
            elapsed_time = time.time() - start_time
            print(f"数据加载完成，耗时: {elapsed_time:.2f}秒")
            
            return self.df
            
        except Exception as e:
            print(f"数据加载失败: {str(e)}")
            raise
    
    def setup_strategies(self):
        """设置策略"""
        print("设置策略...")
        
        strategies_config = self.config['strategies']
        
        # 双均线策略
        if strategies_config['dual_ma']['enabled']:
            dual_ma_strategy = DualMovingAverageStrategy(strategies_config['dual_ma']['params'])
            self.strategy_manager.add_strategy('dual_ma', dual_ma_strategy)
            print("- 双均线策略已添加")
        
        # RSI动量策略
        if strategies_config['rsi_momentum']['enabled']:
            rsi_strategy = RSIMomentumStrategy(strategies_config['rsi_momentum']['params'])
            self.strategy_manager.add_strategy('rsi_momentum', rsi_strategy)
            print("- RSI动量策略已添加")
        
        # 突破策略
        if strategies_config['breakout']['enabled']:
            breakout_strategy = BreakoutStrategy(strategies_config['breakout']['params'])
            self.strategy_manager.add_strategy('breakout', breakout_strategy)
            print("- 突破策略已添加")
        
        # MACD策略
        if strategies_config['macd']['enabled']:
            macd_strategy = MACDStrategy(strategies_config['macd']['params'])
            self.strategy_manager.add_strategy('macd', macd_strategy)
            print("- MACD策略已添加")
        
        # Granville策略
        if strategies_config['granville']['enabled']:
            granville_strategy = GranvilleStrategy(strategies_config['granville']['params'])
            self.strategy_manager.add_strategy('granville', granville_strategy)
            print("- Granville策略已添加")
        
        # 组合策略
        if strategies_config['composite']['enabled']:
            enabled_strategies = []
            if strategies_config['dual_ma']['enabled']:
                enabled_strategies.append(DualMovingAverageStrategy(strategies_config['dual_ma']['params']))
            if strategies_config['rsi_momentum']['enabled']:
                enabled_strategies.append(RSIMomentumStrategy(strategies_config['rsi_momentum']['params']))
            if strategies_config['breakout']['enabled']:
                enabled_strategies.append(BreakoutStrategy(strategies_config['breakout']['params']))
            if strategies_config['macd']['enabled']:
                enabled_strategies.append(MACDStrategy(strategies_config['macd']['params']))
            if strategies_config['granville']['enabled']:
                enabled_strategies.append(GranvilleStrategy(strategies_config['granville']['params']))
            
            if enabled_strategies:
                composite_strategy = CompositeStrategy(enabled_strategies, strategies_config['composite']['params'])
                self.strategy_manager.add_strategy('composite', composite_strategy)
                print("- 组合策略已添加")
        
        print(f"总共添加了 {len(self.strategy_manager.strategies)} 个策略")
    
    def run_optimization(self):
        """运行参数优化"""
        if not self.config['optimization']['enabled']:
            print("参数优化已禁用")
            return
        
        print("开始参数优化...")
        start_time = time.time()
        
        strategies_to_optimize = self.config['optimization']['strategies_to_optimize']
        max_combinations = self.config['optimization']['max_combinations_per_strategy']
        
        # 优化各个策略
        for strategy_name in strategies_to_optimize:
            if strategy_name in self.strategy_manager.strategies:
                print(f"\n优化策略: {strategy_name}")
                
                results = self.optimizer.optimize_single_strategy(
                    df=self.df,
                    strategy_name=strategy_name,
                    max_combinations=max_combinations
                )
                
                self.optimization_results[strategy_name] = results
                
                if results:
                    best_params = results[0].params
                    self.best_params[strategy_name] = best_params
                    print(f"最优参数: {best_params}")
                    print(f"最优评分: {results[0].score:.4f}")
        
        elapsed_time = time.time() - start_time
        print(f"\n参数优化完成，总耗时: {elapsed_time:.2f}秒")
        
        # 保存优化结果
        if self.config['output']['save_results']:
            optimizer_results_path = self.output_dir / "optimization_results.json"
            self.optimizer.save_results(str(optimizer_results_path))
    
    def run_backtest(self, strategy_name: str, use_optimized_params: bool = True) -> Dict[str, Any]:
        """运行回测"""
        print(f"\n开始回测策略: {strategy_name}")
        
        # 获取参数
        if use_optimized_params and strategy_name in self.best_params:
            # 使用优化后的参数
            params = self.best_params[strategy_name]
            print(f"使用优化参数: {params}")
        else:
            # 使用配置文件中的参数
            params = self.config['strategies'][strategy_name]['params']
            print(f"使用配置参数: {params}")
        
        # 创建策略实例
        if strategy_name == 'dual_ma':
            strategy = DualMovingAverageStrategy(params)
            self.strategy_manager.add_strategy(f'{strategy_name}_backtest', strategy)
            strategy_results = self.strategy_manager.run_strategy(f'{strategy_name}_backtest', self.df)
            signals = strategy_results['ma_signal']
        elif strategy_name == 'rsi_momentum':
            strategy = RSIMomentumStrategy(params)
            self.strategy_manager.add_strategy(f'{strategy_name}_backtest', strategy)
            strategy_results = self.strategy_manager.run_strategy(f'{strategy_name}_backtest', self.df)
            signals = strategy_results['rsi_signal']
        elif strategy_name == 'breakout':
            strategy = BreakoutStrategy(params)
            self.strategy_manager.add_strategy(f'{strategy_name}_backtest', strategy)
            strategy_results = self.strategy_manager.run_strategy(f'{strategy_name}_backtest', self.df)
            signals = strategy_results['breakout_signal']
        elif strategy_name == 'macd':
            strategy = MACDStrategy(params)
            self.strategy_manager.add_strategy(f'{strategy_name}_backtest', strategy)
            strategy_results = self.strategy_manager.run_strategy(f'{strategy_name}_backtest', self.df)
            signals = strategy_results['macd_cross']
        elif strategy_name == 'granville':
            strategy = GranvilleStrategy(params)
            self.strategy_manager.add_strategy(f'{strategy_name}_backtest', strategy)
            strategy_results = self.strategy_manager.run_strategy(f'{strategy_name}_backtest', self.df)
            signals = strategy_results['granville_signal']
        else:
            raise ValueError(f"不支持的策略: {strategy_name}")
        
        # 创建回测引擎
        backtest_config = self.config['backtest']
        engine = BacktestEngine(
            initial_capital=backtest_config['initial_capital'],
            commission_rate=backtest_config['commission_rate'],
            slippage=backtest_config['slippage'],
            position_size_type=backtest_config['position_size_type'],
            position_size=backtest_config['position_size'],
            max_position_size=backtest_config['max_position_size'],
            enable_short=backtest_config['enable_short'],
            risk_free_rate=backtest_config['risk_free_rate']
        )
        
        # 设置风险管理
        engine.set_risk_management(
            stop_loss_pct=backtest_config['stop_loss_pct'],
            take_profit_pct=backtest_config['take_profit_pct']
        )
        
        # 运行回测
        results = engine.run_backtest(self.df, signals)
        
        # 获取交易和投资组合历史
        portfolio_history = engine.get_portfolio_history()
        trade_history = engine.get_trade_history()
        
        # 保存结果
        backtest_data = {
            'strategy_name': strategy_name,
            'params': params,
            'results': results,
            'portfolio_history': portfolio_history,
            'trade_history': trade_history,
            'signals': signals,
            'strategy_results': strategy_results
        }
        
        self.backtest_results[strategy_name] = backtest_data
        
        # 打印结果摘要
        self.analyzer.print_performance_summary(results)
        
        return backtest_data
    
    def run_analysis(self, strategy_name: str):
        """运行结果分析"""
        if strategy_name not in self.backtest_results:
            print(f"策略 {strategy_name} 的回测结果不存在")
            return
        
        print(f"\n开始分析策略: {strategy_name}")
        
        backtest_data = self.backtest_results[strategy_name]
        results = backtest_data['results']
        portfolio_history = backtest_data['portfolio_history']
        trade_history = backtest_data['trade_history']
        signals = backtest_data['signals']
        strategy_results = backtest_data['strategy_results']
        
        analysis_config = self.config['analysis']
        
        if analysis_config['generate_plots']:
            # 创建输出目录
            plot_dir = self.output_dir / "plots" / strategy_name
            plot_dir.mkdir(parents=True, exist_ok=True)
            
            # 绘制净值曲线
            if not portfolio_history.empty:
                equity_path = plot_dir / "equity_curve.png" if analysis_config['save_plots'] else None
                self.analyzer.plot_equity_curve(portfolio_history, save_path=equity_path)
            
            # 绘制交易分析
            if not trade_history.empty:
                trades_path = plot_dir / "trades_analysis.png" if analysis_config['save_plots'] else None
                self.analyzer.plot_trades_analysis(trade_history, save_path=trades_path)
            
            # 绘制策略信号
            signal_column = {
                'dual_ma': 'ma_signal',
                'rsi_momentum': 'rsi_signal',
                'breakout': 'breakout_signal',
                'macd': 'macd_cross',
                'granville': 'granville_signal'
            }.get(strategy_name, 'signal')
            
            if signal_column in strategy_results.columns:
                # 合并数据
                plot_df = self.df.copy()
                plot_df[signal_column] = strategy_results[signal_column]
                
                signals_path = plot_dir / "strategy_signals.png" if analysis_config['save_plots'] else None
                self.analyzer.plot_strategy_signals(
                    df=plot_df,
                    signal_column=signal_column,
                    start_date=analysis_config['plot_start_date'],
                    end_date=analysis_config['plot_end_date'],
                    save_path=signals_path
                )
            
            # 绘制性能指标
            metrics_path = plot_dir / "performance_metrics.png" if analysis_config['save_plots'] else None
            self.analyzer.plot_performance_metrics(results, save_path=metrics_path)
        
        # 生成HTML报告
        if analysis_config['generate_html_report']:
            html_path = self.output_dir / f"{strategy_name}_report.html"
            self.analyzer.generate_html_report(
                results=results,
                portfolio_history=portfolio_history,
                trade_history=trade_history,
                save_path=str(html_path)
            )
    
    def save_results(self):
        """保存结果"""
        if not self.config['output']['save_results']:
            return
        
        print("保存结果...")
        
        # 保存回测结果摘要
        results_summary = []
        for strategy_name, backtest_data in self.backtest_results.items():
            summary = {
                'strategy': strategy_name,
                'params': backtest_data['params'],
                'metrics': backtest_data['results']
            }
            results_summary.append(summary)
        
        summary_path = self.output_dir / "backtest_summary.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"回测摘要已保存到: {summary_path}")
        
        # 保存交易记录和投资组合历史
        for strategy_name, backtest_data in self.backtest_results.items():
            strategy_dir = self.output_dir / strategy_name
            strategy_dir.mkdir(exist_ok=True)
            
            # 保存交易记录
            if self.config['output']['save_trades'] and not backtest_data['trade_history'].empty:
                trades_path = strategy_dir / "trades.csv"
                backtest_data['trade_history'].to_csv(trades_path, index=False)
            
            # 保存投资组合历史
            if self.config['output']['save_portfolio'] and not backtest_data['portfolio_history'].empty:
                portfolio_path = strategy_dir / "portfolio.csv"
                backtest_data['portfolio_history'].to_csv(portfolio_path, index=False)
    
    def run_full_backtest(self, strategies: List[str] = None):
        """运行完整回测流程"""
        print("开始完整回测流程...")
        start_time = time.time()
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 设置策略
        self.setup_strategies()
        
        # 3. 参数优化
        self.run_optimization()
        
        # 4. 运行回测
        if strategies is None:
            strategies = ['dual_ma', 'rsi_momentum', 'breakout', 'macd', 'granville']
        
        for strategy_name in strategies:
            if strategy_name in self.config['strategies'] and self.config['strategies'][strategy_name]['enabled']:
                self.run_backtest(strategy_name, use_optimized_params=True)
                self.run_analysis(strategy_name)
        
        # 5. 保存结果
        self.save_results()
        
        elapsed_time = time.time() - start_time
        print(f"\n完整回测流程完成，总耗时: {elapsed_time:.2f}秒")
        print(f"结果已保存到: {self.output_dir}")
        
        # 6. 打印最终摘要
        self.print_final_summary()
    
    def print_final_summary(self):
        """打印最终摘要"""
        print("\n" + "="*80)
        print("最终回测摘要")
        print("="*80)
        
        for strategy_name, backtest_data in self.backtest_results.items():
            results = backtest_data['results']
            print(f"\n{strategy_name.upper()}:")
            print(f"  总收益率: {results.get('total_return_pct', 0):.2f}%")
            print(f"  夏普比率: {results.get('sharpe_ratio', 0):.4f}")
            print(f"  最大回撤: {results.get('max_drawdown', 0):.2f}%")
            print(f"  胜率: {results.get('win_rate', 0):.2f}%")
            print(f"  总交易次数: {results.get('total_trades', 0)}")
        
        # 找出最佳策略
        best_strategy = None
        best_score = float('-inf')
        
        for strategy_name, backtest_data in self.backtest_results.items():
            results = backtest_data['results']
            # 使用复合评分
            score = (
                results.get('sharpe_ratio', 0) * 0.4 +
                results.get('total_return_pct', 0) * 0.004 +
                max(-results.get('max_drawdown', 0), -50) * 0.02 +
                results.get('win_rate', 0) * 0.01
            )
            
            if score > best_score:
                best_score = score
                best_strategy = strategy_name
        
        if best_strategy:
            print(f"\n最佳策略: {best_strategy.upper()}")
            print(f"综合评分: {best_score:.4f}")
        
        print("="*80)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DOGE趋势动量策略回测')
    parser.add_argument('--config', '-c', type=str, default='config.json',
                       help='配置文件路径')
    parser.add_argument('--strategies', '-s', nargs='+', 
                       choices=['dual_ma', 'rsi_momentum', 'breakout', 'macd', 'granville'],
                       help='要回测的策略')
    parser.add_argument('--no-optimization', action='store_true',
                       help='跳过参数优化')
    parser.add_argument('--no-analysis', action='store_true',
                       help='跳过结果分析')
    
    args = parser.parse_args()
    
    try:
        # 创建回测器
        backtester = TrendMomentumBacktester(args.config)
        
        # 如果指定了跳过优化
        if args.no_optimization:
            backtester.config['optimization']['enabled'] = False
        
        # 如果指定了跳过分析
        if args.no_analysis:
            backtester.config['analysis']['generate_plots'] = False
            backtester.config['analysis']['generate_html_report'] = False
        
        # 运行完整回测
        backtester.run_full_backtest(args.strategies)
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"回测失败: {str(e)}")
        raise

if __name__ == "__main__":
    main()