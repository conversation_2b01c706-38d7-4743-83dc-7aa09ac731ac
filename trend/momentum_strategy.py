import pandas as pd
import numpy as np
from typing import Dict, Any, Tu<PERSON>, List
from abc import ABC, abstractmethod
import talib

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, params: Dict[str, Any]):
        self.name = name
        self.params = params
        
    @abstractmethod
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        pass
    
    def get_param(self, key: str, default=None):
        """获取策略参数"""
        return self.params.get(key, default)

class DualMovingAverageStrategy(BaseStrategy):
    """双均线交叉策略"""
    
    def __init__(self, params: Dict[str, Any] = None):
        if params is None:
            params = {
                'fast_period': 9,
                'slow_period': 21,
                'signal_type': 'ema',  # 'ema' or 'sma'
                'volume_confirm': True,
                'volume_threshold': 1.5
            }
        super().__init__("DualMovingAverage", params)
    
    def calculate_ma_direction(self, ma_series: pd.Series, window: int = 5) -> pd.Series:
        """计算移动平均线方向"""
        # 计算均线斜率
        ma_slope = (ma_series - ma_series.shift(window)) / window
        
        # 判断方向：1=上升，-1=下降，0=平稳
        direction = pd.Series(0, index=ma_series.index)
        direction[ma_slope > 0.0001] = 1  # 上升
        direction[ma_slope < -0.0001] = -1  # 下降
        
        return direction
    
    def calculate_price_deviation(self, price: pd.Series, ma: pd.Series) -> pd.Series:
        """计算价格偏离移动平均线的程度"""
        return (price - ma) / ma * 100
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成双均线交叉信号"""
        df = df.copy()
        
        fast_period = self.get_param('fast_period', 9)
        slow_period = self.get_param('slow_period', 21)
        signal_type = self.get_param('signal_type', 'ema')
        volume_confirm = self.get_param('volume_confirm', True)
        volume_threshold = self.get_param('volume_threshold', 1.5)
        
        # 计算移动平均线
        if signal_type == 'ema':
            df['ma_fast'] = talib.EMA(df['close'].values, timeperiod=fast_period)
            df['ma_slow'] = talib.EMA(df['close'].values, timeperiod=slow_period)
        else:
            df['ma_fast'] = talib.SMA(df['close'].values, timeperiod=fast_period)
            df['ma_slow'] = talib.SMA(df['close'].values, timeperiod=slow_period)
        
        # 计算均线方向
        df['ma_fast_direction'] = self.calculate_ma_direction(df['ma_fast'])
        df['ma_slow_direction'] = self.calculate_ma_direction(df['ma_slow'])
        
        # 计算价格偏离度
        df['price_deviation_fast'] = self.calculate_price_deviation(df['close'], df['ma_fast'])
        df['price_deviation_slow'] = self.calculate_price_deviation(df['close'], df['ma_slow'])
        
        # 生成信号
        df['ma_signal'] = 0
        df.loc[df['ma_fast'] > df['ma_slow'], 'ma_signal'] = 1  # 做多
        df.loc[df['ma_fast'] < df['ma_slow'], 'ma_signal'] = -1  # 做空
        
        # 金叉死叉信号
        df['ma_cross'] = df['ma_signal'].diff()
        df['golden_cross'] = (df['ma_cross'] == 2) & (df['ma_signal'] == 1)
        df['death_cross'] = (df['ma_cross'] == -2) & (df['ma_signal'] == -1)
        
        # 成交量确认
        if volume_confirm:
            df['volume_ma'] = talib.SMA(df['volume'].values, timeperiod=20)
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            # 只有在成交量放大时才确认信号
            df.loc[df['volume_ratio'] < volume_threshold, 'golden_cross'] = False
            df.loc[df['volume_ratio'] < volume_threshold, 'death_cross'] = False
        
        return df

class GranvilleStrategy(BaseStrategy):
    """Granville八大法则策略"""
    
    def __init__(self, params: Dict[str, Any] = None):
        if params is None:
            params = {
                'ma_period': 20,
                'ma_type': 'sma',  # 'sma' or 'ema'
                'direction_window': 5,
                'deviation_threshold': 3.0,  # 价格偏离阈值（%）
                'support_resistance_window': 10,
                'volume_confirm': True,
                'volume_threshold': 1.2
            }
        super().__init__("Granville", params)
    
    def calculate_ma_direction(self, ma_series: pd.Series, window: int = 5) -> pd.Series:
        """计算移动平均线方向"""
        ma_slope = (ma_series - ma_series.shift(window)) / window
        
        direction = pd.Series(0, index=ma_series.index)
        direction[ma_slope > 0.0001] = 1  # 上升
        direction[ma_slope < -0.0001] = -1  # 下降
        
        return direction
    
    def calculate_price_deviation(self, price: pd.Series, ma: pd.Series) -> pd.Series:
        """计算价格偏离移动平均线的程度"""
        return (price - ma) / ma * 100
    
    def check_granville_buy_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """检查Granville买入信号"""
        df = df.copy()
        
        # 买1：均线整体上行，股价由下至上上穿均线（黄金交叉）
        df['buy_1'] = (
            (df['ma_direction'] == 1) &  # 均线上行
            (df['close'] > df['ma']) &  # 价格在均线上方
            (df['close'].shift(1) <= df['ma'].shift(1)) &  # 前一期价格在均线下方
            (df['price_deviation'] > 0)  # 价格已突破均线
        )
        
        # 买2：股价出现下跌迹象，但尚未跌破均线（均线支撑）
        df['buy_2'] = (
            (df['ma_direction'] == 1) &  # 均线仍上行
            (df['close'] > df['ma']) &  # 价格在均线上方
            (df['close'] < df['close'].shift(1)) &  # 价格下跌
            (df['price_deviation'] > 0) &  # 但未跌破均线
            (df['price_deviation'] < 2.0)  # 接近均线
        )
        
        # 买3：股价仍处于均线上方，但呈现急剧下跌趋势，当跌破均线时
        df['buy_3'] = (
            (df['ma_direction'] == 1) &  # 均线仍上行
            (df['close'] < df['ma']) &  # 价格跌破均线
            (df['close'].shift(1) > df['ma'].shift(1)) &  # 前一期在均线上方
            (df['close'] < df['close'].shift(2)) &  # 持续下跌
            (df['price_deviation'] > -2.0)  # 但未远离均线
        )
        
        # 买4：股价和均线都处于下降通道，且股价严重远离均线
        df['buy_4'] = (
            (df['ma_direction'] == -1) &  # 均线下行
            (df['close'] < df['ma']) &  # 价格在均线下方
            (df['price_deviation'] < -self.get_param('deviation_threshold', 3.0))  # 严重偏离
        )
        
        return df
    
    def check_granville_sell_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """检查Granville卖出信号"""
        df = df.copy()
        
        # 卖1：均线由上升变为下降，股价跌破均线（死亡交叉）
        df['sell_1'] = (
            (df['ma_direction'] == -1) &  # 均线转为下行
            (df['ma_direction'].shift(1) == 1) &  # 前期均线上行
            (df['close'] < df['ma']) &  # 价格跌破均线
            (df['close'].shift(1) >= df['ma'].shift(1))  # 前期价格在均线上方
        )
        
        # 卖2：股价仍处于均线之下，但开始上涨，接近均线但未突破（均线阻力）
        df['sell_2'] = (
            (df['ma_direction'] == -1) &  # 均线下行
            (df['close'] < df['ma']) &  # 价格在均线下方
            (df['close'] > df['close'].shift(1)) &  # 价格上涨
            (df['price_deviation'] > -2.0) &  # 接近均线
            (df['price_deviation'] < 0)  # 但未突破
        )
        
        # 卖3：股价突破均线，但持续时间不长，再次跌破均线
        df['sell_3'] = (
            (df['ma_direction'] == -1) &  # 均线下行
            (df['close'] < df['ma']) &  # 价格再次跌破均线
            (df['close'].shift(1) > df['ma'].shift(1)) &  # 前期在均线上方
            (df['close'].shift(2) < df['ma'].shift(2))  # 前前期在均线下方（短暂突破）
        )
        
        # 卖4：股价和均线都上涨，但股价严重偏离均线
        df['sell_4'] = (
            (df['ma_direction'] == 1) &  # 均线上行
            (df['close'] > df['ma']) &  # 价格在均线上方
            (df['price_deviation'] > self.get_param('deviation_threshold', 3.0))  # 严重偏离
        )
        
        return df
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成Granville策略信号"""
        df = df.copy()
        
        ma_period = self.get_param('ma_period', 20)
        ma_type = self.get_param('ma_type', 'sma')
        direction_window = self.get_param('direction_window', 5)
        volume_confirm = self.get_param('volume_confirm', True)
        volume_threshold = self.get_param('volume_threshold', 1.2)
        
        # 计算移动平均线
        if ma_type == 'ema':
            df['ma'] = talib.EMA(df['close'].values, timeperiod=ma_period)
        else:
            df['ma'] = talib.SMA(df['close'].values, timeperiod=ma_period)
        
        # 计算均线方向
        df['ma_direction'] = self.calculate_ma_direction(df['ma'], direction_window)
        
        # 计算价格偏离度
        df['price_deviation'] = self.calculate_price_deviation(df['close'], df['ma'])
        
        # 检查买入信号
        df = self.check_granville_buy_signals(df)
        
        # 检查卖出信号
        df = self.check_granville_sell_signals(df)
        
        # 生成最终信号
        df['granville_signal'] = 0
        
        # 买入信号
        buy_signal = df['buy_1'] | df['buy_2'] | df['buy_3'] | df['buy_4']
        df.loc[buy_signal, 'granville_signal'] = 1
        
        # 卖出信号
        sell_signal = df['sell_1'] | df['sell_2'] | df['sell_3'] | df['sell_4']
        df.loc[sell_signal, 'granville_signal'] = -1
        
        # 成交量确认
        if volume_confirm:
            df['volume_ma'] = talib.SMA(df['volume'].values, timeperiod=20)
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            # 只有在成交量放大时才确认信号
            volume_filter = df['volume_ratio'] >= volume_threshold
            df.loc[~volume_filter, 'granville_signal'] = 0
        
        # 添加信号详情
        df['granville_buy_type'] = 0
        df.loc[df['buy_1'], 'granville_buy_type'] = 1
        df.loc[df['buy_2'], 'granville_buy_type'] = 2
        df.loc[df['buy_3'], 'granville_buy_type'] = 3
        df.loc[df['buy_4'], 'granville_buy_type'] = 4
        
        df['granville_sell_type'] = 0
        df.loc[df['sell_1'], 'granville_sell_type'] = 1
        df.loc[df['sell_2'], 'granville_sell_type'] = 2
        df.loc[df['sell_3'], 'granville_sell_type'] = 3
        df.loc[df['sell_4'], 'granville_sell_type'] = 4
        
        return df

class RSIMomentumStrategy(BaseStrategy):
    """RSI动量策略"""
    
    def __init__(self, params: Dict[str, Any] = None):
        if params is None:
            params = {
                'rsi_period': 14,
                'rsi_overbought': 70,
                'rsi_oversold': 30,
                'rsi_middle': 50,
                'momentum_confirm': True
            }
        super().__init__("RSIMomentum", params)
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成RSI动量信号"""
        df = df.copy()
        
        rsi_period = self.get_param('rsi_period', 14)
        rsi_overbought = self.get_param('rsi_overbought', 70)
        rsi_oversold = self.get_param('rsi_oversold', 30)
        rsi_middle = self.get_param('rsi_middle', 50)
        momentum_confirm = self.get_param('momentum_confirm', True)
        
        # 计算RSI
        df['rsi'] = talib.RSI(df['close'].values, timeperiod=rsi_period)
        
        # RSI信号
        df['rsi_signal'] = 0
        df.loc[df['rsi'] > rsi_middle, 'rsi_signal'] = 1  # 做多
        df.loc[df['rsi'] < rsi_middle, 'rsi_signal'] = -1  # 做空
        
        # 超买超卖信号
        df['rsi_overbought_signal'] = df['rsi'] > rsi_overbought
        df['rsi_oversold_signal'] = df['rsi'] < rsi_oversold
        
        # 动量确认
        if momentum_confirm:
            df['momentum'] = talib.MOM(df['close'].values, timeperiod=10)
            df['momentum_positive'] = df['momentum'] > 0
            df['momentum_negative'] = df['momentum'] < 0
            
            # 结合动量信号
            df.loc[~df['momentum_positive'], 'rsi_signal'] = np.where(
                df.loc[~df['momentum_positive'], 'rsi_signal'] == 1, 0, 
                df.loc[~df['momentum_positive'], 'rsi_signal']
            )
            df.loc[~df['momentum_negative'], 'rsi_signal'] = np.where(
                df.loc[~df['momentum_negative'], 'rsi_signal'] == -1, 0, 
                df.loc[~df['momentum_negative'], 'rsi_signal']
            )
        
        return df

class BreakoutStrategy(BaseStrategy):
    """突破策略"""
    
    def __init__(self, params: Dict[str, Any] = None):
        if params is None:
            params = {
                'lookback_period': 20,
                'breakout_threshold': 0.02,  # 2%突破
                'volume_confirm': True,
                'volume_multiplier': 2.0,
                'atr_period': 14
            }
        super().__init__("Breakout", params)
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成突破信号"""
        df = df.copy()
        
        lookback_period = self.get_param('lookback_period', 20)
        breakout_threshold = self.get_param('breakout_threshold', 0.02)
        volume_confirm = self.get_param('volume_confirm', True)
        volume_multiplier = self.get_param('volume_multiplier', 2.0)
        atr_period = self.get_param('atr_period', 14)
        
        # 计算支撑阻力位
        df['resistance'] = df['high'].rolling(window=lookback_period).max()
        df['support'] = df['low'].rolling(window=lookback_period).min()
        
        # 计算ATR用于动态阈值
        df['atr'] = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=atr_period)
        df['dynamic_threshold'] = df['atr'] / df['close']
        
        # 使用动态阈值或固定阈值
        df['threshold'] = np.maximum(df['dynamic_threshold'], breakout_threshold)
        
        # 突破信号
        df['upward_breakout'] = (df['close'] > df['resistance'].shift(1) * (1 + df['threshold']))
        df['downward_breakout'] = (df['close'] < df['support'].shift(1) * (1 - df['threshold']))
        
        # 成交量确认
        if volume_confirm:
            df['avg_volume'] = df['volume'].rolling(window=20).mean()
            df['volume_spike'] = df['volume'] > df['avg_volume'] * volume_multiplier
            
            df['upward_breakout'] = df['upward_breakout'] & df['volume_spike']
            df['downward_breakout'] = df['downward_breakout'] & df['volume_spike']
        
        # 生成信号
        df['breakout_signal'] = 0
        df.loc[df['upward_breakout'], 'breakout_signal'] = 1
        df.loc[df['downward_breakout'], 'breakout_signal'] = -1
        
        return df

class MACDStrategy(BaseStrategy):
    """MACD策略"""
    
    def __init__(self, params: Dict[str, Any] = None):
        if params is None:
            params = {
                'fast_period': 12,
                'slow_period': 26,
                'signal_period': 9,
                'histogram_threshold': 0,
                'price_confirm': True
            }
        super().__init__("MACD", params)
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成MACD信号"""
        df = df.copy()
        
        fast_period = self.get_param('fast_period', 12)
        slow_period = self.get_param('slow_period', 26)
        signal_period = self.get_param('signal_period', 9)
        histogram_threshold = self.get_param('histogram_threshold', 0)
        price_confirm = self.get_param('price_confirm', True)
        
        # 计算MACD
        df['macd'], df['macd_signal'], df['macd_histogram'] = talib.MACD(
            df['close'].values, 
            fastperiod=fast_period, 
            slowperiod=slow_period, 
            signalperiod=signal_period
        )
        
        # MACD信号
        df['macd_cross'] = 0
        df.loc[df['macd'] > df['macd_signal'], 'macd_cross'] = 1
        df.loc[df['macd'] < df['macd_signal'], 'macd_cross'] = -1
        
        # 金叉死叉
        df['macd_cross_change'] = df['macd_cross'].diff()
        df['macd_golden_cross'] = (df['macd_cross_change'] == 2) & (df['macd_cross'] == 1)
        df['macd_death_cross'] = (df['macd_cross_change'] == -2) & (df['macd_cross'] == -1)
        
        # 直方图信号
        df['macd_histogram_signal'] = 0
        df.loc[df['macd_histogram'] > histogram_threshold, 'macd_histogram_signal'] = 1
        df.loc[df['macd_histogram'] < -histogram_threshold, 'macd_histogram_signal'] = -1
        
        # 价格确认
        if price_confirm:
            df['price_trend'] = 0
            df.loc[df['close'] > df['close'].shift(1), 'price_trend'] = 1
            df.loc[df['close'] < df['close'].shift(1), 'price_trend'] = -1
            
            # 只有价格趋势一致时才确认信号
            df.loc[df['price_trend'] != 1, 'macd_golden_cross'] = False
            df.loc[df['price_trend'] != -1, 'macd_death_cross'] = False
        
        return df

class CompositeStrategy(BaseStrategy):
    """组合策略"""
    
    def __init__(self, strategies: List[BaseStrategy], params: Dict[str, Any] = None):
        if params is None:
            params = {
                'signal_threshold': 0.6,  # 信号强度阈值
                'weight_method': 'equal',  # 'equal' or 'weighted'
                'strategy_weights': None
            }
        super().__init__("Composite", params)
        self.strategies = strategies
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成组合信号"""
        df = df.copy()
        
        signal_threshold = self.get_param('signal_threshold', 0.6)
        weight_method = self.get_param('weight_method', 'equal')
        strategy_weights = self.get_param('strategy_weights', None)
        
        # 收集所有策略的信号
        all_signals = []
        signal_names = []
        
        for i, strategy in enumerate(self.strategies):
            strategy_df = strategy.generate_signals(df)
            
            # 提取主要信号
            if hasattr(strategy, 'get_main_signal'):
                signal = strategy.get_main_signal(strategy_df)
            else:
                # 默认提取逻辑
                if 'ma_signal' in strategy_df.columns:
                    signal = strategy_df['ma_signal']
                elif 'rsi_signal' in strategy_df.columns:
                    signal = strategy_df['rsi_signal']
                elif 'breakout_signal' in strategy_df.columns:
                    signal = strategy_df['breakout_signal']
                elif 'macd_cross' in strategy_df.columns:
                    signal = strategy_df['macd_cross']
                else:
                    signal = pd.Series(0, index=df.index)
            
            all_signals.append(signal)
            signal_names.append(f'{strategy.name}_signal')
            
            # 保存策略信号到主DataFrame
            df[signal_names[-1]] = signal
        
        # 计算权重
        if weight_method == 'equal':
            weights = [1/len(self.strategies)] * len(self.strategies)
        else:
            weights = strategy_weights or [1/len(self.strategies)] * len(self.strategies)
        
        # 计算加权信号
        df['composite_signal_raw'] = 0
        for signal, weight in zip(all_signals, weights):
            df['composite_signal_raw'] += signal * weight
        
        # 生成最终信号
        df['composite_signal'] = 0
        df.loc[df['composite_signal_raw'] > signal_threshold, 'composite_signal'] = 1
        df.loc[df['composite_signal_raw'] < -signal_threshold, 'composite_signal'] = -1
        
        # 计算信号强度
        df['signal_strength'] = abs(df['composite_signal_raw'])
        
        return df

class MomentumStrategyManager:
    """动量策略管理器"""
    
    def __init__(self):
        self.strategies = {}
        self.results = {}
    
    def add_strategy(self, name: str, strategy: BaseStrategy):
        """添加策略"""
        self.strategies[name] = strategy
    
    def remove_strategy(self, name: str):
        """移除策略"""
        if name in self.strategies:
            del self.strategies[name]
    
    def run_strategy(self, name: str, df: pd.DataFrame) -> pd.DataFrame:
        """运行单个策略"""
        if name not in self.strategies:
            raise ValueError(f"策略 '{name}' 不存在")
        
        strategy = self.strategies[name]
        result = strategy.generate_signals(df)
        self.results[name] = result
        
        return result
    
    def run_all_strategies(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """运行所有策略"""
        results = {}
        
        for name, strategy in self.strategies.items():
            try:
                result = strategy.generate_signals(df)
                results[name] = result
                self.results[name] = result
                print(f"策略 '{name}' 运行成功")
            except Exception as e:
                print(f"策略 '{name}' 运行失败: {str(e)}")
        
        return results
    
    def get_strategy_summary(self) -> Dict[str, Dict]:
        """获取策略摘要"""
        summary = {}
        
        for name, strategy in self.strategies.items():
            summary[name] = {
                'type': strategy.__class__.__name__,
                'parameters': strategy.params
            }
        
        return summary
    
    def create_default_strategies(self) -> None:
        """创建默认策略组合"""
        # 双均线策略
        ma_strategy = DualMovingAverageStrategy({
            'fast_period': 9,
            'slow_period': 21,
            'signal_type': 'ema',
            'volume_confirm': True,
            'volume_threshold': 1.5
        })
        self.add_strategy('dual_ma', ma_strategy)
        
        # Granville八大法则策略
        granville_strategy = GranvilleStrategy({
            'ma_period': 20,
            'ma_type': 'sma',
            'direction_window': 5,
            'deviation_threshold': 3.0,
            'volume_confirm': True,
            'volume_threshold': 1.2
        })
        self.add_strategy('granville', granville_strategy)
        
        # RSI策略
        rsi_strategy = RSIMomentumStrategy({
            'rsi_period': 14,
            'rsi_overbought': 70,
            'rsi_oversold': 30,
            'rsi_middle': 50,
            'momentum_confirm': True
        })
        self.add_strategy('rsi_momentum', rsi_strategy)
        
        # 突破策略
        breakout_strategy = BreakoutStrategy({
            'lookback_period': 20,
            'breakout_threshold': 0.015,
            'volume_confirm': True,
            'volume_multiplier': 2.0,
            'atr_period': 14
        })
        self.add_strategy('breakout', breakout_strategy)
        
        # MACD策略
        macd_strategy = MACDStrategy({
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9,
            'histogram_threshold': 0,
            'price_confirm': True
        })
        self.add_strategy('macd', macd_strategy)
        
        # 组合策略
        composite_strategy = CompositeStrategy(
            [ma_strategy, granville_strategy, rsi_strategy, breakout_strategy, macd_strategy],
            {
                'signal_threshold': 0.5,
                'weight_method': 'equal'
            }
        )
        self.add_strategy('composite', composite_strategy)

if __name__ == "__main__":
    # 测试动量策略
    from doge_data_loader import DogeDataLoader
    
    try:
        # 加载数据
        loader = DogeDataLoader()
        df = loader.load_processed_data()
        
        print(f"数据加载成功，共 {len(df)} 条记录")
        
        # 创建策略管理器
        manager = MomentumStrategyManager()
        manager.create_default_strategies()
        
        # 运行所有策略
        results = manager.run_all_strategies(df)
        
        # 打印策略摘要
        summary = manager.get_strategy_summary()
        print("\n策略摘要:")
        for name, info in summary.items():
            print(f"  {name}: {info['type']}")
        
        # 分析信号统计
        print("\n信号统计:")
        for name, result in results.items():
            if name == 'composite':
                signal_col = 'composite_signal'
            elif name == 'dual_ma':
                signal_col = 'ma_signal'
            elif name == 'granville':
                signal_col = 'granville_signal'
            elif name == 'rsi_momentum':
                signal_col = 'rsi_signal'
            elif name == 'breakout':
                signal_col = 'breakout_signal'
            elif name == 'macd':
                signal_col = 'macd_cross'
            else:
                continue
            
            if signal_col in result.columns:
                signals = result[signal_col].dropna()
                long_signals = (signals == 1).sum()
                short_signals = (signals == -1).sum()
                neutral_signals = (signals == 0).sum()
                
                print(f"  {name}:")
                print(f"    做多信号: {long_signals}")
                print(f"    做空信号: {short_signals}")
                print(f"    中性信号: {neutral_signals}")
                
                # 如果是Granville策略，显示详细信号类型
                if name == 'granville':
                    if 'granville_buy_type' in result.columns:
                        for buy_type in range(1, 5):
                            count = (result['granville_buy_type'] == buy_type).sum()
                            print(f"      买入{buy_type}信号: {count}")
                    if 'granville_sell_type' in result.columns:
                        for sell_type in range(1, 5):
                            count = (result['granville_sell_type'] == sell_type).sum()
                            print(f"      卖出{sell_type}信号: {count}")
        
        print("\n策略测试完成!")
        
    except Exception as e:
        print(f"策略测试失败: {str(e)}")
        raise