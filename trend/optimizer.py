import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Union
from itertools import product
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from sklearn.model_selection import ParameterGrid
import time
import json
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from backtest_engine import BacktestEngine
from momentum_strategy import (
    MomentumStrategyManager, 
    DualMovingAverageStrategy, 
    RSIMomentumStrategy, 
    BreakoutStrategy, 
    MACDStrategy,
    CompositeStrategy
)

@dataclass
class OptimizationResult:
    """优化结果"""
    params: Dict[str, Any]
    metrics: Dict[str, float]
    score: float
    strategy_name: str
    backtest_results: Dict[str, Any]

class ObjectiveFunction:
    """目标函数"""
    
    @staticmethod
    def sharpe_ratio(metrics: Dict[str, float]) -> float:
        """夏普比率目标函数"""
        return metrics.get('sharpe_ratio', -10)
    
    @staticmethod
    def total_return(metrics: Dict[str, float]) -> float:
        """总收益率目标函数"""
        return metrics.get('total_return_pct', -100)
    
    @staticmethod
    def calmar_ratio(metrics: Dict[str, float]) -> float:
        """卡玛比率目标函数"""
        max_drawdown = metrics.get('max_drawdown', 100)
        total_return = metrics.get('total_return_pct', -100)
        if max_drawdown <= 0:
            return -100
        return total_return / max_drawdown
    
    @staticmethod
    def composite_score(metrics: Dict[str, float]) -> float:
        """综合评分目标函数"""
        sharpe = metrics.get('sharpe_ratio', -10)
        total_return = metrics.get('total_return_pct', -100)
        max_drawdown = metrics.get('max_drawdown', 100)
        win_rate = metrics.get('win_rate', 0)
        profit_factor = metrics.get('profit_factor', 0)
        
        # 综合评分权重
        score = (
            sharpe * 0.3 +
            total_return * 0.002 +  # 缩放到合适范围
            max(-max_drawdown, -50) * 0.02 +  # 最大回撤惩罚
            win_rate * 0.01 +
            min(profit_factor, 5) * 0.2  # 限制盈亏比上限
        )
        
        return score

def run_single_optimization(args: Tuple) -> OptimizationResult:
    """运行单次优化（用于多进程）"""
    try:
        df, strategy_name, strategy_params, backtest_params, objective_func = args
        
        # 创建策略管理器
        manager = MomentumStrategyManager()
        
        # 根据策略名称创建策略
        if strategy_name == 'dual_ma':
            strategy = DualMovingAverageStrategy(strategy_params)
            manager.add_strategy('dual_ma', strategy)
            signal_results = manager.run_strategy('dual_ma', df)
            signals = signal_results['ma_signal']
        elif strategy_name == 'rsi_momentum':
            strategy = RSIMomentumStrategy(strategy_params)
            manager.add_strategy('rsi_momentum', strategy)
            signal_results = manager.run_strategy('rsi_momentum', df)
            signals = signal_results['rsi_signal']
        elif strategy_name == 'breakout':
            strategy = BreakoutStrategy(strategy_params)
            manager.add_strategy('breakout', strategy)
            signal_results = manager.run_strategy('breakout', df)
            signals = signal_results['breakout_signal']
        elif strategy_name == 'macd':
            strategy = MACDStrategy(strategy_params)
            manager.add_strategy('macd', strategy)
            signal_results = manager.run_strategy('macd', df)
            signals = signal_results['macd_cross']
        else:
            raise ValueError(f"未知的策略名称: {strategy_name}")
        
        # 创建回测引擎
        stop_loss_pct = backtest_params.pop('stop_loss_pct', None)
        take_profit_pct = backtest_params.pop('take_profit_pct', None)
        
        engine = BacktestEngine(**backtest_params)
        
        # 设置风险管理参数
        if stop_loss_pct is not None or take_profit_pct is not None:
            engine.set_risk_management(stop_loss_pct, take_profit_pct)
        
        # 运行回测
        results = engine.run_backtest(df, signals)
        
        # 计算目标函数值
        score = objective_func(results)
        
        return OptimizationResult(
            params=strategy_params,
            metrics=results,
            score=score,
            strategy_name=strategy_name,
            backtest_results=results
        )
        
    except Exception as e:
        print(f"优化过程中出错: {str(e)}")
        return OptimizationResult(
            params=strategy_params,
            metrics={},
            score=-1000,
            strategy_name=strategy_name,
            backtest_results={}
        )

class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, 
                 objective_function: str = 'composite_score',
                 n_jobs: int = None,
                 verbose: bool = True):
        
        self.objective_function = getattr(ObjectiveFunction, objective_function)
        self.n_jobs = n_jobs or max(1, mp.cpu_count() - 1)
        self.verbose = verbose
        self.optimization_results: List[OptimizationResult] = []
    
    def get_default_param_grids(self) -> Dict[str, Dict[str, List]]:
        """获取默认参数网格"""
        return {
            'dual_ma': {
                'fast_period': [5, 9, 12, 15],
                'slow_period': [20, 21, 26, 30],
                'signal_type': ['ema', 'sma'],
                'volume_confirm': [True, False],
                'volume_threshold': [1.2, 1.5, 2.0]
            },
            'rsi_momentum': {
                'rsi_period': [7, 14, 21],
                'rsi_overbought': [65, 70, 75, 80],
                'rsi_oversold': [20, 25, 30, 35],
                'rsi_middle': [45, 50, 55],
                'momentum_confirm': [True, False]
            },
            'breakout': {
                'lookback_period': [10, 15, 20, 30],
                'breakout_threshold': [0.01, 0.015, 0.02, 0.025],
                'volume_confirm': [True, False],
                'volume_multiplier': [1.5, 2.0, 2.5, 3.0],
                'atr_period': [10, 14, 20]
            },
            'macd': {
                'fast_period': [8, 12, 16],
                'slow_period': [21, 26, 30],
                'signal_period': [6, 9, 12],
                'histogram_threshold': [0, 0.001, 0.002],
                'price_confirm': [True, False]
            }
        }
    
    def get_default_backtest_params(self) -> Dict[str, Any]:
        """获取默认回测参数"""
        return {
            'initial_capital': 100000,
            'commission_rate': 0.001,
            'slippage': 0.0001,
            'position_size_type': 'percent',
            'position_size': 0.1,
            'enable_short': True,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.10
        }
    
    def optimize_single_strategy(self, 
                                df: pd.DataFrame,
                                strategy_name: str,
                                param_grid: Dict[str, List] = None,
                                backtest_params: Dict[str, Any] = None,
                                max_combinations: int = 1000) -> List[OptimizationResult]:
        """优化单个策略"""
        
        if param_grid is None:
            param_grid = self.get_default_param_grids()[strategy_name]
        
        if backtest_params is None:
            backtest_params = self.get_default_backtest_params()
        
        # 生成参数组合
        param_combinations = list(ParameterGrid(param_grid))
        
        # 限制组合数量
        if len(param_combinations) > max_combinations:
            np.random.shuffle(param_combinations)
            param_combinations = param_combinations[:max_combinations]
        
        if self.verbose:
            print(f"优化策略 '{strategy_name}'，参数组合数量: {len(param_combinations)}")
        
        # 准备多进程参数
        args_list = [
            (df, strategy_name, params, backtest_params, self.objective_function)
            for params in param_combinations
        ]
        
        # 多进程优化
        results = []
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
            # 提交任务
            future_to_params = {
                executor.submit(run_single_optimization, args): args[2]
                for args in args_list
            }
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_params):
                try:
                    result = future.result()
                    results.append(result)
                    completed += 1
                    
                    if self.verbose and completed % 10 == 0:
                        elapsed = time.time() - start_time
                        print(f"已完成 {completed}/{len(param_combinations)} 组合 "
                              f"({completed/len(param_combinations)*100:.1f}%), "
                              f"耗时: {elapsed:.1f}秒")
                        
                except Exception as e:
                    print(f"优化任务失败: {str(e)}")
        
        # 按评分排序
        results.sort(key=lambda x: x.score, reverse=True)
        
        # 保存结果
        self.optimization_results.extend(results)
        
        if self.verbose:
            total_time = time.time() - start_time
            print(f"策略 '{strategy_name}' 优化完成，总耗时: {total_time:.1f}秒")
            if results:
                best_result = results[0]
                print(f"最优参数: {best_result.params}")
                print(f"最优评分: {best_result.score:.4f}")
                print(f"总收益率: {best_result.metrics.get('total_return_pct', 0):.2f}%")
                print(f"夏普比率: {best_result.metrics.get('sharpe_ratio', 0):.4f}")
                print(f"最大回撤: {best_result.metrics.get('max_drawdown', 0):.2f}%")
        
        return results
    
    def optimize_multiple_strategies(self, 
                                   df: pd.DataFrame,
                                   strategies: List[str] = None,
                                   param_grids: Dict[str, Dict[str, List]] = None,
                                   backtest_params: Dict[str, Any] = None,
                                   max_combinations_per_strategy: int = 200) -> Dict[str, List[OptimizationResult]]:
        """优化多个策略"""
        
        if strategies is None:
            strategies = ['dual_ma', 'rsi_momentum', 'breakout', 'macd']
        
        if param_grids is None:
            param_grids = self.get_default_param_grids()
        
        all_results = {}
        
        for strategy_name in strategies:
            if self.verbose:
                print(f"\n开始优化策略: {strategy_name}")
            
            strategy_param_grid = param_grids.get(strategy_name, {})
            results = self.optimize_single_strategy(
                df=df,
                strategy_name=strategy_name,
                param_grid=strategy_param_grid,
                backtest_params=backtest_params,
                max_combinations=max_combinations_per_strategy
            )
            
            all_results[strategy_name] = results
        
        return all_results
    
    def walk_forward_optimization(self, 
                                df: pd.DataFrame,
                                strategy_name: str,
                                param_grid: Dict[str, List] = None,
                                backtest_params: Dict[str, Any] = None,
                                train_period: int = 30,  # 训练期天数
                                test_period: int = 7,    # 测试期天数
                                step_size: int = 1) -> List[Dict[str, Any]]:
        """步进式优化"""
        
        if param_grid is None:
            param_grid = self.get_default_param_grids()[strategy_name]
        
        if backtest_params is None:
            backtest_params = self.get_default_backtest_params()
        
        # 将天数转换为分钟数（假设1天=1440分钟）
        train_size = train_period * 1440
        test_size = test_period * 1440
        step_size_minutes = step_size * 1440
        
        results = []
        
        for start_idx in range(0, len(df) - train_size - test_size, step_size_minutes):
            # 训练集
            train_df = df.iloc[start_idx:start_idx + train_size].copy()
            
            # 测试集
            test_df = df.iloc[start_idx + train_size:start_idx + train_size + test_size].copy()
            
            if self.verbose:
                print(f"训练期: {train_df.iloc[0]['datetime']} - {train_df.iloc[-1]['datetime']}")
                print(f"测试期: {test_df.iloc[0]['datetime']} - {test_df.iloc[-1]['datetime']}")
            
            # 在训练集上优化参数
            train_results = self.optimize_single_strategy(
                df=train_df,
                strategy_name=strategy_name,
                param_grid=param_grid,
                backtest_params=backtest_params,
                max_combinations=50  # 限制组合数量以加快速度
            )
            
            if not train_results:
                continue
            
            # 使用最优参数在测试集上测试
            best_params = train_results[0].params
            
            # 在测试集上运行回测
            test_args = (test_df, strategy_name, best_params, backtest_params, self.objective_function)
            test_result = run_single_optimization(test_args)
            
            results.append({
                'train_start': train_df.iloc[0]['datetime'],
                'train_end': train_df.iloc[-1]['datetime'],
                'test_start': test_df.iloc[0]['datetime'],
                'test_end': test_df.iloc[-1]['datetime'],
                'best_params': best_params,
                'train_score': train_results[0].score,
                'test_score': test_result.score,
                'test_metrics': test_result.metrics
            })
            
            if self.verbose:
                print(f"训练评分: {train_results[0].score:.4f}")
                print(f"测试评分: {test_result.score:.4f}")
                print("=" * 50)
        
        return results
    
    def save_results(self, filepath: str):
        """保存优化结果"""
        results_data = []
        for result in self.optimization_results:
            results_data.append({
                'strategy_name': result.strategy_name,
                'params': result.params,
                'score': result.score,
                'metrics': result.metrics
            })
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        print(f"优化结果已保存到: {filepath}")
    
    def load_results(self, filepath: str):
        """加载优化结果"""
        with open(filepath, 'r', encoding='utf-8') as f:
            results_data = json.load(f)
        
        self.optimization_results = []
        for data in results_data:
            result = OptimizationResult(
                params=data['params'],
                metrics=data['metrics'],
                score=data['score'],
                strategy_name=data['strategy_name'],
                backtest_results=data['metrics']
            )
            self.optimization_results.append(result)
        
        print(f"从 {filepath} 加载了 {len(self.optimization_results)} 个优化结果")
    
    def get_best_params(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """获取策略的最优参数"""
        strategy_results = [r for r in self.optimization_results if r.strategy_name == strategy_name]
        if not strategy_results:
            return None
        
        best_result = max(strategy_results, key=lambda x: x.score)
        return best_result.params
    
    def get_summary(self) -> pd.DataFrame:
        """获取优化结果摘要"""
        if not self.optimization_results:
            return pd.DataFrame()
        
        summary_data = []
        for result in self.optimization_results:
            summary_data.append({
                'strategy': result.strategy_name,
                'score': result.score,
                'total_return_pct': result.metrics.get('total_return_pct', 0),
                'sharpe_ratio': result.metrics.get('sharpe_ratio', 0),
                'max_drawdown': result.metrics.get('max_drawdown', 0),
                'win_rate': result.metrics.get('win_rate', 0),
                'total_trades': result.metrics.get('total_trades', 0)
            })
        
        return pd.DataFrame(summary_data)

if __name__ == "__main__":
    # 测试参数优化器
    from doge_data_loader import DogeDataLoader
    
    try:
        # 加载数据
        loader = DogeDataLoader()
        df = loader.load_processed_data()
        
        # 使用小部分数据进行测试
        test_df = df.iloc[:10000].copy()  # 使用前10000条数据
        
        print(f"使用测试数据，长度: {len(test_df)}")
        
        # 创建优化器
        optimizer = ParameterOptimizer(
            objective_function='composite_score',
            n_jobs=2,  # 使用较少的进程数进行测试
            verbose=True
        )
        
        # 优化双均线策略
        dual_ma_results = optimizer.optimize_single_strategy(
            df=test_df,
            strategy_name='dual_ma',
            max_combinations=20  # 限制组合数量以加快测试速度
        )
        
        # 打印最优结果
        if dual_ma_results:
            best_result = dual_ma_results[0]
            print(f"\n最优双均线策略参数:")
            print(f"参数: {best_result.params}")
            print(f"评分: {best_result.score:.4f}")
            print(f"总收益率: {best_result.metrics.get('total_return_pct', 0):.2f}%")
            print(f"夏普比率: {best_result.metrics.get('sharpe_ratio', 0):.4f}")
            print(f"最大回撤: {best_result.metrics.get('max_drawdown', 0):.2f}%")
        
        # 获取摘要
        summary = optimizer.get_summary()
        print(f"\n优化结果摘要:")
        print(summary.head())
        
        print("\n参数优化器测试完成!")
        
    except Exception as e:
        print(f"参数优化器测试失败: {str(e)}")
        raise