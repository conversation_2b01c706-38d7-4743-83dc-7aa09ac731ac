#!/usr/bin/env python3
"""
Granville八大法则策略测试和验证脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入系统模块
from doge_data_loader import DogeDataLoader
from momentum_strategy import GranvilleStrategy, DualMovingAverageStrategy
from backtest_engine import BacktestEngine
from analysis import BacktestAnalyzer

def test_granville_signals():
    """测试Granville策略信号生成"""
    print("="*60)
    print("测试Granville策略信号生成")
    print("="*60)
    
    # 加载数据
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    
    # 使用部分数据进行测试
    test_df = df.iloc[:5000].copy()
    print(f"使用测试数据: {len(test_df)} 条记录")
    
    # 创建Granville策略
    granville_strategy = GranvilleStrategy({
        'ma_period': 20,
        'ma_type': 'sma',
        'direction_window': 5,
        'deviation_threshold': 3.0,
        'volume_confirm': True,
        'volume_threshold': 1.2
    })
    
    # 生成信号
    print("\n生成Granville信号...")
    result = granville_strategy.generate_signals(test_df)
    
    # 分析信号统计
    print("\n信号统计:")
    signals = result['granville_signal'].dropna()
    long_signals = (signals == 1).sum()
    short_signals = (signals == -1).sum()
    neutral_signals = (signals == 0).sum()
    
    print(f"总信号数: {len(signals)}")
    print(f"做多信号: {long_signals} ({long_signals/len(signals)*100:.2f}%)")
    print(f"做空信号: {short_signals} ({short_signals/len(signals)*100:.2f}%)")
    print(f"中性信号: {neutral_signals} ({neutral_signals/len(signals)*100:.2f}%)")
    
    # 分析各种买卖信号类型
    print("\n详细信号类型分析:")
    for buy_type in range(1, 5):
        count = (result['granville_buy_type'] == buy_type).sum()
        print(f"买入{buy_type}信号 (买{buy_type}): {count}")
    
    for sell_type in range(1, 5):
        count = (result['granville_sell_type'] == sell_type).sum()
        print(f"卖出{sell_type}信号 (卖{sell_type}): {count}")
    
    # 分析价格偏离度分布
    print("\n价格偏离度分析:")
    deviation = result['price_deviation'].dropna()
    print(f"平均偏离度: {deviation.mean():.2f}%")
    print(f"偏离度标准差: {deviation.std():.2f}%")
    print(f"最大正偏离: {deviation.max():.2f}%")
    print(f"最大负偏离: {deviation.min():.2f}%")
    
    return result

def compare_strategies():
    """比较Granville策略和传统双均线策略"""
    print("\n" + "="*60)
    print("策略比较：Granville vs 传统双均线")
    print("="*60)
    
    # 加载数据
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    test_df = df.iloc[:8000].copy()
    
    # 创建策略
    granville_strategy = GranvilleStrategy({
        'ma_period': 20,
        'ma_type': 'sma',
        'direction_window': 5,
        'deviation_threshold': 3.0,
        'volume_confirm': False  # 关闭成交量确认以便比较
    })
    
    dual_ma_strategy = DualMovingAverageStrategy({
        'fast_period': 9,
        'slow_period': 21,
        'signal_type': 'ema',
        'volume_confirm': False
    })
    
    # 生成信号
    granville_result = granville_strategy.generate_signals(test_df)
    dual_ma_result = dual_ma_strategy.generate_signals(test_df)
    
    # 回测比较
    strategies = {
        'Granville': (granville_result['granville_signal'], granville_result),
        'DualMA': (dual_ma_result['ma_signal'], dual_ma_result)
    }
    
    backtest_results = {}
    
    for strategy_name, (signals, strategy_result) in strategies.items():
        print(f"\n回测策略: {strategy_name}")
        
        # 创建回测引擎
        engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            position_size_type='percent',
            position_size=0.08
        )
        
        # 运行回测
        results = engine.run_backtest(test_df, signals)
        backtest_results[strategy_name] = results
        
        print(f"  总收益率: {results['total_return_pct']:.2f}%")
        print(f"  夏普比率: {results['sharpe_ratio']:.4f}")
        print(f"  最大回撤: {results['max_drawdown']:.2f}%")
        print(f"  胜率: {results['win_rate']:.2f}%")
        print(f"  交易次数: {results['total_trades']}")
    
    # 详细比较
    print("\n" + "-"*50)
    print("详细对比结果:")
    print("-"*50)
    print(f"{'指标':<15} {'Granville':<15} {'DualMA':<15} {'差异':<15}")
    print("-"*50)
    
    metrics = ['total_return_pct', 'sharpe_ratio', 'max_drawdown', 'win_rate', 'total_trades']
    for metric in metrics:
        granville_val = backtest_results['Granville'].get(metric, 0)
        dual_ma_val = backtest_results['DualMA'].get(metric, 0)
        diff = granville_val - dual_ma_val
        
        print(f"{metric:<15} {granville_val:<15.2f} {dual_ma_val:<15.2f} {diff:<15.2f}")
    
    return backtest_results

def test_granville_rules():
    """测试Granville八大法则的具体实现"""
    print("\n" + "="*60)
    print("测试Granville八大法则具体实现")
    print("="*60)
    
    # 加载数据
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    test_df = df.iloc[:10000].copy()
    
    # 创建Granville策略
    granville_strategy = GranvilleStrategy({
        'ma_period': 20,
        'ma_type': 'sma',
        'direction_window': 5,
        'deviation_threshold': 3.0,
        'volume_confirm': False
    })
    
    # 生成信号
    result = granville_strategy.generate_signals(test_df)
    
    # 测试各个法则的触发条件
    print("\nGranville八大法则触发统计:")
    
    # 买入法则
    buy_rules = ['buy_1', 'buy_2', 'buy_3', 'buy_4']
    buy_descriptions = [
        "买1: 均线上行+价格上穿均线(黄金交叉)",
        "买2: 均线支撑+价格接近但未跌破均线", 
        "买3: 价格急跌后跌破均线",
        "买4: 价格严重偏离均线下方"
    ]
    
    for rule, desc in zip(buy_rules, buy_descriptions):
        if rule in result.columns:
            count = result[rule].sum()
            print(f"{desc}: {count}次")
    
    # 卖出法则
    sell_rules = ['sell_1', 'sell_2', 'sell_3', 'sell_4']
    sell_descriptions = [
        "卖1: 均线转为下行+价格跌破均线(死亡交叉)",
        "卖2: 均线阻力+价格上升但未突破均线",
        "卖3: 价格短暂突破均线后再次跌破",
        "卖4: 价格严重偏离均线上方"
    ]
    
    for rule, desc in zip(sell_rules, sell_descriptions):
        if rule in result.columns:
            count = result[rule].sum()
            print(f"{desc}: {count}次")
    
    # 分析均线方向统计
    print("\n均线方向统计:")
    ma_direction = result['ma_direction'].value_counts()
    print(f"上升: {ma_direction.get(1, 0)}次")
    print(f"下降: {ma_direction.get(-1, 0)}次")
    print(f"平稳: {ma_direction.get(0, 0)}次")
    
    # 分析价格偏离度的极端情况
    print("\n价格偏离度极端情况:")
    deviation_threshold = granville_strategy.get_param('deviation_threshold', 3.0)
    extreme_positive = (result['price_deviation'] > deviation_threshold).sum()
    extreme_negative = (result['price_deviation'] < -deviation_threshold).sum()
    
    print(f"超过+{deviation_threshold}%偏离: {extreme_positive}次")
    print(f"超过-{deviation_threshold}%偏离: {extreme_negative}次")
    
    return result

def analyze_granville_performance():
    """分析Granville策略的性能表现"""
    print("\n" + "="*60)
    print("Granville策略性能分析")
    print("="*60)
    
    # 加载数据
    loader = DogeDataLoader()
    df = loader.load_processed_data()
    test_df = df.iloc[:15000].copy()
    
    # 测试不同参数组合
    param_combinations = [
        {'ma_period': 15, 'deviation_threshold': 2.0},
        {'ma_period': 20, 'deviation_threshold': 3.0},
        {'ma_period': 25, 'deviation_threshold': 4.0},
        {'ma_period': 30, 'deviation_threshold': 5.0}
    ]
    
    results = {}
    
    for params in param_combinations:
        print(f"\n测试参数: MA期间={params['ma_period']}, 偏离阈值={params['deviation_threshold']}%")
        
        # 创建策略
        strategy = GranvilleStrategy({
            'ma_period': params['ma_period'],
            'ma_type': 'sma',
            'direction_window': 5,
            'deviation_threshold': params['deviation_threshold'],
            'volume_confirm': False
        })
        
        # 生成信号
        strategy_result = strategy.generate_signals(test_df)
        signals = strategy_result['granville_signal']
        
        # 回测
        engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            position_size_type='percent',
            position_size=0.08
        )
        
        backtest_result = engine.run_backtest(test_df, signals)
        results[f"MA{params['ma_period']}_DEV{params['deviation_threshold']}"] = backtest_result
        
        print(f"  总收益率: {backtest_result['total_return_pct']:.2f}%")
        print(f"  夏普比率: {backtest_result['sharpe_ratio']:.4f}")
        print(f"  最大回撤: {backtest_result['max_drawdown']:.2f}%")
        print(f"  胜率: {backtest_result['win_rate']:.2f}%")
        print(f"  交易次数: {backtest_result['total_trades']}")
    
    # 找出最佳参数
    best_params = None
    best_score = float('-inf')
    
    for param_name, result in results.items():
        # 使用综合评分
        score = (
            result['total_return_pct'] * 0.4 +
            result['sharpe_ratio'] * 10 +
            max(-result['max_drawdown'], -50) * 0.5 +
            result['win_rate'] * 0.2
        )
        
        if score > best_score:
            best_score = score
            best_params = param_name
    
    print(f"\n最佳参数组合: {best_params}")
    print(f"最佳综合评分: {best_score:.2f}")
    
    return results

def main():
    """主测试函数"""
    print("Granville八大法则策略完整测试")
    print("="*80)
    
    try:
        # 1. 测试信号生成
        signal_result = test_granville_signals()
        
        # 2. 策略比较
        comparison_result = compare_strategies()
        
        # 3. 测试法则实现
        rules_result = test_granville_rules()
        
        # 4. 性能分析
        performance_result = analyze_granville_performance()
        
        print("\n" + "="*80)
        print("所有测试完成！")
        print("="*80)
        
        print("\n测试总结:")
        print("✓ Granville策略信号生成正常")
        print("✓ 八大法则实现正确")
        print("✓ 策略回测功能正常")
        print("✓ 参数优化测试通过")
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()