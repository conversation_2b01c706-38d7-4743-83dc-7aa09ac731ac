#!/usr/bin/env python3
"""
简化的系统测试脚本
"""

import sys
import time
import pandas as pd
import numpy as np

# 导入所有模块
from doge_data_loader import DogeDataLoader
from momentum_strategy import MomentumStrategyManager, DualMovingAverageStrategy
from backtest_engine import BacktestEngine
from optimizer import ParameterOptimizer
from analysis import BacktestAnalyzer

def test_system():
    """测试系统功能"""
    print("开始系统测试...")
    
    try:
        # 1. 测试数据加载
        print("\n1. 测试数据加载...")
        loader = DogeDataLoader()
        df = loader.load_processed_data()
        print(f"✓ 数据加载成功，共 {len(df)} 条记录")
        
        # 使用小部分数据进行测试
        test_df = df.iloc[:5000].copy()
        print(f"✓ 使用测试数据，共 {len(test_df)} 条记录")
        
        # 2. 测试策略
        print("\n2. 测试策略...")
        manager = MomentumStrategyManager()
        strategy = DualMovingAverageStrategy({
            'fast_period': 9,
            'slow_period': 21,
            'signal_type': 'ema',
            'volume_confirm': False
        })
        manager.add_strategy('dual_ma', strategy)
        
        strategy_results = manager.run_strategy('dual_ma', test_df)
        signals = strategy_results['ma_signal']
        print(f"✓ 策略运行成功，生成 {len(signals)} 个信号")
        
        # 3. 测试回测引擎
        print("\n3. 测试回测引擎...")
        engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            position_size_type='percent',
            position_size=0.05  # 降低仓位以减少交易次数
        )
        
        results = engine.run_backtest(test_df, signals)
        print(f"✓ 回测完成，总收益率: {results['total_return_pct']:.2f}%")
        
        # 4. 测试分析模块
        print("\n4. 测试分析模块...")
        analyzer = BacktestAnalyzer()
        analyzer.print_performance_summary(results)
        
        portfolio_history = engine.get_portfolio_history()
        trade_history = engine.get_trade_history()
        
        print(f"✓ 分析模块正常，投资组合历史: {len(portfolio_history)} 条记录")
        print(f"✓ 交易历史: {len(trade_history)} 条记录")
        
        # 5. 测试优化器（简化版）
        print("\n5. 测试优化器...")
        optimizer = ParameterOptimizer(n_jobs=1, verbose=False)
        
        # 使用更小的参数网格
        simple_param_grid = {
            'fast_period': [5, 9],
            'slow_period': [20, 21],
            'signal_type': ['ema'],
            'volume_confirm': [False]
        }
        
        optimization_results = optimizer.optimize_single_strategy(
            df=test_df,
            strategy_name='dual_ma',
            param_grid=simple_param_grid,
            max_combinations=4
        )
        
        if optimization_results:
            best_result = optimization_results[0]
            print(f"✓ 优化完成，最优参数: {best_result.params}")
            print(f"✓ 最优评分: {best_result.score:.4f}")
        else:
            print("✗ 优化失败")
        
        print("\n" + "="*50)
        print("系统测试完成！所有模块工作正常")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 系统测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_system()
    sys.exit(0 if success else 1)